﻿using EngagetoEntities;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using Newtonsoft.Json.Linq;

namespace EngagetoContracts.TemplateContracts
{
    public interface ITemplate
    {

        Task<bool> IsTemplateExist(string businessId, string templateName);
        Task<string> UploadMediaFile(UploadFileDto file);
        Task<Template> CreateTemplateAsync(CreateTemplateDto model, bool Draft);
        Task<HttpResponseMessage> EditTemplateAsync(EditTemplateDto model);
        Task<HttpResponseMessage> SendTemplateAsync(SendTemplateDto model);
        Task<Template> AddTemplateAsync(CreateTemplateDto model, bool Draft, Guid currentUserId);
        Task<Template> UpdateTemplateAsync(EditTemplateDto editModel, Guid currentUserId);
        Task<object> GetAllTemplateFilterAsync(FilterOperationsDto Operations, string BusinessId, Guid UserId, int page, int per_page);
        List<Object> GetTemplateByIdAsync(List<Guid> ids, string businessId, Guid userId);
        Task MarkTemplateAsDeletedAsync(DeleteTemplateDto model);
        Task<bool> RestoredDeletedTemplate(RestoredTemplateDto model);
        Task<Conversations> GetSendTemplateAsync(string companyId, Guid businessId, TemplateRequestDto templateRequestDto);
        Task<JObject> GetWhatsAppSendTemplatePayloadAsync(string companyId, Guid? templateId, string? templateName, string contact, string? headerVariableValue, List<string>? bodyVariableValue);       
        Task<Conversations> SaveConversationAsync(Conversations conversation);
        Task<Template> SaveTemplateAsync(Template templateDto,Guid userId);   
        Task<List<Button>> SaveButtonsAsync(List<Button> buttons, Guid templateId);
        Task<List<object>> GetApprovedTemplates(string CompanyId,string? TemplateName);
        Task<bool> ProcessAuthTemplatePayloadAsync(AuthTemplateDto dto, string businessId, string userName, Guid currentUserId);
        Task<Conversations> SendAuthTemplateServiceAsync(string businessId, SendAuthTemplateDto sendAuthTemplateDto);
        Task<HttpResponseMessage> CreateCarouselTemplateAsync(CarouselTemplateDto model, bool Draft);
        Task<HttpResponseMessage> SendCarouselTemplateAsync(SendCarouselTemplateDto model);
        Task<HttpResponseMessage> SendAuthenticationTemplateAsync(SendAuthenticationTemplateDto model, string oneTimeOtp);
        Task<CreateTemplateDto> GetTemplateByIdAsync(Guid id);
        string SendCarouselCardsPayload(List<CarouselCardsDto> carouselCards, Template template, List<CarouselCardVariableDto> variables);
    }
}
