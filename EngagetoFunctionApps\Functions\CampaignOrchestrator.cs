﻿using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace EngagetoFunctionApps.Functions
{
    public class CampaignOrchestrator
    {
        private readonly ILogger<CampaignScheduler> _logger;
        private readonly ICampaignScheduler _campaignScheduler;
        private readonly EngagetoDapper.Data.Interfaces.GenericInterfaces.IGenericRepository _genericRepository;

        public CampaignOrchestrator(
            ILogger<CampaignScheduler> logger,
            ICampaignScheduler campaignScheduler,
            EngagetoDapper.Data.Interfaces.GenericInterfaces.IGenericRepository genericRepository)
        {
            _logger = logger;
            _campaignScheduler = campaignScheduler;
            _genericRepository = genericRepository;
        }

        [Function(nameof(RunCampaignOrchestrator))]
        public async Task RunCampaignOrchestrator(
        [OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<InputPayload>();

            try
            {
                _logger.LogInformation("Orchestrator started for CampaignId: {CampaignId}", input?.CampaignId);

                var campaignData = input.CampaignJsonData;
                var campaign = campaignData.Adapt<Campaign>();
                campaign.CampaignId = input.CampaignId;
                campaign.DateSetLive =  input.ScheduledTime ;
                var audience = input.Audiances;
                bool isDevelopment = input.IsDevelopment;

                InputPayload activityInput = new(
                    campaign.CampaignId,
                    campaignData,
                    input.ScheduledTime,
                    audience,
                    isDevelopment
                );

                _logger.LogInformation("Calling activity function: PartitionCampaignActivity for CampaignId: {CampaignId}", campaign.CampaignId);

                await context.CallActivityAsync(nameof(PartitionCampaignActivity), activityInput);

                _logger.LogInformation("Orchestration completed successfully for CampaignId: {CampaignId}", campaign.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in RunCampaignOrchestrator for CampaignId: {CampaignId}", input?.CampaignId);
                context.SetCustomStatus($"Error: {ex.Message}");
                throw;
            }
        }

        [Function(nameof(PartitionCampaignActivity))]
        public async Task PartitionCampaignActivity(
        [ActivityTrigger] InputPayload input)
        {
            _logger.LogInformation("PartitionCampaignActivity triggered for CampaignId: {CampaignId}", input.CampaignId);
            _logger.LogInformation("InputPayload: {Payload}", JsonConvert.SerializeObject(input));
            _logger.LogInformation("ScheduledTime: {ScheduledTime}", input.ScheduledTime);

            input.CampaignJsonData.CampaignId = input.CampaignId;
            input.CampaignJsonData.DateSetLive = input.ScheduledTime;

            try
            {
                await _campaignScheduler.PartitionCampaignBatchesAsync(input.CampaignJsonData, input.Audiances, input.IsDevelopment);
                _logger.LogInformation("PartitionCampaignActivity completed for CampaignId: {CampaignId}", input.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing PartitionCampaignActivity for CampaignId: {CampaignId}", input.CampaignId);
                throw;
            }
        }

        [Function(nameof(ProcessCampaignSubBatches))]
        public async Task ProcessCampaignSubBatches([OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<InputPayload>();
            _logger.LogInformation("ProcessCampaignSubBatches started for CampaignId: {CampaignId}", input.CampaignId);
            _logger.LogInformation("Input Payload: {Payload}", JsonConvert.SerializeObject(input));

            try
            {
                if (input.ScheduledTime > context.CurrentUtcDateTime)
                {
                    _logger.LogInformation("Waiting until scheduled time: {ScheduledTime}", input.ScheduledTime);
                    await context.CreateTimer(input.ScheduledTime, CancellationToken.None);
                }

                _logger.LogInformation("Calling StartCampaignProcessing activity.");
                await context.CallActivityAsync(nameof(StartCampaignProcessing), input);

                _logger.LogInformation("Calling CompleteCampaign activity.");
                await context.CallActivityAsync(nameof(CompleteCampaign), input.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing campaign {CampaignId}", input.CampaignId);
                _logger.LogInformation("Calling FailCampaign activity.");
                await context.CallActivityAsync(nameof(FailCampaign), input.CampaignId);
                throw;
            }
        }

        [Function(nameof(StartCampaignProcessing))]
        public async Task StartCampaignProcessing([ActivityTrigger] InputPayload input)
        {
            try
            {
                _logger.LogInformation("StartCampaignProcessing started for CampaignId: {CampaignId}", input.CampaignId);
                _logger.LogInformation("Input Payload: {Payload}", JsonConvert.SerializeObject(input));

                var campaignData = input.CampaignJsonData;
                var campaign = campaignData.Adapt<Campaign>();
                campaign.CampaignId = input.CampaignId;
                var audience = input.Audiances;

                _logger.LogInformation("Calling ProcessCampaignAsync with {AudienceCount} audience(s).", audience?.Count ?? 0);
                await _campaignScheduler.ProcessCampaignAsync(campaign, audience);
                _logger.LogInformation("Campaign processing completed for CampaignId: {CampaignId}", input.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing campaign {CampaignId}. JsonData: {JsonData}", input.CampaignId, JsonConvert.SerializeObject(input.CampaignJsonData));
                throw;
            }
        }

        [Function(nameof(CompleteCampaign))]
        public async Task CompleteCampaign([ActivityTrigger] Guid campaignId)
        {
            _logger.LogInformation("Marking campaign {CampaignId} as Completed.", campaignId);

            var campaign = await GetCampaignData(campaignId);
            if (campaign != null)
            {
                campaign.State = CampaignState.Completed;
                await _genericRepository.UpdateRecordAsync(
                    "Campaigns",
                    new List<string> { "State" },
                    campaign,
                    new Dictionary<string, object> { { "CampaignId", campaignId } });

                _logger.LogInformation("Campaign {CampaignId} marked as Completed.", campaignId);
            }
            else
            {
                _logger.LogWarning("Campaign not found for CampaignId: {CampaignId}", campaignId);
            }
        }

        [Function(nameof(FailCampaign))]
        public async Task FailCampaign([ActivityTrigger] Guid campaignId)
        {
            _logger.LogInformation("Marking campaign {CampaignId} as Incompleted due to failure.", campaignId);

            var campaign = await GetCampaignData(campaignId);
            if (campaign != null)
            {
                campaign.State = CampaignState.Incompleted;
                await _genericRepository.UpdateRecordAsync(
                    "Campaigns",
                    new List<string> { "State" },
                    campaign,
                    new Dictionary<string, object> { { "CampaignId", campaignId } });

                _logger.LogInformation("Campaign {CampaignId} marked as Incompleted.", campaignId);
            }
            else
            {
                _logger.LogWarning("Campaign not found for CampaignId: {CampaignId}", campaignId);
            }
        }

        private async Task<Campaign> GetCampaignData(Guid campaignId)
        {
            _logger.LogInformation("Fetching campaign data for CampaignId: {CampaignId}", campaignId);

            var campaign = await _genericRepository.GetByObjectAsync<Campaign>(
                new Dictionary<string, object> { { "CampaignId", campaignId } },
                "Campaigns");

            var result = campaign.FirstOrDefault();
            if (result != null)
                _logger.LogInformation("Campaign data retrieved successfully for CampaignId: {CampaignId}", campaignId);
            else
                _logger.LogWarning("No campaign data found for CampaignId: {CampaignId}", campaignId);

            return result ?? new();
        }
    }

}
