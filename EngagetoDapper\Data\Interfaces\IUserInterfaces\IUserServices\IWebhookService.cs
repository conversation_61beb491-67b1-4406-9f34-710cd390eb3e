﻿using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;


namespace EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices
{
    public interface IWebhookService
    {
        Task<List<WebhookEndpointEntity>> GetWebhookEnpointAsync(string companyId);
        Task<bool> SaveWebhookEnpointAsync(WebhookEndpointRequestDto webhookEndpointRequestDto);
        Task<bool> SaveWebhookEventsAsync(string companyId, string eventName, Conversations conversations);
        Task<bool> SendTemplateWebhookAsync(string companyId, Guid templateId);
    }
}
