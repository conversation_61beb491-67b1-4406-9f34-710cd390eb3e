﻿using EngagetoContracts.WebhookContracts.Client;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Response;
using EngagetoRepository.WebhookRepository.Hubs.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Runtime.InteropServices;
using System.Security.Claims;
//using WhatsAppBusiness.Models;

namespace Engageto.Controllers.WebhookControllers
{
    [Route("api/")]
    [ApiController]
    public class InboxController : ControllerBase
    {
        private readonly ApplicationDbContext appDbContext;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly IInboxService _inboxService;
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;


        public InboxController(ApplicationDbContext appDb,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
            IInboxService inboxService, IWhatsAppBusinessClient whatsAppBusinessClient)
        {
            appDbContext = appDb;
            _userService = userService;
            _inboxService = inboxService;
            _whatsAppBusinessClient = whatsAppBusinessClient;
        }

        [HttpPost("latestInbox-contacts")]
        [Authorize]
        public async Task<IActionResult> LatestInboxContacts([FromQuery, Required(ErrorMessage = "Please enter the Business Id")] Guid BusinessId,
              [FromQuery, Required(ErrorMessage = "Please enter the User Id")] Guid UserId, [FromBody] FilterDto? Operations, [FromQuery] int page)
        {
            try
            {
                var inboxContacts = await _inboxService.LatestInboxContacts(BusinessId, UserId.ToString(), Operations ?? new(), page);
                return Ok(inboxContacts);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("inbox-contacts")]
        [Authorize]
        public async Task<IActionResult> InboxContacts([FromQuery, Required(ErrorMessage = "Please enter the Business Id")] Guid BusinessId, [FromQuery, Required(ErrorMessage = "Please enter the User Id")] Guid UserId, [FromBody, Optional] FilterDto? Operations)
        {
            try
            {
                var result = await _inboxService.GetInboxContactsAsync(BusinessId, UserId, Operations);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.Message);
            }
        }


        [HttpGet("get-inbox-contacts")]
        [Authorize]
        public async Task<IActionResult> GetInboxContacts([FromQuery, Required(ErrorMessage = "Please enter the Business Id")] Guid BusinessId,
            CancellationToken cancellationToken)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var currentUserIdClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                if (currentUserIdClaim == null || !Guid.TryParse(currentUserIdClaim.Value, out Guid currentUserId))
                    throw new UnauthorizedAccessException("Invalid current user.");

                var result = await _userService.IsValidateMetaAccount(BusinessId.ToString(), currentUserId);
                cancellationToken.ThrowIfCancellationRequested();
                var inboxContacts = await _userService.GetInboxContactsAsync(BusinessId.ToString(), currentUserId, result?.PhoneNumberID ?? string.Empty, cancellationToken);
                return Ok(inboxContacts.OrderByDescending(x => x.UnRead).ThenByDescending(x => x.LastMessageAt));
            }
            catch (OperationCanceledException)
            {
                return StatusCode(499, new ApiResponse<string>()
                {
                    Success = false,
                    Message = "The operation was cancelled."
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }


        [HttpGet("inbox-contact-conversations")]
        [Authorize]
        public async Task<IActionResult> InboxContactsConversations([FromQuery, Required(ErrorMessage = "Please enter the User Id.")] Guid BusinessId,
                                                 [FromQuery, Required, RegularExpression(@"^\d{12,13}$", ErrorMessage = "Contact format is not correct.")] string Contact)
        {
            try
            {
                var result = await _inboxService.GetInboxConversations(BusinessId, Contact);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse<string>()
                {
                    Success = false,
                    Message = ex.Message,
                    Errors = ex.StackTrace
                });
            }
        }

        [HttpGet("filter")]
        [Authorize]
        public IActionResult Filter([FromQuery] Guid UserId)
        {
            try
            {
                var obj = new Filters();
                var filter = appDbContext.Filters.FirstOrDefault(m => m.UserId == UserId);
                if (filter != null)
                {
                    obj = filter;
                }
                return Ok(obj);
            }
            catch (ArgumentNullException ex)
            {
                Console.WriteLine($"ArgumentNullException: {ex.Message}");
                return BadRequest("An argument provided was null.");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"InvalidOperationException: {ex.Message}");
                return StatusCode(StatusCodes.Status500InternalServerError, "An invalid operation occurred.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception: {ex.Message}");
                return StatusCode(StatusCodes.Status500InternalServerError, "An unexpected error occurred.");
            }

        }
        [HttpPost("book-demo")]
        public async Task<IActionResult> BookDemo([FromBody] CreateRoomRequest request)
        {
            try
            {
                await _inboxService.BookDemoAsync(request.Name, request.Email, request.ScheduledTime);
                return Ok(new { Message = "Demo booked successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Failed to create room: {ex.Message}");
            }
        }
    }
}
