﻿using EngagetoEntities.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoBackGroundJobs.Interfaces
{
    public interface ICampaignScheduler
    {
        Task ProcessCampaignAsync(Campaign campaign , List<string> audiances);
        Task PartitionCampaignBatchesAsync(Campaign campaign, List<string> audienceList, bool isDevelopment);
        Task ProcessCampaignBatchAsync(int pageSize, int page, Campaign campaign,List<string>  audiances,bool isCompleted = false);
        Task<List<string>> GetContactIdsToExcel(int uploadFileId, Campaign campaign);
    }
}
