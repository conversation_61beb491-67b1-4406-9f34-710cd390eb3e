﻿using Microsoft.Data.SqlClient;
using System.Data;

namespace EngagetoDapper.Data.Connections
{
    public sealed class DapperConnectionFactory : IDapperConnectionFactory, IDisposable
    {
        private readonly IDbConnection _dbConnection;
        private readonly IUnitOfWork _unitOfWork;

        public DapperConnectionFactory(string connectionString)
        {
            _dbConnection = new SqlConnection(connectionString);
            _dbConnection.Open();
            _unitOfWork = new UnitOfWork(_dbConnection);
        }

        IDbConnection IDapperConnectionFactory.Connection => _dbConnection;
        IUnitOfWork IDapperConnectionFactory.UnitOfWork => _unitOfWork;


        public void Dispose()
        {
            _unitOfWork.Dispose();
            _dbConnection.Dispose();
        }
    }
}
