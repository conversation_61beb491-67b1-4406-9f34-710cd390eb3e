﻿using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces
{
    public interface IConversationAnalyticsService
    {
        Task<List<ConversationAnalyticsPriceEntity>> GetConversationAnalyticsPriceByYear(string companyId, int year);
        Task<ConversationAnalyticsPriceEntity> GetConversationAnalyticsPriceByCategoryName(string companyId, string categoryName, int year);
        Task<List<ConversationAnalyticsPriceEntity>> GetDefaultConversationAnalyticsPrice();
        Task<List<BusinessDetailsMeta>> GetAllBusinessMetaAccountsAsync();
        Task<bool> SaveConversationAnalyticsDetailsAsync(List<ConversationAnalyticsEntity> conversationAnalyticsDto);
        Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByCompanyIdAsync(string companyId);
        Task<List<ConversationAnalyticsEntity>> GetAnalyticsDetailsByStartAndEndDateAsync(string companyId, DateTime startDate, DateTime endDate, string? ConversationCategory = null);
        Task<List<ConversationAnalyticsEntity>> GetConversationAnalyticsByIdAsync(Guid id);
        Task<bool> SaveConversationCostAsync(ConversationCostDto conversationCostDto);
        Task<ConversationAnalyticsJobRequest?> GetConversationAnalyticsJobRequest(string companyId, DateTime date);
        Task<DateTime?> GetCurrentDayConversationAnalyticsJobRequestsAsync(string companyId);
        Task<bool> UpdateConversationAnalyticsRequestAsync(ConversationAnalyticsJobRequest conversationAnalyticsJob);
        Task<bool> SaveConversationAnalyticsRequestAsync(ConversationAnalyticsJobRequest conversationAnalyticsJob);
        Task<bool> SaveConversationCostDetuctionHistoryAsync(List<EngagetoEntities.Entities.Conversations> conversations, string companyId, string? userId = null);
    }
}
