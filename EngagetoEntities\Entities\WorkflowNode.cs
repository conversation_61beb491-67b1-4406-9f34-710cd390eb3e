using EngagetoEntities.Dtos.CommonDtos.PaymentGatwayDtos;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Enums;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace EngagetoEntities.Entities
{
    [Table("WorkflowNodes")]
    public class WorkflowNode : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public Guid WorkflowId { get; set; }
        public string? BusinessId { get; set; } 
        public NodeType Type { get; set; }
        public string? Payload { get; set; }  // Stored as JSON string
        public string? Position { get; set; }  // Stored as JSON string
        public int? Delay { get; set; }
        public bool IsEntry { get; set; }
        public bool IsFinal { get; set; }

        // Navigation properties
        [ForeignKey("WorkflowId")]
        public virtual Workflow Workflow { get; set; }
        public virtual ICollection<WorkflowEdge> SourceEdges { get; set; } = new List<WorkflowEdge>();
        public virtual ICollection<WorkflowEdgeTargetNode> TargetEdges { get; set; } = new List<WorkflowEdgeTargetNode>();


        [NotMapped]
        public NodePayload PayloadModel
        {
            get => string.IsNullOrEmpty(Payload)
                    ? null
                    : JsonSerializer.Deserialize<NodePayload>(Payload);
            set => Payload = value == null
                    ? null
                    : JsonSerializer.Serialize(value);
        }

        [NotMapped]
        public PositionModel PositionJson
        {
            get => string.IsNullOrEmpty(Position) ? null : JsonSerializer.Deserialize<PositionModel>(Position);
            set => Position = value == null ? null : JsonSerializer.Serialize(value);
        }
    }
    public class PositionModel
    {
        public double X { get; set; }
        public double Y { get; set; }
    }

    public class NodePayload
    {
        [JsonPropertyName("FlowStart")]
        public FlowStartModel? FlowStartModel { get; set; }
        public TemplateModel? Template { get; set; }
        public InteractiveMessageModel? InteractiveMessage { get; set;}
        public ConditionModel? Condition { get; set; }  
        public HttpRequestModel? HttpRequest { get; set; }
    }

    public class InteractiveMessageModel
    {
        public InteractiveType Type { get; set; }
        public MediaType mediaType { get; set; }
        public string? mediaFile { get; set; }
        public string Body { get; set; } = default!;
        public string? Header { get; set; }
        public string? Footer { get; set; } 
        public List<ButtonModel>? Buttons { get; set; }
        public ListModel? List { get; set; }
        public List<VariableModel>? Variables { get; set; }

    }
    public class ButtonModel
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }
    public class ListModel
    {
        public string? ButtonText { get; set; }
        public List<SectionModel>? Sections { get; set; }
    }
    public class SectionModel
    {
        public string Title { get; set; }
        public List<RowModel> Rows { get; set; }
    }
    public class RowModel
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
    }

    public class TemplateModel
    {
        public Guid? TemplateId { get; set; }
        public string TemplateName { get; set; } = string.Empty;
        public List<VariableModel>? BodyVariableValues { get; set; }
        public VariableModel? HeaderValue { get; set; }
        public List<CarouselCardVariableModel>? CarouselVariables { get; set; }
        public int? TimeInMinutes { get; set; }
    }
   
    public class CarouselCardVariableModel
    {
        public List<VariableModel>? BodyCarouselVariableValues { get; set; }
        public List<VariableModel>? RedirectUrlVariableValues { get; set; }
        public string? MediaUrl { get; set; }
    }
    public class VariableModel
    {
        public string? Variable { get; set; } = default!;
        public string? Value { get; set; }
        public string FallbackValue { get; set; } = default!;
    }
    public class FlowStartModel
    {
        public EntryNodeType EntryNodeType { get; set; }
        public List<LeadSourceDto>? LeadSource { get; set; }
        public List<LeadStatusDto>? LeadStatus { get; set; }
        public List<string>? LeadProject { get; set; }
    }
    public class ConditionModel
    {
        public string? Attribute { get; set; }
        public ConditionOperator Operator { get; set; }
        public string Value { get; set; } = string.Empty;
        public List<ButtonModel> Buttons { get; set; }
    }
    public class HttpRequestModel
    {
        public string Url { get; set; } = default!;
        public string Method { get; set; } = default!;
        public string ContentType { get; set; } = default!;
        public Dictionary<string, object>? Headers { get; set; }
        public Dictionary<string, object>? QueryParameters { get; set; }
        public Dictionary<string, object>? FormData { get; set; }
        public string? JsonBody { get; set; }
        public List<VariableModel>? VariableValues { get; set; }
    }

}

