using EngagetoEntities.Entities;

namespace EngagetoContracts.Workflow
{
    public interface INodeWorkflowEngineService
    {
        Task ProcessWorkflowAsync(Contacts contacts, string? textMessage, bool? isNewCustomer = false, bool? isStatusChange = false, bool? isProjectChange = false);
        Task<int> CleanupScheduledJobsForNode(Guid nodeId);
        Task<int> CleanupScheduledJobsForWorkflow(Guid workflowId);
        Task ExecuteUnifiedReminderTemplateJob(Guid contactId, string templateJson);
        Task ExecuteInteractiveMessageTimeoutJob(Guid contactId, Guid nodeId);
        Task ExecuteTemplateTimeoutJob(Guid contactId, Guid nodeId);
        Task ExecuteUniversalTimeoutJob(Guid contactId, Guid nodeId, string nodeType);
    }
}