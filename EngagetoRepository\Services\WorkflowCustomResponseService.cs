using DocumentFormat.OpenXml.Office2010.Excel;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Entities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.Services
{
    public class WorkflowCustomResponseService : IWorkflowCustomResponseService
    {
        private readonly ApplicationDbContext _dbContext;

        public WorkflowCustomResponseService(
            ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<WorkflowCustomerResponseDto> CreateWorkflowCustomResponseAsync(WorkflowCustomerResponseDto request, Guid currentUserId)
        {
            try
            {
                var workflowCustomResponse = new WorkflowCustomerResponse
                {
                    Id = Guid.NewGuid(),
                    BusinessId = request.BusinessId,
                    NodeId = request.NodeId,
                    UserId = request.UserId,
                    WorkflowId =  request.WorkflowId,
                    AttributeId = request.AttributeId,
                    CreatedBy = currentUserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedBy = currentUserId,
                    UpdatedAt = DateTime.UtcNow
                };

                await _dbContext.AddAsync(workflowCustomResponse);
                await _dbContext.SaveChangesAsync();

                return workflowCustomResponse.Adapt<WorkflowCustomerResponseDto>();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<WorkflowCustomerResponseDto> UpdateWorkflowCustomResponseAsync(Guid id, WorkflowCustomerResponseDto request, Guid currentUserId)
        {
            try
            {
                var workflowCustomResponse = await _dbContext.WorkflowCustomerResponse
                    .FirstOrDefaultAsync(w => w.Id == id && !w.IsDeleted);

                if (workflowCustomResponse == null)
                {
                    throw new KeyNotFoundException($"Workflow custom response with ID {id} not found");
                }

                workflowCustomResponse.BusinessId = request.BusinessId;
                workflowCustomResponse.NodeId = request.NodeId;
                workflowCustomResponse.UserId = request.UserId;
                workflowCustomResponse.AttributeId = request.AttributeId;
                workflowCustomResponse.UpdatedBy = currentUserId;
                workflowCustomResponse.UpdatedAt = DateTime.UtcNow;

                _dbContext.WorkflowCustomerResponse.Update(workflowCustomResponse);
                await _dbContext.SaveChangesAsync();
                return workflowCustomResponse.Adapt<WorkflowCustomerResponseDto>(); ;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<WorkflowCustomerResponseDto>> GetAllWorkflowCustomResponsesAsync()
        {
            try
            {
                var responses = await _dbContext.WorkflowCustomerResponse
                    .Where(w => !w.IsDeleted)
                    .OrderByDescending(w => w.CreatedAt)
                    .ToListAsync();

                return responses.Adapt<List<WorkflowCustomerResponseDto>>(); ;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<WorkflowCustomerResponseDto> GetWorkflowCustomResponseByIdAsync(Guid id)
        {
            try
            {
                var response = await _dbContext.WorkflowCustomerResponse
                    .FirstOrDefaultAsync(w => w.Id == id && !w.IsDeleted);

                if (response == null)
                {
                    throw new KeyNotFoundException($"Workflow custom response with ID {id} not found");
                }

                return response.Adapt<WorkflowCustomerResponseDto>();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<WorkflowCustomerResponseDto>> GetWorkflowCustomResponseByWorkflowIdAsync(Guid workflowId)
        {
            try
            {
                var response = await _dbContext.WorkflowCustomerResponse
                    .Where(w => w.WorkflowId == workflowId && !w.IsDeleted).ToListAsync();

                if (response == null)
                {
                    return null;
                }

                return response.Adapt<List<WorkflowCustomerResponseDto>>();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<WorkflowCustomerResponse> GetWorkflowCustomResponseAsync(Guid nodeId, Guid workflowId, Guid businessId)
        {
            try
            {
                var response =  await _dbContext.WorkflowCustomerResponse.FirstOrDefaultAsync(i=>i.NodeId==nodeId && i.WorkflowId ==workflowId&&i.BusinessId==businessId );
               
                if (response == null)
                {
                    return null;
                }
                return response;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
    }
} 