﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace EngagetoEntities.Dtos.WebhookDtos
{
    public class WAWebhookDto
    {
        [JsonProperty("object")]
        public string Object { get; set; }

        [JsonProperty("entry")]
        public Entry[] Entry { get; set; }
    }
    public partial class Entry
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("changes")]
        public Change[] Changes { get; set; }

        [JsonProperty("time")]
        public long Time { get; set; }
    }

    public partial class Change
    {
        [JsonProperty("value")]
        public Value? Value { get; set; }

        [JsonProperty("field")]
        public string? Field { get; set; }
    }

    public partial class Value
    {
        [JsonProperty("messaging_product")]
        public string? MessagingProduct { get; set; }
        [JsonProperty("event")]
        public string? Event { get; set; }
        [JsonProperty("message_template_id")]
        public long? MessageTemplateId { get; set; }
        [JsonProperty("message_template_name")]
        public string? MessageTemplateName { get; set; }
        [JsonProperty("reasion")]
        public string? Reason { get; set; }

        [JsonProperty("metadata")]
        public Metadata? Metadata { get; set; }

        [JsonProperty("statuses")]
        public Statuses[]? Statuses { get; set; }
        [JsonProperty("contacts")]
        public Contact[]? Contacts { get; set; }
        [JsonProperty("messages")]
        public Message[]? Messages { get; set; }
        [JsonProperty("max_daily_conversation_per_phone")]
        public long? MessageLimitPerUser { get; set; }
        [JsonProperty("current_limit")]
        public string? CurrentLimit { get; set; }

        [JsonProperty("business_id")]
        public long? BusinessId { get; set; }

        [JsonProperty("max_phone_numbers_per_business")]
        public long? MaxPhoneNumbersPerBusiness { get; set; }

        [JsonProperty("previous_quality_score")]
        public string? PreviousQualityScore { get; set; }

        [JsonProperty("new_quality_score")]
        public string? NewQualityScore { get; set; }

        [JsonProperty("message_template_language")]
        public string? MessageTemplateLanguage { get; set; }

        [JsonProperty("display_phone_number")]
        public string? DisplayPhoneNumber { get; set; }
    }


    public partial class Metadata
    {
        [JsonProperty("display_phone_number")]
        public string? DisplayPhoneNumber { get; set; }

        [JsonProperty("phone_number_id")]
        public string? PhoneNumberId { get; set; }
    }

    public partial class Statuses
    {
        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonProperty("recipient_id")]
        public string? RecipientId { get; set; }

        [JsonProperty("status")]
        public string? Status { get; set; }
        [JsonProperty("errors")]
        public WAStatusError[]? Errors { get; set; }

        [JsonProperty("timestamp")]
        public string? Timestamp { get; set; }

        [JsonProperty("conversation")]
        public Conversation? Conversation { get; set; }

        [JsonProperty("pricing")]
        public Pricing? Pricing { get; set; }
    }

    public partial class Conversation
    {
        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonProperty("expiration_timestamp")]
        public string? ExpirationTimestamp { get; set; }

        [JsonProperty("origin")]
        public Origin? Origin { get; set; }
    }

    public partial class Origin
    {
        [JsonProperty("type")]
        public string? Type { get; set; }
    }

    public partial class Pricing
    {
        [JsonProperty("pricing_model")]
        public string? PricingModel { get; set; }

        [JsonProperty("billable")]
        public bool? Billable { get; set; }

        [JsonProperty("category")]
        public string? Category { get; set; }
    }

    //Recevied 
    #region Received Message webhook Dto
    public partial class Contact
    {
        [JsonProperty("profile")]
        public Profile Profile { get; set; }

        [JsonProperty("wa_id")]
        public string WaId { get; set; }
    }

    public partial class Profile
    {
        [JsonProperty("name")]
        public string Name { get; set; }
    }
    public partial class Message
    {
        [JsonProperty("from")]
        public string From { get; set; }
        [JsonProperty("id")]
        public string Id { get; set; }
        [JsonProperty("timestamp")]
        public string? Timestamp { get; set; }
        [JsonProperty("context")]
        public MessageContext? MessageContext { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }
        [JsonProperty("text")]
        public Text? Text { get; set; }
        [JsonProperty("reaction")]
        public Reaction? Reaction { get; set; }
        [JsonProperty("image")]
        public BaseMediaMessage? Image { get; set; }
        [JsonProperty("video")]
        public BaseMediaMessage? Video { get; set; }
        [JsonProperty("audio")]
        public BaseMediaMessage? Audio { get; set; }
        [JsonProperty("document")]
        public BaseMediaMessage? Document { get; set; }
        [JsonProperty("sticker")]
        public BaseMediaMessage? Sticker { get; set; }
        [JsonProperty("button")]
        public ReplyOnButton? Button { get; set; }
        [JsonProperty("interactive")]
        public Interactive? Interactive { get; set; }

    }
    public partial class Text
    {
        [JsonProperty("body")]
        public string Body { get; set; }
    }
    public partial class Reaction
    {
        [JsonProperty("emoji")]
        public string Emoji { get; set; }

        [JsonProperty("messsage_id")]
        public string MesssageId { get; set; }
    }
    public class BaseMediaMessage
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        [JsonProperty("caption")]
        public string? Caption { get; set; }

        [JsonProperty("mime_type")]
        public string MimeType { get; set; }

        [JsonProperty("sha256")]
        public string Sha256 { get; set; }
        [JsonProperty("animated")]
        public bool? Animated { get; set; }
        [JsonProperty("file")]
        public string? File { get; set; }
    }

    public class MessageContext
    {
        [JsonProperty("from")]
        public string? From { get; set; }
        [JsonProperty("id")]
        public string Id { get; set; }
    }
    public class ReplyOnButton
    {
        public string Payload { get; set; }
        public string Text { get; set; }
    }
    public class Interactive
    {
        public string? Type { get; set; }
        [JsonProperty("button_reply")]
        public ButtonReply? ButtonReply { get; set; }
        [JsonProperty("list_reply")]
        public ListReply? ListReply { get; set; }
    }
    public class ListReply
    {
        public string? Id { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
    }

    public class ButtonReply
    {
        public string? Id { get; set; }
        public string? Title { get; set; }
    }

    #endregion

    public class WAStatusError
    {
        public long? Code { get; set; }
        public string? Message { get; set; }
        public string? Type { get; set; }
        [JsonProperty("error_data")]
        public WAErrorData? ErrorData { get; set; }
        [JsonProperty("error_subcode")]
        public long? ErrorSubcode { get; set; }
        [JsonProperty("fbtrace_id")]
        public string? FbtraceId { get; set; }
    }
    public class WAErrorData
    {
        [JsonProperty("messaging_product")]
        public string? MessagingProduct { get; set; }
        public string? Details { get; set; }
    }
}
