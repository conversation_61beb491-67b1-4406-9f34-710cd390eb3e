﻿using System;
using PhoneNumbers;

public static class PhoneNumberHelper
{
    private static readonly PhoneNumberUtil PhoneUtil = PhoneNumberUtil.GetInstance();

    /// <summary>
    /// Extracts the country code and national (local) phone number from a given phone number.
    /// </summary>
    /// <param name="phoneNumber">The phone number as a string.</param>
    /// <param name="defaultCountryCode">The default country code to use if the phone number lacks one.</param>
    /// <returns>A tuple containing the country code and the national number.</returns>
    public static (string CountryCode, string NationalNumber) GetCountryCodeAndPhoneNumber(string phoneNumber, string? defaultCountryCode = "+91")
    {
        try
        {
            // Remove any extra "+" symbols and parse the phone number
            string formattedNumber = phoneNumber.StartsWith("+") ? phoneNumber : $"+{phoneNumber}";
            var parsedNumber = PhoneUtil.Parse(formattedNumber, null);

            // Validate the parsed number
            if (!PhoneUtil.IsValidNumber(parsedNumber) && defaultCountryCode != null)
            {
                // If invalid, try parsing with the default country code
                string fallbackNumber = $"{defaultCountryCode}{phoneNumber}";
                parsedNumber = PhoneUtil.Parse(fallbackNumber, null);
            }

            if (!PhoneUtil.IsValidNumber(parsedNumber))
                throw new ArgumentException("Invalid phone number.");

            // Extract country code and national number
            string countryCode = parsedNumber.CountryCode.ToString();
            string nationalNumber = parsedNumber.NationalNumber.ToString();

            return (countryCode, nationalNumber);
        }
        catch (NumberParseException)
        {
            // Return the original input if parsing fails
            return (defaultCountryCode ?? "91", phoneNumber);
        }
    }
}
