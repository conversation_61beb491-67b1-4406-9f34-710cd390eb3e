# Interactive Message Variables - EXACT Campaign Pattern

## Overview

This implementation follows your **exact campaign pattern** for separating normal variables from Leadrat variables using `StringHelper.IsLeadratVariable()`.

## Key Pattern (Same as Campaigns)

### Variable Detection Logic

```csharp
// EXACT same logic as campaigns
var hasLeadratVariables = variables.Any(v => StringHelper.IsLeadratVariable(v.Value));

if (!hasLeadratVariables)
{
    // Process with local variables only (similar to ProcessTemplateWithLocalVariable)
    return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
}
else
{
    // Process with Leadrat variables (similar to ProcessTemplateWithLeadratVariable)
    return await ProcessInteractiveMessageWithLeadratVariablesAsync(messageBody, variables, extractedVariables, contact);
}
```

### Campaign Pattern Reference

```csharp
// From your CampaignService.cs - EXACT same pattern
campaign.HeaderValue = dto.Template?.HeaderValue != null
    ? ((string.IsNullOrEmpty(dto.Template.HeaderValue.Value) || StringHelper.IsLeadratVariable(dto.Template.HeaderValue.Value))
    ? dto.Template.HeaderValue.FallbackValue
    : dto.Template.HeaderValue.Value) : null;

campaign.BodyValues = dto.Template?.BodyValues != null
    ? string.Join(",", dto.Template.BodyValues.Select(i =>
        ((string.IsNullOrEmpty(i.Value) || StringHelper.IsLeadratVariable(i.Value)))
        ? i.FallbackValue
        : i.Value)) : null;
```

## Usage Examples

### 1. Local Variables Only (No API Call)

```json
{
  "Type": "Button",
  "Body": "Hi {{1}}, welcome to {{2}}!",
  "Variables": [
    {
      "Variable": "{{1}}",
      "Value": "Name", // Contact property (NOT Leadrat variable)
      "FallbackValue": "Customer"
    },
    {
      "Variable": "{{2}}",
      "Value": "CompanyName", // Contact property (NOT Leadrat variable)
      "FallbackValue": "Our Company"
    }
  ]
}
```

**Result**: Uses contact properties directly (no API call)

- `{{1}}` → `contact.Name` or "Customer"
- `{{2}}` → `contact.CompanyName` or "Our Company"

### 2. Leadrat Variables (Triggers API Call)

```json
{
  "Type": "Button",
  "Body": "Hi #name#, your balance is #balance#!",
  "Variables": [
    {
      "Variable": "#name#",
      "Value": "#name#", // Leadrat variable (triggers API call)
      "FallbackValue": "Valued Customer"
    },
    {
      "Variable": "#balance#",
      "Value": "#balance#", // Leadrat variable (triggers API call)
      "FallbackValue": "0.00"
    }
  ]
}
```

**Result**: Makes API call to Leadrat API

- API Success: `#name#` → API response value
- API Failure: `#name#` → "Valued Customer"

### 3. Mixed Variables (Triggers API Call)

```json
{
  "Type": "Button",
  "Body": "Hi #name#, welcome to {{1}}!",
  "Variables": [
    {
      "Variable": "#name#",
      "Value": "#name#", // Leadrat variable (triggers API call)
      "FallbackValue": "Customer"
    },
    {
      "Variable": "{{1}}",
      "Value": "CompanyName", // Contact property
      "FallbackValue": "Our Company"
    }
  ]
}
```

**Result**: Makes API call because of `#name#` variable

- `#name#` → API response or "Customer"
- `{{1}}` → `contact.CompanyName` or "Our Company"

## Processing Flow

### Step 1: Variable Extraction

```csharp
// Uses your existing StringHelper method
var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);
```

### Step 2: Leadrat Variable Detection

```csharp
// EXACT same logic as campaigns
var hasLeadratVariables = variables.Any(v => StringHelper.IsLeadratVariable(v.Value));
// StringHelper.IsLeadratVariable() checks if value matches pattern: #([^\s#][\w\s]*[^\s#])#
```

### Step 3A: Local Variables Processing

```csharp
// Similar to ProcessTemplateWithLocalVariable (same as AutomationSettingService)
var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);
var contactProperties = typeof(Contacts).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(contact)?.ToString() ?? string.Empty);

// Create variable values list in the same order as extracted variables
var variableValuesList = new List<string>();
foreach (var extractedVar in extractedVariables)
{
    var matchingVariable = variables.FirstOrDefault(v => v.Variable == extractedVar);
    if (matchingVariable != null)
    {
        var value = contactProperties.ContainsKey(matchingVariable.Value) && !string.IsNullOrEmpty(contactProperties[matchingVariable.Value])
            ? contactProperties[matchingVariable.Value]
            : matchingVariable.FallbackValue;
        variableValuesList.Add(value);
    }
}

return StringHelper.ReplacePlaceholders(updatedMessage, variableValuesList);
```

### Step 3B: Leadrat Variables Processing

```csharp
// Similar to ProcessTemplateWithLeadratVariable
var phoneNumber = $"{contact.CountryCode}{contact.Contact}";
var apiUrl = $"https://your-leadrat-api.com/customer/{phoneNumber}";
var response = await httpClient.GetAsync(apiUrl);

if (response.IsSuccessStatusCode)
{
    // Use API data
    var jsonResponse = JsonConvert.DeserializeObject<JObject>(jsonString);
    var apiDataDict = ObjectHelper.ConvertJObjectToDictionary(jsonResponse);

    // Create variable values list in the same order as extracted variables (same as StringHelper pattern)
    var variableValuesList = new List<string>();
    foreach (var extractedVar in extractedVariables)
    {
        var matchingVariable = variables.FirstOrDefault(v => v.Variable == extractedVar);
        if (matchingVariable != null)
        {
            var value = GetValueFromApiData(apiDataDict, matchingVariable.Value) ?? matchingVariable.FallbackValue;
            variableValuesList.Add(value);
        }
    }

    return StringHelper.ReplacePlaceholders(messageBody, variableValuesList);
}
else
{
    // Fallback to local variables
    return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
}
```

## Key Benefits

1. **✅ Exact Campaign Pattern**: Uses identical logic as your existing campaign system
2. **✅ Automatic Detection**: No configuration needed - automatically detects Leadrat variables
3. **✅ Backward Compatible**: Existing workflows continue to work unchanged
4. **✅ Consistent Behavior**: Developers familiar with campaigns will understand immediately
5. **✅ Robust Fallback**: Same fallback logic as campaigns

## Implementation Details

### Leadrat Variable Regex

```csharp
// From StringHelper.cs (line 18)
private static readonly Regex LeadratVariableRegex = new Regex(@"#([^\s#][\w\s]*[^\s#])#");

// Matches: #name#, #balance#, #customer name#
// Doesn't match: Name, {{1}}, CompanyName
```

### API URL Customization

```csharp
// In ProcessInteractiveMessageWithLeadratVariablesAsync method (line 898)
var apiUrl = $"https://your-leadrat-api.com/customer/{phoneNumber}";
// Replace with your actual Leadrat API URL
```

This implementation ensures your interactive messages have the same powerful dynamic content capabilities as your campaigns, using the exact same patterns and detection logic!
