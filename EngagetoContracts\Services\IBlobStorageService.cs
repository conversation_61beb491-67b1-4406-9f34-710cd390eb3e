﻿using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace EngagetoContracts.Services
{
    public interface IBlobStorageService : ITransientService
    {
        Task<ViewUploadFileDto> UploadAsync(UploadFileDto fileDto, bool shouldUseOtherFileName = true);
        Task<FileContentResult> DownloadAsync(string fileName);
        Task<string> GetUrlAsync(string fileName, bool isExpire = false);
        Task<bool> UpdateFileAsync(string fileName, IFormFile file);
        Task<ViewUploadedFileDto> GetUploadedFile(int  id);
        Task<bool> UpdateFileAsync(string fileName, MemoryStream memoryStream, string contentType);
    }
}
