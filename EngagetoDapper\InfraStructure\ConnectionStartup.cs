﻿using EngagetoDapper.Data.Connections;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Data;


namespace EngagetoDapper.InfraStructure
{
    public static class ConnectionStartup
    {
        public static IServiceCollection AddConnection(this IServiceCollection services, IConfiguration config)
        {
            // Register DapperConnectionFactory
            var connectionString = config["ConnectionStrings:ConnStr"];

            services.AddScoped<IDbConnection>(sp =>
            {
                var connection = new SqlConnection(connectionString);
                connection.Open();
                return connection;
            })
            .AddScoped<IUnitOfWork>(sp =>
            {
                var dbConnection = sp.GetRequiredService<IDbConnection>();
                return new UnitOfWork(dbConnection);
            })
            .AddScoped<IDapperConnectionFactory>(sp =>
            {
                return new DapperConnectionFactory(connectionString);
            })
            .AddScoped<IUnitOfWork,UnitOfWork>(); 
            return services;
        }
    }
}
