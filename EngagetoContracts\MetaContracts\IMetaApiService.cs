﻿using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using Newtonsoft.Json.Linq;

namespace EngagetoContracts.MetaContracts
{
    public interface IMetaApiService
    {
        public Task<HealthStatusDto?> GetHealthStatusAsync(string accountId);
        Task<ViewAuthTempaltePreviewDto?> GetAuthTemplatePreviewAsync(AuthTemplatePreviewRequestDto requestDto);
        Task<(bool Success, JObject Result)> CreateAuthTemplateRequestAsync(string businessId, JObject authTemplate);
        Task<(bool Success, JObject Result)> CreateTemplateRequestAsync(string businessId, JObject authTemplate);
        Task<(bool Success, JObject Result)> SendCarouselTemplateAsync(string businessId, JObject template, string? waPhoneNumberId = null, string? token = null);
        Task<(bool IsSuccess, JObject Result)> SendTextWithMediaMessageAsync(string businessId, string phoneNumber, string text, string mediatype, string? mediaUrl, string? waMessageId, string? token = null, string? waPhoneNumberId = null, string? waAccountId = null);
        Task<(bool IsSuccess, JObject Result)> SendTemplateAsync(string businessId, string language, string phoneNumber, string templateName, List<string>? bodyValues, MediaType mediaType, string? headerValue, string? token = null, string? waPhoneNumberId = null, string? waAccountId = null);
        Task<JObject> GetTemplateByNameAsync(string businessId, string templateName);
        Task<(bool Success, JObject Result)> GetBusinessDetailsAsync(string businessId);
        Task<(bool Success, JObject Result)> GetConversationAnalyticsAsync(string businessId);
        Task<(bool Success, JObject Result)> SendAuthTemplateRequestAsync(string businessId, JObject authTemplate);
        Task<List<GetTemplateData>> GetAllTemplatesAsync(BusinessDetailsMeta business);
        Task<(bool IsSuccess, JObject Result)> SendInteractiveMessageAsync(string businessId,string phoneNumber, string messageBody,  string? headerText,string? Footer  ,MediaType mediaType, string?mediaFile ,InteractiveType type,List<ButtonModel>? buttons = null,  ListModel? listItems = null);
        Task<(bool Success, JObject Result)> BlockContactAsync(string businessId, string phoneNumber);
    }
}
