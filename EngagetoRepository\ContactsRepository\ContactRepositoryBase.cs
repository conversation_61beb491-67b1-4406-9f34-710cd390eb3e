﻿using Amazon.Runtime.Internal.Util;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDatabase.ContactsDatabase.Models;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using EngagetoEntities.Validations;
using EngagetoRepository.Services;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Data;
using System.Reflection;

namespace EngagetoRepository.ContactsRepository
{
    public class ContactRepositoryBase : IContactRepositoryBase
    {
        private readonly ApplicationDbContext _dbContext;
        private IConfiguration _configuration;
        private IGenericRepository _genericRepository;
        private readonly db_aa80b1_whatsappbusinessContext _whatsAppBusinessAPI;
        private readonly IUserIdentityService _userIdentityService;
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IMetaApiService _metaApiService;

        public ContactRepositoryBase(
            ApplicationDbContext dbContext,
            IConfiguration configuration,
            db_aa80b1_whatsappbusinessContext whatsAppBusinessAPI,
            IGenericRepository genericRepository,
            IUserIdentityService userIdentityService,
            INodeWorkflowEngineService nodeWorkflowEngineService,
            ILogHistoryService logHistoryService,
            IMetaApiService metaApiService
            )
        {
            _dbContext = dbContext;
            _whatsAppBusinessAPI = whatsAppBusinessAPI;
            _configuration = configuration;
            _genericRepository = genericRepository;
            _userIdentityService = userIdentityService;
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
            _logHistoryService = logHistoryService;
            _metaApiService = metaApiService;
        }


        public Object GetContactDetailsAsync(ContactOperationsDto? Operations, Guid BusinessId, Guid UserId, int page = 1, int per_page = 10)
        {
            List<Contacts> list = new List<Contacts>();
            var UserIdExist = _whatsAppBusinessAPI.UserRoles;
            int totalContacts = 0;
            if (UserIdExist.Select(m => m.Id.ToString().ToLower()).ToList().Contains(UserId.ToString().ToLower()))
            {
                var RoleId = UserIdExist.FirstOrDefault(m => m.Id == UserId)?.RoleId;
                var Role = _whatsAppBusinessAPI.Roles.FirstOrDefault(m => m.Id == RoleId)?.Name;
                if (Role == "Admin" || Role == "Owner")
                {
                    list = Get(BusinessId).Where(m => m.IsActive).ToList();
                    totalContacts = _dbContext.Contacts.Where(x => x.BusinessId == BusinessId && x.IsActive == true).Count();

                }
                else
                {

                    list = Get(BusinessId).Where(m => m.UserId.ToString()?.ToLower() == UserId.ToString().ToLower() && m.IsActive).ToList();
                    totalContacts = totalContacts = _dbContext.Contacts.Where(x => x.BusinessId == BusinessId && x.UserId == UserId && x.IsActive == true).Count();
                }
            }

            if (Operations != null)
            {

                if (Operations.Searching != null)
                    if (Operations.Searching.Value != null && Operations.Searching.Value != "")
                    {
                        var data1 = list.Where(m => m.Name.ToLower().Contains(Operations.Searching.Value.ToLower())).ToList();
                        if (data1.Count > 0)
                        {
                            list = data1;
                        }

                        else
                        {
                            data1 = list.Where(m => $"{m.CountryCode}{m.Contact}".ToLower().Contains(Operations.Searching.Value.ToLower())).ToList();
                            if (data1.Count > 0)
                            {
                                list = data1;
                            }
                            else
                            {
                                var searchingValue = Operations.Searching?.Value?.ToLower();
                                if (!string.IsNullOrEmpty(searchingValue))
                                {
                                    data1 = list.Where(m => m.Email != null && m.Email.ToLower().Contains(searchingValue)).ToList();
                                    list = data1 ?? new List<Contacts>(); // Ensure list is not null
                                }
                                else
                                {
                                    list = list ?? new List<Contacts>();
                                }
                            }
                        }
                    }
                if (Operations.Sorting != null)
                {


                    if (Operations.Sorting.Column != "" && Operations.Sorting.Column != null)
                    {
                        Operations.Sorting.Column = Operations.Sorting.Column.ToLower();
                        Operations.Sorting.Order = Operations.Sorting.Order?.ToLower();

                        list = Operations.Sorting.Order == "desc" ?
                            list.OrderByDescending(m => GetValue(m, Operations.Sorting.Column)).ToList() :
                            list.OrderBy(m => GetValue(m, Operations.Sorting.Column)).ToList();

                        object GetValue(object item, string propertyName)
                        {
                            var property = item.GetType().GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                            {
                                var value = property.GetValue(item, null);
                                // Handle if property is DateTime
                                if (value is DateTime dateTimeValue)
                                {
                                    return dateTimeValue;
                                }
                                return value ?? new();
                            }
                            throw new ArgumentException($"Property '{propertyName}' not found.");
                        }


                    }
                }

                if (Operations.Filtering != null)
                {

                    if (Operations.Filtering.FilterType != null && Operations.Filtering.FilterType.Any())
                    {
                        List<Contacts> filteredList = list;
                        List<Contacts> Empty = new List<Contacts>();

                        if (Operations.Filtering.FilterType == "and")
                        {
                            foreach (var condition in Operations.Filtering.Conditions)
                            {

                                var property = typeof(Contacts).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                                if (property != null)
                                {
                                    filteredList = ApplyCondition(filteredList, condition, property);
                                }

                            }

                        }
                        else if (Operations.Filtering.FilterType == "or")
                        {
                            var tempFilteredList = new List<Contacts>();
                            filteredList = new List<Contacts>();
                            foreach (var condition in Operations.Filtering.Conditions)
                            {
                                var property = typeof(Contacts).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                                if (property != null)
                                {
                                    tempFilteredList = ApplyCondition(list, condition, property);
                                    foreach (var item in tempFilteredList)
                                    {
                                        if (!filteredList.Contains(item))
                                        {
                                            filteredList.Add(item);
                                        }
                                    }

                                }

                            }

                        }
                        list = filteredList;
                    }

                }
            }

            if (Operations != null)
            {
                if ((Operations.Searching != null && !string.IsNullOrEmpty(Operations.Searching.Value))
                || (Operations.Sorting != null && (!string.IsNullOrEmpty(Operations.Sorting.Column) && !string.IsNullOrEmpty(Operations.Sorting.Order)))
                || (Operations.Filtering != null && Operations.Filtering.Conditions != null)
                    )
                    totalContacts = 0;
            }

            list = list.OrderByDescending(m => m.CreatedDate).ToList();
            var Total = totalContacts > 0 ? totalContacts : list.Count();
            int no_pages = list.Count() / per_page;
            if ((list.Count - (no_pages * per_page)) > 0)
            {
                no_pages++;
            }

            List<Contacts> data = list.Skip((page - 1) * per_page).Take(per_page).ToList();
            data = data ?? new();
            var result = new
            {
                Total = Total,
                page = page,
                per_page = per_page,
                ExampleForImport = _configuration["ExampleForImport:File"],
                data = data.OrderByDescending(m => m.CreatedDate).Select(m => new
                {
                    ContactId = m.ContactId,
                    Name = m.Name,
                    CountryCode = m.CountryCode,
                    Contact = m.Contact,
                    Country = m.CountryName,
                    Email = m.Email,
                    CreatedDate = m.CreatedDate,
                    ChatStatus = m.ChatStatus,
                    OptStatus = m.IsOptIn,
                    Source = m.Source,
                    Tags = m.Tags != null ? _dbContext.Tags.Where(a => m.Tags.Contains(a.Id.ToString()) && a.IsActive).Select(m => new { Id = m.Id, Tag = m.Tag }).ToArray() : new object[0]
                })
            };

            return result;
        }

        public async Task<bool> AddContactAsync(Guid businessId, Guid userId, ContactDetailsDto contactInfo)
        {
            if (string.IsNullOrEmpty(contactInfo.Name))
            {
                throw new Exception("The contact name is required.");
            }
            if (string.IsNullOrEmpty(contactInfo.CountryCode))
            {
                throw new Exception("The contact country code is required.");
            }
            if (string.IsNullOrEmpty(contactInfo.Contact))
            {
                throw new Exception("The contact number is required");
            }
            var (contact, isValid) = PhoneNumberValidator.ValidatePhoneNumber(contactInfo.CountryCode, contactInfo.Contact);

            if (!isValid)
            {
                throw new Exception("This phone number format is not recognized. Please check the country code and phone number");
            }

            var contacts = new List<Contacts>();
            var data = new EngagetoEntities.Entities.Contacts();
            data.Name = contactInfo.Name;
            data.CountryCode = contactInfo.CountryCode;
            data.Contact = contactInfo.Contact;
            data.Email = contactInfo.Email;

            if (contactInfo.CountryName != null)
            {
                data.CountryName = contactInfo.CountryName;
            }
            List<string> tags = new List<string>();
            if (contactInfo.Tags != null && contactInfo.Tags?.Any() == true)
            {
                foreach (var tag in contactInfo.Tags)
                {
                    var tagDetail = TagDetails(businessId).FirstOrDefault(m => m.Id.ToString().ToLower() == tag.ToString().ToLower());
                    if (tagDetail != null)
                    {
                        tags.Add(tagDetail.Id.ToString());
                    }
                }
            }
            data.Tags = string.Join(",", tags);
            data.UserId = userId;
            data.BusinessId = businessId;
            data.IsActive = true;
            data.CreatedDate = DateTime.UtcNow.Date;
            data.ChatStatus = EngagetoEntities.Enums.ChatStatus.New;
            data.IsOptIn = EngagetoEntities.Enums.Is_OptIn.optin;
            data.Source = EngagetoEntities.Enums.SourceType.Direct;
            data.IsSpam = false;
            contacts.Add(data);
            var existingContacts = Get(businessId).Select(c => (c.CountryCode + c.Contact)).ToArray();
            List<CountryDetail> CountryDetails = _whatsAppBusinessAPI.CountryDetails.ToList();

            if (existingContacts.Contains((data.CountryCode + data.Contact)))
            {
                throw new Exception("The contact already exists.");
            }
            else if (!CountryDetails.Select(m => m.CountryCode).ToArray().Contains(data.CountryCode) && data.CountryCode != "")
            {
                throw new Exception("The country code does not exist.");
            }
            if (contactInfo.CountryName != null && data.CountryName != "")
            {
                if (!CountryDetails.Select(m => m.CountryName.ToLower()).ToArray().Contains(data.CountryName?.ToLower()))
                {
                    throw new Exception("The country name does not exist.");
                }
            }
            await _dbContext.Contacts.AddRangeAsync(contacts);
            await _dbContext.SaveChangesAsync();

            try
            {
                if (data.Source == SourceType.Direct)
                {
                    await _nodeWorkflowEngineService.ProcessWorkflowAsync(data, null, true);
                }
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessWorkflowFromDirect", null, $"Processing contacts: {data.ContactId}", ex.Message, ex.StackTrace);
            }

            return true;
        }

        public Object GetContactDetailsByContactIdAsync(Guid businessId, Guid userId, Guid contactId)
        {
            List<Contacts> contactsQuery = new List<Contacts>();
            var UserIdExist = _whatsAppBusinessAPI.UserRoles;
            if (UserIdExist.Select(m => m.Id).ToList().Contains(userId))
            {
                var RoleId = UserIdExist.FirstOrDefault(m => m.Id == userId)?.RoleId;
                var Role = _whatsAppBusinessAPI.Roles.FirstOrDefault(m => m.Id == RoleId)?.Name;
                if (Role == "Owner" || Role == "Admin")
                {
                    contactsQuery = Get(businessId);
                }
                else
                {
                    contactsQuery = Get(businessId).Where(m => m.UserId == userId).ToList();
                }
            }
            else
            {
                throw new Exception("The contact was not found for the specified UserId and ContactId.");
            }

            var contact = Get(businessId)
                         .Where(m => m.ContactId == contactId)
                         .FirstOrDefault();
            if (contact == null)
                throw new Exception("The contact was not found for the specified UserId and ContactId.");

            var tags = new List<string>();
            if (!string.IsNullOrEmpty(contact.Tags))
            {
                var tagIds = contact.Tags.Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t));
                tags = _dbContext.Tags
                    .Where(t => tagIds.Contains(t.Id.ToString()) &&
                               t.IsActive &&
                               t.BusinessId == businessId)
                    .Select(t => t.Tag)
                    .Where(t => !string.IsNullOrEmpty(t))
                    .ToList();
            }

            var output = new[] { new  { ContactId = contact.ContactId,
                                        Name = contact.Name,
                                        Contact = contact.CountryCode + contact.Contact,
                                        Email = contact.Email,
                                        CountryName = contact.CountryName,
                                        Tags = tags.Any() ? tags : null
                                       }
                               };
            return output;
        }

        public async Task<Stream> ContactExportAsync(Guid businessId, Guid userId)
        {
            const int batchSize = 1000;
            var memoryStream = new MemoryStream();
            var userRole = await _whatsAppBusinessAPI.UserRoles
                .Where(m => m.Id == userId)
                .Select(m => new
                {
                    RoleId = m.RoleId,
                    RoleName = _whatsAppBusinessAPI.Roles
                        .Where(r => r.Id == m.RoleId)
                        .Select(r => r.Name)
                        .FirstOrDefault()
                })
                .FirstOrDefaultAsync() ?? throw new Exception("User id not found");

            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("Contacts");
                var headers = new[] { "Name", "CountryCode", "Contact", "Email", "CountryName",
                            "Tags", "CreatedDate", "ChatStatus", "Is_OptIn" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(1, i + 1).Value = headers[i];
                }

                var query = Get(businessId).Where(m => m.IsActive);
                if (userRole.RoleName != "Admin" && userRole.RoleName != "Owner")
                {
                    query = query.Where(m => m.UserId?.ToString().ToLower() == userId.ToString().ToLower());
                }

                var tagsDict = await _dbContext.Tags
                    .Where(t => t.BusinessId == businessId)
                    .ToDictionaryAsync(t => t.Id.ToString(), t => t.Tag);

                int currentRow = 2;
                var allContacts = query.ToList();

                for (int i = 0; i < allContacts.Count; i += batchSize)
                {
                    var batch = allContacts.Skip(i).Take(batchSize);

                    foreach (var contact in batch)
                    {
                        var tags = contact.Tags != null
                            ? string.Join(",", contact.Tags
                                .Split(',')
                                .Where(tagId => tagsDict.ContainsKey(tagId))
                                .Select(tagId => tagsDict[tagId]))
                            : null;

                        worksheet.Cell(currentRow, 1).Value = contact.Name;
                        worksheet.Cell(currentRow, 2).Value = contact.CountryCode;
                        worksheet.Cell(currentRow, 3).Value = contact.Contact;
                        worksheet.Cell(currentRow, 4).Value = contact.Email;
                        worksheet.Cell(currentRow, 5).Value = contact.CountryName;
                        worksheet.Cell(currentRow, 6).Value = tags;
                        worksheet.Cell(currentRow, 7).Value = contact.CreatedDate;
                        worksheet.Cell(currentRow, 8).Value = contact.ChatStatus.ToString();
                        worksheet.Cell(currentRow, 9).Value = contact.IsOptIn.ToString();

                        currentRow++;
                    }
                }
                worksheet.Columns().AdjustToContents();
                worksheet.Row(1).Style.Font.Bold = true;
                worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                workbook.SaveAs(memoryStream);
                memoryStream.Position = 0;
            }
            return memoryStream;
        }

        public DataTable ContactExportByIdAsync(ContactsIds contactsIds, Guid businessId, Guid userId)
        {
            const int batchSize = 1000;

            var dataTable = new DataTable("Contacts");
            dataTable.Columns.Add("Name", typeof(string));
            dataTable.Columns.Add("CountryCode", typeof(string));
            dataTable.Columns.Add("Contact", typeof(string));
            dataTable.Columns.Add("Email", typeof(string));
            dataTable.Columns.Add("Country", typeof(string));
            dataTable.Columns.Add("Tags", typeof(string));
            dataTable.Columns.Add("CreatedDate", typeof(DateTime));
            dataTable.Columns.Add("ChatStatus", typeof(string));
            dataTable.Columns.Add("Is_OptIn", typeof(string));

            var userRole = _whatsAppBusinessAPI.UserRoles
                .Where(m => m.Id == userId)
                .Select(m => new
                {
                    RoleId = m.RoleId,
                    RoleName = _whatsAppBusinessAPI.Roles
                        .Where(r => r.Id == m.RoleId)
                        .Select(r => r.Name)
                        .FirstOrDefault()
                })
                .FirstOrDefault();

            if (userRole == null) return dataTable;

            var tagsDict = _dbContext.Tags
                .Where(t => t.BusinessId == businessId)
                .ToDictionary(
                    t => t.Id.ToString(),
                    t => t.Tag
                );

            var contactIds = contactsIds.ContactIds.ToHashSet();
            var query = Get(businessId)
                .Where(m => m.IsActive && contactIds.Contains(m.ContactId));

            if (userRole.RoleName != "Owner" && userRole.RoleName != "Admin")
            {
                query = query.Where(m => m.UserId?.ToString().ToLower() == userId.ToString().ToLower());
            }

            var contacts = query.ToList();
            for (int i = 0; i < contacts.Count; i += batchSize)
            {
                var batch = contacts.Skip(i).Take(batchSize);

                foreach (var contact in batch)
                {
                    var tags = contact.Tags != null
                        ? string.Join(",",
                            contact.Tags.Split(',')
                                .Where(tagId => tagsDict.ContainsKey(tagId))
                                .Select(tagId => tagsDict[tagId]))
                        : null;

                    dataTable.Rows.Add(
                        contact.Name ?? string.Empty,
                        contact.CountryCode ?? string.Empty,
                        contact.Contact ?? string.Empty,
                        contact.Email ?? string.Empty,
                        contact.CountryName ?? string.Empty,
                        tags,
                        contact.CreatedDate,
                        contact.ChatStatus.ToString(),
                        contact.IsOptIn.ToString()
                    );
                }
            }

            return dataTable;
        }

        private List<Contacts> ApplyCondition(List<Contacts> list, ContactFilterCondition condition, PropertyInfo property)
        {
            if (property != null)
            {
                if (property.Name == condition.Column)
                {
                    var conditionValues = condition.Value?.Where(v => !string.IsNullOrWhiteSpace(v)).ToArray() ?? Array.Empty<string>();

                    if (!conditionValues.Any())
                    {
                        return list;
                    }


                    switch (condition.Operator.ToLower())
                    {
                        case "equals":
                            list = list.Where(item =>
                            {
                                var value = property.GetValue(item)?.ToString();
                                return value != null && conditionValues.Contains(value, StringComparer.OrdinalIgnoreCase);
                            }).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item =>
                            {
                                var value = property.GetValue(item)?.ToString();
                                return value != null && conditionValues.Contains(value, StringComparer.OrdinalIgnoreCase);
                            }).ToList();
                            break;

                        case "contains":
                            if (condition.Value != null && !condition.Value.SequenceEqual(Array.Empty<string>()))
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item) as string;
                                    if (value != null)
                                    {
                                        var valueArray = value.Split(",");
                                        return conditionValues.Any(conditionValue => valueArray.Contains(conditionValue));
                                    }
                                    return false;
                                }).ToList();
                            break;


                        default:
                            break;
                    }
                }

            }
            return list;

        }

        public List<Contacts> Get(Guid BusinessId)
        {
            List<Contacts> contacts = new List<Contacts>();

            contacts.AddRange(_dbContext.Contacts.Where((m => m.BusinessId == BusinessId && m.IsActive == true)).AsEnumerable());
            return contacts;

        }

        public async Task AddAll(List<Contacts> contacts)
        {
            try
            {
                if (contacts == null || !contacts.Any())
                {
                    throw new ArgumentException("The contacts list cannot be null or empty.", nameof(contacts));
                }

                using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                {
                    try
                    {
                        await _dbContext.Contacts.AddRangeAsync(contacts);
                        await _dbContext.SaveChangesAsync();

                        var tableName = StringHelper.GetTableName<ChatStatusEntity>();
                        var columns = StringHelper.GetPropertyNames<ChatStatusEntity>();
                        var chatStatusEntities = contacts.Select(x => new EngagetoEntities.Entities.ChatStatusEntity(x.ContactId, EngagetoEntities.Enums.ChatStatus.New, x.UserId)).ToList();

                        await _genericRepository.InsertRecordsAsync(tableName, columns, chatStatusEntities);

                        await transaction.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        throw new Exception($"Error adding Contacts: {ex.Message}", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding Contacts: {ex.Message}");
            }
        }
        public void ContactEdit(Contacts Contact)
        {
            try
            {
                _dbContext.Contacts.Update(Contact);
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding Contacts: {ex.Message}");
            }
        }
        public void DeActivateContacts(Guid[] list)
        {
            try
            {
                _dbContext.Contacts
                    .Where(m => list.Contains(m.ContactId))
                    .ToList()
                    .ForEach(contact => contact.IsActive = false);

                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating Contacts: {ex.Message}", ex);
            }
        }

        public void ContactTagsUpdate(Guid[] list, List<Guid> ContactIds)
        {
            try
            {
                foreach (var contactId in ContactIds)
                {
                    var contact = _dbContext.Contacts.FirstOrDefault(m => m.ContactId == contactId);
                    if (contact == null)
                    {
                        continue;
                    }

                    var existingTags = contact.Tags?.Split(',') ?? Array.Empty<string>();
                    for (int i = 0; i < list.Length; i++)
                    {
                        var tag = list[i].ToString();
                        if (!existingTags.Contains(tag))
                        {
                            existingTags = existingTags.Append(tag).ToArray();
                        }
                    }

                    contact.Tags = string.Join(",", existingTags.Where(tag => !string.IsNullOrEmpty(tag)).ToList());
                    _dbContext.Update(contact);
                }
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating Contact Tags: {ex.Message}");
            }
        }

        public void TagsCreate(string Tag, Guid BusinessId, Guid UserId)
        {

            var IsTagExist = _dbContext.Tags
                  .Any(t => t.BusinessId == BusinessId
                  && t.IsActive
                  && t.Tag.ToLower() == Tag.ToLower());

            if (IsTagExist) throw new InvalidOperationException("The tag already exists. Please try another tag name.");

            var data = new Tags();
            data.Tag = Tag;
            data.IsActive = true;
            data.UserId = UserId;
            data.BusinessId = BusinessId;
            data.CreatedAt = DateTime.UtcNow;
            data.CreatedBy = UserId.ToString();
            data.UpdatedBy = UserId.ToString();
            _dbContext.Tags.Add(data);
            _dbContext.SaveChanges();

        }

        public List<Tags> TagDetails(Guid BusinessId)
        {
            try
            {
                var Users = _whatsAppBusinessAPI.Users;
                var tagsWithUserNames = _dbContext.Tags
                                        .Where(m => m.BusinessId == BusinessId && m.IsActive == true)
                                        .AsEnumerable()
                                        .Select(tag => new Tags
                                        {
                                            Id = tag.Id,
                                            Tag = tag.Tag,
                                            CreatedAt = tag.CreatedAt,
                                            UpdatedAt = tag.UpdatedAt,
                                            DeletedAt = tag.DeletedAt,
                                            CreatedBy = Users.FirstOrDefault(u => u.Id.ToString().ToLower() == tag.CreatedBy.ToLower())?.Name ?? "",
                                            UpdatedBy = Users.FirstOrDefault(u => u.Id.ToString().ToLower() == tag.UpdatedBy.ToLower())?.Name ?? "",
                                            UserId = tag.UserId,
                                            BusinessId = tag.BusinessId,
                                            IsActive = tag.IsActive,
                                        }).ToList();
                return tagsWithUserNames;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting tag details: {ex.Message}");
            }
        }


        public List<Tags> GetTagDetailsAsync(Guid BusinessId, Guid userId, string search)
        {

            var tags = new List<EngagetoEntities.Entities.Tags>();
            var userRoles = _whatsAppBusinessAPI.UserRoles;

            if (userRoles.Select(m => m.Id).ToList().Contains(userId))
            {
                var roleId = userRoles.FirstOrDefault(m => m.Id == userId)?.RoleId;
                var role = _whatsAppBusinessAPI.Roles.FirstOrDefault(m => m.Id == roleId)?.Name;
                tags = TagDetails(BusinessId).ToList();
                if (!string.IsNullOrEmpty(search))
                {
                    tags = tags.Where(t => t.ToString().ToLower().Contains(search.ToLower()) && t.IsActive == true).ToList();
                }
            }
            return tags;
        }

        public object ContactsDetailsByTag(Guid[] list, Guid BusinessId, Guid userId)
        {
            List<Contacts> data = new List<Contacts>();
            var UserIdExist = _whatsAppBusinessAPI.UserRoles;
            if (!UserIdExist.Select(m => m.Id).ToList().Contains(userId)) throw new Exception("UserId does not exist!");
            var RoleId = UserIdExist.FirstOrDefault(m => m.Id == userId)?.RoleId;
            var Role = _whatsAppBusinessAPI.Roles.FirstOrDefault(m => m.Id == RoleId)?.Name;

            List<string> Tag = _dbContext.Tags.Where(m => m.BusinessId == BusinessId && list.Contains(m.Id)).Select(m => m.Id.ToString().ToLowerInvariant()).ToList();
            foreach (var Contacts in _dbContext.Contacts.ToList())
            {
                if (Contacts.Tags != null)
                    foreach (var tag in Contacts.Tags.Split(',').ToArray())
                    {
                        if (Tag.Contains(tag))
                        {
                            if (!data.Contains(Contacts))
                            {
                                data.Add(Contacts);
                            }
                        }
                    }
            }

            if (Role == "Admin" || Role == "Owner")
            {
                data.Where(m => m.IsActive).ToList();
            }
            else
            {
                data.Where(m => m.UserId?.ToString().ToLower() == userId.ToString().ToLower() && m.IsActive).ToList();
            }

            var output = data.Select(m => new
            {
                ContactId = m.ContactId,
                Name = m.Name,
                Contact = m.CountryCode + m.Contact,
                Email = m.Email,
                Tags = _dbContext.Tags.Where(tag => m.Tags != null && m.Tags.Contains(tag.Id.ToString())).Select(tag => tag.Tag).ToArray().Where(tag => !string.IsNullOrEmpty(tag))
            });

            return output;
        }

        public bool TagRemove(Guid TagId, Guid UserId)
        {
            try
            {
                var status = 0;
                var TagToRemove = _dbContext.Tags.FirstOrDefault(m => m.Id == TagId);
                if (TagToRemove != null)
                {
                    TagToRemove.DeletedAt = DateTime.UtcNow;
                    TagToRemove.DeletedBy = UserId.ToString();
                    TagToRemove.IsActive = false;
                    _dbContext.Tags.Update(TagToRemove);
                    status = _dbContext.SaveChanges();
                }
                if (status > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public bool TagEdit(TagRequestDto tag, Guid UserId)
        {
            try
            {
                var status = 0;
                var TagToEdit = _dbContext.Tags.FirstOrDefault(m => m.Id == tag.Id);
                if (TagToEdit != null)
                {
                    TagToEdit.UpdatedAt = DateTime.UtcNow;
                    TagToEdit.UpdatedBy = UserId.ToString();
                    TagToEdit.Tag = tag.Tag ?? string.Empty;
                    _dbContext.Tags.Update(TagToEdit);
                    status = _dbContext.SaveChanges();
                }
                if (status > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public void NoteAddToContact(List<string> Note, Guid ContactId)
        {
            List<string> NoteId = new List<string>();

            foreach (var note in Note)
            {
                Notes obj = new Notes();
                obj.Note = note;
                EntityEntry<Notes> data = _dbContext.Notes.Add(obj);
                obj.IsActive = true;
                _dbContext.SaveChanges();
                NoteId.Add(data.Entity.Id.ToString());
            }

            var Contact = _dbContext.Contacts.FirstOrDefault(m => m.ContactId == ContactId);

            if (!string.IsNullOrEmpty(Contact?.Note))
            {
                var noteArray = Contact.Note.Split(',').Select(n => n.Trim()).ToArray();
                NoteId.AddRange(noteArray);
            }

            Contact.Note = string.Join(",", NoteId);
            _dbContext.Update(Contact);
            _dbContext.SaveChanges();
        }
        public void NoteRemoveFromContact(List<string> Note, Guid ContactId)
        {
            var contact = _dbContext.Contacts.FirstOrDefault(c => c.ContactId == ContactId);
            if (contact == null)
            {
                return;
            }
            var existingNoteIds = contact.Note?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                     .Select(id => id.ToLower())
                                     .ToList();
            var updatedNoteIds = existingNoteIds?.Except(Note.Select(m => m.ToLower()).ToList());

            foreach (var noteIdToRemove in Note)
            {
                var note = _dbContext.Notes.FirstOrDefault(n => n.Id.ToString() == noteIdToRemove);
                if (note != null)
                {
                    note.IsActive = false;
                    _dbContext.Notes.Update(note);
                }
            }

            contact.Note = string.Join(",", updatedNoteIds);
            _dbContext.Contacts.Update(contact);
            _dbContext.SaveChanges();
        }
        public void ContactTagsRemove(string[] list, List<Guid> ContactIds)
        {
            try
            {
                foreach (var contactId in ContactIds)
                {
                    var contact = _dbContext.Contacts.FirstOrDefault(m => m.ContactId == contactId);
                    if (contact == null)
                    {
                        continue;
                    }

                    var existingTags = contact.Tags?.ToLower().Split(',') ?? Array.Empty<string>();
                    var tagsToRemove = _dbContext.Tags
                        .Where(m => list.Contains(m.Tag.ToLower()))
                        .Select(m => m.Id.ToString().ToLower())
                        .ToList();

                    existingTags = existingTags.Except(tagsToRemove).ToArray();
                    contact.Tags = string.Join(",", existingTags);
                    _dbContext.Update(contact);

                }
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error removing Contact Tags: {ex.Message}");
            }
        }

        public async Task<List<Contacts>> GetContactByContactNumber(string contactNumber, string BusinessId)
        {
            if (string.IsNullOrWhiteSpace(contactNumber))
                return null;

            string normalizedContactNumber = contactNumber.Replace("+", "");

            return await _dbContext.Contacts
                .Where(x => (x.CountryCode + x.Contact).Replace("+", "").Contains(normalizedContactNumber) && x.BusinessId.ToString().ToLower() == BusinessId.ToLower()).ToListAsync();
        }

        public async Task<Contacts> SaveContactNumber(
            string countryCode,
            string contactNo,
            string businessId,
            SourceType source,
            string? name = null,
            Guid? userId = null)
        {
            var contactDetails = await GetContactByContactNumber(contactNo, businessId);

            if (contactDetails.Any())
            {
                foreach (var contact in contactDetails)
                {
                    contact.Name = name ?? contact.Name;
                    contact.IsActive = true;
                    contact.LastMessageAt = DateTime.UtcNow;
                    _dbContext.Update(contact);
                }
            }
            else
            {
                Guid.TryParse(businessId, out Guid companyId);
                var contact = new Contacts
                {
                    Name = name ?? "Unknown",
                    Contact = contactNo,
                    BusinessId = companyId,
                    UserId = userId,
                    CountryCode = $"+{countryCode?.Replace("+", "") ?? "91"}",
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    LastMessageAt = DateTime.UtcNow,
                    IsOptIn = Is_OptIn.optin,
                    Source = source
                };
                await _dbContext.AddAsync(contact);
                await _dbContext.SaveChangesAsync();

                await _nodeWorkflowEngineService.ProcessWorkflowAsync(contact, null, true);
                contactDetails = new List<Contacts> { contact };
            }

            return contactDetails.First();
        }

        public void UpdateContactAsync(ContactDetailsDto Contact, Guid contactId, Guid businessId, Guid userId)
        {
            try
            {
                if (string.IsNullOrEmpty(Contact.Name))
                {
                    throw new Exception("The contact name is required.");
                }
                if (string.IsNullOrEmpty(Contact.CountryCode))
                {
                    throw new Exception("The contact country code is required.");
                }

                if (string.IsNullOrEmpty(Contact.Contact))
                {
                    throw new Exception("The contact number is required");
                }
                if (!string.IsNullOrEmpty(Contact.Email))
                {
                    if (!StringHelper.IsValidEmail(Contact.Email))
                    {
                        throw new InvalidOperationException("Email is not valid.");
                    }
                }
                var (contact, isValid) = PhoneNumberValidator.ValidatePhoneNumber(Contact.CountryCode, Contact.Contact);

                if (!isValid)
                {
                    throw new Exception("This phone number format is not recognized. Please check the country code and phone number");
                }
                var data = Get(businessId).FirstOrDefault(m => m.ContactId == contactId);
                var Contacts = new List<Contacts>();
                var existingContacts = Get(businessId).Select(c => (c.CountryCode + c.Contact)).ToList();
                List<CountryDetail> CountryDetails = _whatsAppBusinessAPI.CountryDetails.ToList();

                if (data != null && data.Contact != null)
                {
                    existingContacts.Remove((data.CountryCode + data.Contact));
                }
                data.Name = Contact.Name;
                data.CountryCode = Contact.CountryCode;

                data.Contact = Contact.Contact;
                data.Email = Contact.Email;

                if (Contact.CountryName != null)
                {
                    data.CountryName = Contact.CountryName;
                }

                List<string> tags = new List<string>();
                if (Contact.Tags != null && Contact.Tags.Count != 0)
                {
                    foreach (var tag in Contact.Tags)
                    {
                        var tagDetail = TagDetails(businessId).FirstOrDefault(m => m.Id.ToString().ToLower() == tag.ToString().ToLower());
                        if (tagDetail != null)
                        {
                            tags.Add(tagDetail.Id.ToString());
                        }
                    }
                }
                data.Tags = string.Join(",", tags);

                if (existingContacts.Contains(data.CountryCode + data.Contact))
                {
                    throw new Exception("The contact already exists.");
                }
                else if (!CountryDetails.Select(m => m.CountryCode).ToArray().Contains(data.CountryCode) && data.CountryCode != "")
                {
                    throw new Exception("The contact format is not valid.");
                }

                if (Contact.CountryName != null && data.CountryName != "")
                {
                    if (!CountryDetails.Select(m => m.CountryName.ToLower()).ToArray().Contains(data.CountryName?.ToLower()))
                    {
                        throw new Exception("The country name does not exist.");
                    }
                }
                _dbContext.Contacts.Update(data);
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding Contacts: {ex.Message}");
            }
        }

        public async Task<PaginatedResult<ContactResponseDto>> GetAllDeletedContactsAsync(ContactOperationsDto operationsDto, int pageNumber, int pageSize)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var query = _dbContext.Contacts
                    .Where(i => i.BusinessId.ToString() == businessId && i.IsActive == false)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(operationsDto?.Searching?.Value))
                {
                    var searchValue = operationsDto.Searching.Value.ToLower();
                    query = query.Where(m =>
                        m.Name.ToLower().Contains(searchValue) ||
                        m.Contact.ToLower().Contains(searchValue) ||
                        (m.Email != null && m.Email.ToLower().Contains(searchValue)));
                }

                if (operationsDto?.Sorting != null)
                {
                    var property = typeof(Contacts).GetProperty(operationsDto.Sorting.Column,
                        BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                    if (property != null)
                    {
                        query = operationsDto.Sorting.Column.ToLower() == "asc"
                            ? query.OrderBy(x => EF.Property<object>(x, property.Name))
                            : query.OrderByDescending(x => EF.Property<object>(x, property.Name));
                    }
                    else
                    {
                        query = query.OrderByDescending(x => x.CreatedDate);
                    }
                }
                else
                {
                    query = query.OrderByDescending(x => x.CreatedDate);
                }

                var allDeletedContacts = await query.ToListAsync();

                if (operationsDto?.Filtering?.FilterType != null && operationsDto.Filtering.Conditions.Any())
                {
                    if (operationsDto.Filtering.FilterType.ToLower() == "and")
                    {
                        foreach (var condition in operationsDto.Filtering.Conditions)
                        {
                            var property = typeof(Contacts).GetProperty(condition.Column,
                                BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                                allDeletedContacts = ApplyCondition(allDeletedContacts, condition, property);
                        }
                    }
                    else if (operationsDto.Filtering.FilterType.ToLower() == "or")
                    {
                        var filteredList = new List<Contacts>();
                        foreach (var condition in operationsDto.Filtering.Conditions)
                        {
                            var property = typeof(Contacts).GetProperty(condition.Column,
                                BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                            {
                                var partialResult = ApplyCondition(allDeletedContacts, condition, property);
                                filteredList.AddRange(partialResult.Where(x => !filteredList.Contains(x)));
                            }
                        }
                        allDeletedContacts = filteredList;
                    }
                }

                var totalCount = allDeletedContacts.Count;

                var contacts = allDeletedContacts
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize);

                // Step 2: Extract all tag IDs from contacts
                var tagIds = contacts
                    .SelectMany(x => !string.IsNullOrEmpty(x.Tags)
                        ? x.Tags.Split(',')
                            .Where(tag => !string.IsNullOrWhiteSpace(tag))
                            .Select(tag =>
                            {
                                if (Guid.TryParse(tag, out Guid tagGuid))
                                    return tagGuid;
                                return Guid.Empty;
                            })
                            .Where(guid => guid != Guid.Empty)
                        : Array.Empty<Guid>())
                    .Distinct();

                // Step 3: Fetch tags from DB and convert to dictionary
                var tagDict = _dbContext.Tags
                    .Where(t => tagIds.Contains(t.Id) && t.IsActive)
                    .Select(t => new TagDto { Id = t.Id, TagName = t.Tag })
                    .ToDictionary(t => t.Id, t => t);

                // Step 4: Map each contact and attach the tag list
                var contactDtos = contacts.Select(x =>
                {
                    var dto = x.Adapt<ContactResponseDto>();

                    var individualTagIds = !string.IsNullOrEmpty(x.Tags)
                        ? x.Tags.Split(',')
                            .Where(tag => !string.IsNullOrWhiteSpace(tag))
                            .Select(tag =>
                            {
                                if (Guid.TryParse(tag, out Guid tagGuid))
                                    return tagGuid;
                                return Guid.Empty;
                            })
                            .Where(guid => guid != Guid.Empty)
                        : Enumerable.Empty<Guid>();

                    dto.Tags = individualTagIds
                        .Where(tagDict.ContainsKey)
                        .Select(id => tagDict[id])
                        .ToList();

                    return dto;
                }).ToList();

                return new PaginatedResult<ContactResponseDto>
                {
                    Total = totalCount,
                    Page = pageNumber,
                    PerPage = pageSize,
                    Items = contactDtos
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving deleted contacts: {ex.Message}", ex);
            }
        }


        public async Task<bool> RestoreDeletedContactAsync(Guid businessId, Guid userId, Guid[] contactIds)
        {
            if (contactIds == null || !contactIds.Any())
            {
                throw new Exception("No contact IDs were provided for restoration.");
            }
            var contactsToRestore = await _dbContext.Contacts.Where(c => c.BusinessId == businessId && contactIds.Contains(c.ContactId) && !c.IsActive).ToListAsync();

            if (!contactsToRestore.Any())
                return false;

            foreach (var contact in contactsToRestore)
            {
                contact.IsActive = true;
            }
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<ContactImportTracker> AddAsync(ContactImportTracker entity)
        {
            var res = await _dbContext.ContactImportTrackers.AddAsync(entity);
            await _dbContext.SaveChangesAsync();
            return res.Entity;
        }

        public async Task<ContactImportTracker> UpdateAsysnc(ContactImportTracker entity)
        {
            var res = _dbContext.ContactImportTrackers.Update(entity);
            await _dbContext.SaveChangesAsync();
            return res.Entity;
        }

        public async Task<ContactImportTracker> GetByIdAysnc(Guid Id)
        {
            var contactImportTracker = await _dbContext.ContactImportTrackers.FirstOrDefaultAsync(i => i.Id == Id);
            return contactImportTracker ?? new();

        }
        public async Task<(Contacts, string)> GetContactByContactNumberAndBusinessId(Guid businessId, ContactDetailsDto contactDetails, Guid userId)
        {
            try
            {
                string normalizedContactNumber = (contactDetails.CountryCode + contactDetails.Contact).Replace("+", "").ToLower();
                var existingContact = await _dbContext.Contacts.FirstOrDefaultAsync(x => (x.CountryCode + x.Contact).Replace("+", "").ToLower().Contains(normalizedContactNumber) && x.BusinessId == businessId);
                if (existingContact != null)
                {
                    return (existingContact, "Contact already exists.");
                }
                var newContact = new Contacts
                {
                    BusinessId = businessId,
                    UserId = userId,
                    Name = contactDetails.Name,
                    CountryCode = contactDetails.CountryCode,
                    Contact = contactDetails.Contact,
                    CountryName = contactDetails.CountryName,
                    Email = contactDetails.Email,
                    Tags = contactDetails.Tags?.FirstOrDefault(),
                    Source = contactDetails.Source,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };

                if (!string.IsNullOrWhiteSpace(newContact.CountryCode))
                {
                    newContact.CountryCode = newContact.CountryCode.StartsWith("+")
                        ? newContact.CountryCode
                        : $"+{newContact.CountryCode.Trim()}";
                }

                var result = await _dbContext.Contacts.AddAsync(newContact);
                await _dbContext.SaveChangesAsync();
                var contacts = result.Entity;
                return (contacts, "Contact added succefully.");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<Contacts>> GetContactByIds(ContactsIdsDto contactIds)
        {
            var businessId = _userIdentityService.BusinessId;
            var contacts = await _dbContext.Contacts
                .Where(c => contactIds.ContactIds != null && contactIds.ContactIds.Contains(c.ContactId.ToString()) && c.BusinessId.ToString() == businessId && c.IsActive)
                .ToListAsync();

            return contacts;

        }

        public async Task<(string Message, Contacts Contact)> CreateLeadratContact(LeadratLeadDto leadDto, string businessId)
        {
            try
            {
                var phoneDetails = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(leadDto.PhoneNumber);
                string countryCode = phoneDetails.CountryCode;
                string phoneNumber = leadDto.PhoneNumber.Length > 10 ? phoneDetails.NationalNumber.Replace(countryCode, "") : leadDto.PhoneNumber;

                var assignTo = !string.IsNullOrEmpty(leadDto.AssignTo) ? PhoneNumberHelper.GetCountryCodeAndPhoneNumber(leadDto.AssignTo) : new();
                string assignToCountryCode = assignTo.CountryCode;
                string assignToPhoneNumber = (!string.IsNullOrEmpty(leadDto.AssignTo) && leadDto.AssignTo.Length > 10) ? assignTo.NationalNumber.Replace(countryCode, "") : leadDto.AssignTo ?? string.Empty;

                var existingContact = await _dbContext.Contacts.Where(i => i.BusinessId == Guid.Parse(businessId) && (i.CountryCode + i.Contact).Replace("+", "") == (countryCode + phoneNumber).Replace("+", "")).FirstOrDefaultAsync();
                var existingStatus = existingContact?.LeadStatus;
                var existingSubStatus = existingContact?.LeadSubStatus;
                var existingProject = existingContact?.Project;

                var existAssignToContact = await _dbContext.Users.Where(i => i.CompanyId == businessId && (i.CountryCode + i.PhoneNumber).Replace("+", "") == (assignToCountryCode + assignToPhoneNumber).Replace("+", "")).FirstOrDefaultAsync();

                if (existingContact != null)
                {
                    existingContact.Name = !string.IsNullOrEmpty(leadDto.Name) ? leadDto.Name : existingContact.Name;
                    existingContact.UserId = existAssignToContact != null ? existAssignToContact.Id : Guid.Empty;
                    existingContact.Email = !string.IsNullOrEmpty(leadDto.Email) ? leadDto.Email : existingContact.Email;
                    existingContact.Source = SourceType.LeadRat;
                    existingContact.SubSource = leadDto.SubSource;
                    existingContact.Note = leadDto.Notes ?? string.Empty;
                    existingContact.Contact = phoneNumber;
                    existingContact.CountryCode = countryCode.StartsWith("+") ? countryCode : $"+{countryCode}";
                    existingContact.Project = leadDto.Project;
                    existingContact.LeadStatus = leadDto.Status;
                    existingContact.LeadSubStatus = leadDto.SubStatus;
                    existingContact.ScheduledAt = leadDto.ScheduledAt;
                    existingContact.IsActive = true;
                    existingContact.IsSpam = false;
                    existingContact.IsOptIn = Is_OptIn.optin;

                    var tableName = StringHelper.GetTableName<Contacts>();
                    var columns = typeof(Contacts)
                                  .GetProperties()
                                  .Select(p => p.Name)
                                  .ToList();
                    var conditions = new Dictionary<string, object>
                                      {
                                         { "ContactId", existingContact.ContactId }
                                      };

                    await _genericRepository.UpdateRecordAsync(tableName, columns, existingContact, conditions);


                    if (leadDto.Status != existingStatus || leadDto.SubStatus != existingSubStatus)
                    {
                        try
                        {
                            await RunInBackground(() => _nodeWorkflowEngineService.ProcessWorkflowAsync(existingContact, null, false, true));
                        }
                        catch (Exception ex)
                        {
                            await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessWorkflowStatusChange", null, $"Processing contacts: {existingContact.ContactId}", ex.Message, ex.StackTrace);
                        }
                    }
                    if (leadDto.Project != existingProject)
                    {
                        try
                        {
                            await RunInBackground(() => _nodeWorkflowEngineService.ProcessWorkflowAsync(existingContact, null, false, false, true));
                        }
                        catch (Exception ex)
                        {
                            await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessWorkflowProjectChange", null, $"Processing contacts: {existingContact.ContactId}", ex.Message, ex.StackTrace);
                        }
                    }

                    return ("Contact updated successfully", existingContact);
                }
                else
                {
                    var newContact = new Contacts
                    {
                        ContactId = Guid.NewGuid(),
                        Name = leadDto.Name ?? countryCode + phoneNumber,
                        CountryCode = countryCode.StartsWith("+") ? countryCode : $"+{countryCode}",
                        Contact = phoneNumber,
                        Email = leadDto.Email,
                        BusinessId = Guid.Parse(businessId),
                        UserId = existAssignToContact != null ? existAssignToContact.Id : Guid.Empty,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        IsOptIn = Is_OptIn.optin,
                        Source = SourceType.LeadRat,
                        SubSource = leadDto.SubSource,
                        ChatStatus = EngagetoEntities.Enums.ChatStatus.New,
                        Note = leadDto.Notes,
                        IsSpam = false,
                        Project = leadDto.Project,
                        LeadStatus = leadDto.Status,
                        LeadSubStatus = leadDto.SubStatus,
                        ScheduledAt = leadDto.ScheduledAt
                    };

                    var tableName = StringHelper.GetTableName<Contacts>();
                    var columns = StringHelper.GetPropertyNames<Contacts>();

                    await _genericRepository.InsertRecordsAsync(tableName, columns, new List<Contacts> { newContact });

                    var chatStatusEntity = new ChatStatusEntity(newContact.ContactId, EngagetoEntities.Enums.ChatStatus.New, null);
                    var chatStatusTableName = StringHelper.GetTableName<ChatStatusEntity>();
                    var chatStatusColumns = StringHelper.GetPropertyNames<ChatStatusEntity>();

                    await _genericRepository.InsertRecordsAsync(chatStatusTableName, chatStatusColumns, new List<ChatStatusEntity> { chatStatusEntity });

                    try
                    {
                        await RunInBackground(() => _nodeWorkflowEngineService.ProcessWorkflowAsync(newContact, null, true, false));
                    }
                    catch (Exception ex)
                    {
                        await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessWorkflowFromLeadRat", null, $"Processing contacts: {newContact.ContactId}", ex.Message, ex.StackTrace);
                    }

                    return ("Contact created successfully", newContact);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error while creating/updating contact: {ex.Message}", ex);
            }
        }

        public async Task<List<object>> GetAllSubSourcesBySourceAsync(Guid businessId)
        {
            var account = await _dbContext.BusinessDetails.Where(i => i.Id == businessId).FirstOrDefaultAsync();
            if (account == null) throw new Exception("Business with given business id not found!");

            var result = Enum.GetValues(typeof(SourceType)).Cast<SourceType>()
                .Select(source => new
                {
                    Source = source.ToString(),
                    SubSources = source == SourceType.LeadRat
                        ? Enum.GetValues(typeof(LeadSource)).Cast<LeadSource>()
                              .Select(subSource => subSource.ToString())
                              .ToList()
                        : new List<string>()
                })
                .ToList<object>();

            return result;
        }

        public async Task<bool> BlockContactsAsync(Guid contactId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var contact = await _dbContext.Contacts
                    .FirstOrDefaultAsync(c => c.ContactId == contactId && c.BusinessId.ToString() == businessId);

                if (contact == null)
                    return false;

                // **BLOCK CONTACT VIA META API**
                var fullPhoneNumber = $"{contact.CountryCode}{contact.Contact}";
                var (success, result) = await _metaApiService.BlockContactAsync(businessId, fullPhoneNumber);

                if (success)
                {
                    // **UPDATE DATABASE ONLY IF META API SUCCEEDS**
                    contact.IsSpam = true;
                    await _dbContext.SaveChangesAsync();

                    Console.WriteLine($"Successfully blocked contact {fullPhoneNumber} via Meta API");
                    return true;
                }
                else
                {
                    Console.WriteLine($"Failed to block contact {fullPhoneNumber}: {result}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error blocking contacts: {ex.Message}");
            }
        }

        private async Task RunInBackground(Func<Task> action)
        {
            try
            {
                await Task.Run(action);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message); ;
            }
        }
    }

    /// <summary>
    /// Result of blocking a contact via Meta API
    /// </summary>
    public class ContactBlockResult
    {
        public Guid ContactId { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}


