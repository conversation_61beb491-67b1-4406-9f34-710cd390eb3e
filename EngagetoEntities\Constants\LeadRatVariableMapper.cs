﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Constants
{
    public static class LeadRatVariableMapper
    {
        public static Dictionary<string, string> LeadVariableMapping = new()
        {
            { "#leadName#", "Name" },
            { "#LeadName#", "Name" },
            { "#leadContact#", "ContactNo" },
            { "#LeadContact#", "ContactNo" },
            { "#LeadEmail#", "Email" },
            { "#leadEmail#", "Email" },
            { "#leadCreatedOn#", "CreatedOn" },
            { "#propertyName#", "Properties[0].Title" },
            { "#PropertyName#", "Properties[0].Title" },
            { "#note#", "Notes" },
            { "#Note#", "Notes" },
            { "#noOfBhk#", "Properties[0].NoOfBHK" },
            { "#NoOfBhk#", "Properties[0].NoOfBHK" },
            { "#bhkType#", "Properties[0].BHKType" },
            { "#BhkType#", "Properties[0].BHKType" },
            { "#BhkTypes#", "Properties[0].BHKType" },
            { "#AssignedTo#", "AssignTo" },
            { "#AlternateContactNo#", "AlternateContactNo" },
            { "#leadCreatedBy#", "CreatedBy" },
            { "#enquiredFor#", "Properties[0].EnquiredFor" },
            { "#AssignedBy#", "AssignedFrom" },
            { "#leadSource#", "Enquiries[0].LeadSource" },
            { "#enquiredLocation#", "Enquiries[0].Address" },
            { "#enquiredType#", "Enquiries[0].EnquiredFor" },
            { "#Project#", "Projects[0].Name" },
            { "#date#", "ScheduledDate"},
            { "#Schedule Date#","ScheduledDate"},
            { "#ScheduleTime#","ScheduledDate" },
            { "#projectName#", "Projects[0].Name"},
            { "#Projects#","Projects[0].Name" },
            { "#Properties#","Properties[0].Title" },
            { "#LowerBudget#", "Enquiry.LowerBudget"},
            { "#UpperBudget#", "Enquiry.UpperBudget"},
            { "#enquiryType#", ".EnquiryTypes[0]"},
            { "#EnquiryTypes#","Enquiry.EnquiryTypes[0]"},
            { "#EnquiredFor#", "Properties[0].EnquiredFor"},
            { "#Lead Source#", "Enquiries[0].LeadSource"},
            { "#Sub Source#", "Enquiry.PropertyType.ChildType.DisplayName"},
            { "#Property Type#", "PropertyType.DisplayName"},
            { "#time#", "ScheduledDate" },
            { "#propertyType#","PropertyType.DisplayName"},
            { "#propertySubtype#","Enquiry.PropertyType.ChildType.DisplayName" },
            { "#carpetArea#","Enquiry.CarpetArea,Enquiry.CarpetAreaUnit" },
            { "#CarpetArea#","Enquiry.CarpetArea,Enquiry.CarpetAreaUnit" },
            { "#ChannelPartnerName#", "ChannelPartners[0].FirmName" },
            { "#channelPartnerName#", "ChannelPartners[0].FirmName" }
        };
        public static Dictionary<string, string> UserDetaislMapping = new()
        {
            {"#userName#","UserName" },
            {"#UserName#","UserName" },
            {"#userEmail#","Email" },
            {"#UserEmail#","Email" },
            {"#userPhoneNumber#","PhoneNumber" },
            {"#UserPhoneNumber#","PhoneNumber" },
            {"#userMobile#","PhoneNumber" },
            {"#Assign To#","FirstName,LastName" },
            {"#Primary Owner#","FirstName,LastName" }
        };

    }
}
