# Build the Docker image
Write-Host "Building Docker image..." -ForegroundColor Green
docker build -t engageto-meta:latest -f Engageto.Meta/Dockerfile .

# Tag the image for Azure Container Registry
Write-Host "Tagging image for Azure Container Registry..." -ForegroundColor Green
docker tag engageto-meta:latest lrbcontainer.azurecr.io/engageto-meta:latest

# Log in to Azure
Write-Host "Logging in to Azure..." -ForegroundColor Green
az login

# Log in to Azure Container Registry
Write-Host "Logging in to Azure Container Registry..." -ForegroundColor Green
az acr login --name lrbcontainer

# Push the image to Azure Container Registry
Write-Host "Pushing image to Azure Container Registry..." -ForegroundColor Green
docker push lrbcontainer.azurecr.io/engageto-meta:latest

# Update the Azure App Service to use the new container image
Write-Host "Updating Azure App Service..." -ForegroundColor Green
az webapp config container set --name dev-enageto-meta --resource-group az-lrb-dev --docker-custom-image-name lrbcontainer.azurecr.io/engageto-meta:latest --docker-registry-server-url https://lrbcontainer.azurecr.io

# Configure app settings for Azure App Service
Write-Host "Configuring App Service settings..." -ForegroundColor Green
az webapp config appsettings set --name dev-enageto-meta --resource-group az-lrb-dev --settings WEBSITES_PORT=80 ASPNETCORE_ENVIRONMENT=Production

# Restart the app service to apply changes
Write-Host "Restarting App Service..." -ForegroundColor Green
az webapp restart --name dev-enageto-meta --resource-group az-lrb-dev

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Waiting for the application to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check if the application is running
Write-Host "Checking application health..." -ForegroundColor Green
$healthUrl = "https://dev-enageto-meta.azurewebsites.net/health"
try {
    $response = Invoke-WebRequest -Uri $healthUrl -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "Application is healthy!" -ForegroundColor Green
    } else {
        Write-Host "Application returned status code: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "Failed to connect to the application: $_" -ForegroundColor Red
    Write-Host "You may need to check the logs in the Azure Portal." -ForegroundColor Yellow
}
