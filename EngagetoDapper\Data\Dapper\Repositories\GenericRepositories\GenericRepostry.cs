﻿using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;
using System.Collections;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;

namespace EngagetoDapper.Data.Dapper.Repositories.GenericRepositories
{
    public class GenericRepostry : IGenericRepository
    {
        private readonly IUnitOfWork _unitOfWork;
        public GenericRepostry(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task<List<T>> GetAllAsync<T>()
        {
            var compiler = new SqlServerCompiler();
            var tableName = GetTableName<T>(); // Get table name dynamically
            var db = new QueryFactory(_unitOfWork.Connection, compiler);
            var query = new Query(tableName);

            var result = await db.GetAsync<T>(query);
            return result.ToList();
        }

        public async Task<List<T>> GetByGuidIdAsync<T>(Dictionary<Guid, string> filterValues)
        {
            var compiler = new SqlServerCompiler();
            var tableName = GetTableName<T>(); // Get table name dynamically
            var db = new QueryFactory(_unitOfWork.Connection, compiler);
            var query = new Query(tableName);

            foreach (var filter in filterValues)
            {
                var columnName = filter.Value;
                var id = filter.Key;
                query.Where(columnName, "=", id);
            }

            var result = await db.GetAsync<T>(query);
            return result.ToList();
        }

        public async Task<List<T>> GetByObjectAsync<T>(Dictionary<string, object> filterValues, string? tableName = null)
        {
            var compiler = new SqlServerCompiler();
            tableName = tableName ?? GetTableName<T>(); // Get table name dynamically

            var db = new QueryFactory(_unitOfWork.Connection, compiler);
            var query = new Query(tableName);

            foreach (var filter in filterValues)
            {
                var columnName = filter.Key;
                var value = filter.Value;
                query.Where(columnName, "=", value);
            }
            try
            {
                var result = await db.GetAsync<T>(query);
                return result.ToList();
            }
            catch(Exception  ex)
            {
                Console.WriteLine(ex);

                return new(); 
            }
           
        }
        public async Task<bool> IsExistAsync<T>(Dictionary<string, object> filterValues, string? tableName = null)
        {
            var compiler = new SqlServerCompiler();
            tableName ??= GetTableName<T>(); // Get table name dynamically if not provided
            var db = new QueryFactory(_unitOfWork.Connection, compiler);
            var query = new Query(tableName);

            // Add conditions from filterValues to the query
            foreach (var filter in filterValues)
            {
                query.Where(filter.Key, filter.Value);
            }

            // Limit the query to check for only one record
            query.Limit(1);

            // Check if any record exists by directly calling ExistsAsync
            return await db.ExistsAsync(query);
        }


        public async Task<List<T>> GetByNameAsync<T>(Dictionary<string, string> filterValues)
        {
            var compiler = new SqlServerCompiler();
            var tableName = GetTableName<T>(); // Get table name dynamically
            var db = new QueryFactory(_unitOfWork.Connection, compiler);
            var query = new Query(tableName);

            foreach (var filter in filterValues)
            {
                var columnName = filter.Value;
                var id = filter.Key;
                query.WhereContains(columnName, id);
            }

            var result = await db.GetAsync<T>(query);
            return result.ToList();
        }

        public async Task<int> SaveAsync<T>(T entity)
        {
            var compiler = new SqlServerCompiler();
            var db = new QueryFactory(_unitOfWork.Connection, compiler);

            var tableName = GetTableName<T>();
            var record = new Query(tableName).AsInsert(entity);
            var result = await db.ExecuteAsync(record);
            return result;
        }

        public async Task<T?> UpdateRecordAsync<T>(T entity, Dictionary<string, object>? filterValues = null)
        {
            var compiler = new SqlServerCompiler();
            var db = new QueryFactory(_unitOfWork.Connection, compiler);

            var tableName = GetTableName<T>();
            var updateQuery = new Query(tableName);
            if (filterValues != null)
            {
                foreach (var filter in filterValues)
                    updateQuery.Where(filter.Key, filter.Value);
            }
            updateQuery.AsUpdate(entity);
            var updateResult = await db.ExecuteAsync(updateQuery);
            if (updateResult > 0)
            {
                var selectQuery = new Query(tableName);
                if (filterValues != null)
                {
                    foreach (var filter in filterValues)
                        selectQuery.Where(filter.Key, filter.Value);
                }
                var updatedRecord = await db.FirstOrDefaultAsync<T>(selectQuery);
                return updatedRecord;
            }

            return default;
        }

        public async Task<bool> UpdateRecordAsync<T>(string tableName, List<string> columns, T entity, Dictionary<string, object> conditions)
        {
            var setClauses = columns.Select(col => $"[{col}] = @{col}").ToList();
            var setClauseString = string.Join(", ", setClauses);

            var whereClauses = conditions.Select(condition => $"{condition.Key} = @{condition.Key}").ToList();
            var whereClauseString = "WHERE " + string.Join(" AND ", whereClauses);

            var sqlQuery = $@"
                UPDATE 
            {tableName}
                SET 
            {setClauseString}
                {whereClauseString}";

            var parameters = new DynamicParameters(entity);
            foreach (var condition in conditions)
            {
                parameters.Add("@" + condition.Key, condition.Value);
            }
            try
            {
                var result = await _unitOfWork.Connection.ExecuteAsync(sqlQuery, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Error updating database.", ex);
            }
        }
        public async Task<bool> UpdateRecordsByIdsAsync<TId, TEntity>(
            string tableName,
            IEnumerable<string> columnsToUpdate,
            TEntity updateValues,
            string idColumn,
            IEnumerable<TId> ids)
        {
            if (!columnsToUpdate?.Any() ?? true)
                throw new ArgumentException("Columns to update cannot be empty.");

            if (!ids?.Any() ?? true)
                throw new ArgumentException("IDs to update cannot be empty.");

            // Create SET clause
            var setClauses = columnsToUpdate.Select(col => $"[{col}] = @{col}");
            var setClauseString = string.Join(", ", setClauses);

            // Create dynamic WHERE IN clause
            var idParamNames = ids.Select((_, i) => $"@id{i}").ToList();
            var whereClause = $"WHERE [{idColumn}] IN ({string.Join(", ", idParamNames)})";

            // Build SQL
            var sql = $@"
                UPDATE [{tableName}]
                SET {setClauseString}
                {whereClause};";

            var parameters = new DynamicParameters(updateValues);
            int index = 0;
            foreach (var id in ids)
            {
                parameters.Add($"@id{index++}", id);
            }

            try
            {
                var affected = await _unitOfWork.Connection.ExecuteAsync(sql, parameters);
                return affected > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to update records in {tableName}.", ex);
            }
        }
        public async Task<bool> FindAndUpdateAsync(string tableName, Dictionary<string, object> updatedValues, Dictionary<string, object> conditions)
        {
            var setClauses = updatedValues.Select(kvp => $"[{kvp.Key}] = @{kvp.Key}").ToList();
            var setClauseString = string.Join(", ", setClauses);

            var whereClauses = conditions.Select(kvp => $"[{kvp.Key}] = @{kvp.Key}").ToList();
            var whereClauseString = string.Join(" AND ", whereClauses);

            var sqlQuery = $@"
                UPDATE {tableName}
                SET {setClauseString}
                WHERE {whereClauseString}";

            var parameters = new DynamicParameters();

            foreach (var kvp in updatedValues)
            {
                parameters.Add(kvp.Key, kvp.Value);
            }
            foreach (var kvp in conditions)
            {
                parameters.Add(kvp.Key, kvp.Value);
            }
            var result = await _unitOfWork.Connection.ExecuteAsync(sqlQuery, parameters, _unitOfWork.Transaction);
            return result > 0;
        }


        public async Task<bool> InsertRecordsAsync<T>(string tableName, List<string> columns, List<T> entities)
        {
            string columnsPart = string.Join(", ", columns.ConvertAll(column => $"[{column}]"));
            string parametersPart = string.Join(", ", columns.ConvertAll(column => "@" + column));

            string query = $"INSERT INTO " +
                $"{tableName} ({columnsPart}) " +
            $"VALUES ({parametersPart})";

            var result = await _unitOfWork.Connection.ExecuteAsync(query, entities);
            return result > 0;
        }

        private string GetTableName<T>()
        {
            var tableAttribute = (TableAttribute)Attribute.GetCustomAttribute(typeof(T), typeof(TableAttribute));
            if (tableAttribute != null)
            {
                return tableAttribute.Name;
            }
            return typeof(T).Name;
        }

        public async Task<bool> DeleteRecordAsync<T>(string tableName, List<RequestFilterDto> requestFilters)
        {
            try
            {
                var whereClauses = requestFilters.Select(condition => $"{condition.Key} {condition.Operator} @{condition.Key}").ToList();
                var whereClauseString = "WHERE " + string.Join(" AND ", whereClauses);
                string sqlQuery = $@"DELETE FROM {tableName} {whereClauseString}";

                var parameters = new DynamicParameters();
                foreach (var condition in requestFilters)
                {
                    parameters.Add("@" + condition.Key, condition.Value);
                }

                var result = await _unitOfWork.Connection.ExecuteAsync(sqlQuery, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Error deleting from the table: {tableName}", ex);
            }
        }

        public async Task<List<T>> GetRecordByRequestFilter<T>(List<RequestFilterDto> requestFilters, string? tableName = null, int? page = 0, int? pageSize = 0, List<string>? columns = null)
        {
            var whereClauses = new List<string>();
            var orGroups = new List<List<string>>();
            tableName = tableName ?? GetTableName<T>();
            List<string> currentGroup = null;
            string order = string.Empty;
            string key = string.Empty;
            foreach (var filter in requestFilters)
            {
                var columnName = filter.Key;
                var value = filter.Value;
                var logicalOperator = filter.LogicalOperator?.ToLowerInvariant() ?? "and";

                if (logicalOperator == "or")
                {
                    if (currentGroup != null)
                    {
                        orGroups.Add(currentGroup);
                    }
                    currentGroup = new List<string>();
                }
                if (new List<string>() { "desc", "asc" }.Contains(filter.Operator.ToLower()))
                {
                    order = filter.Operator.ToUpperInvariant();
                    key = filter.Key;
                }
                else
                {
                    string? condition = BuildCondition(columnName, filter.Operator, value);
                    if (!string.IsNullOrEmpty(condition))
                    {
                        if (currentGroup != null)
                        {
                            currentGroup.Add(condition);
                        }
                        else
                        {
                            whereClauses.Add(condition);
                        }
                    }
                }
            }

            if (currentGroup != null)
            {
                orGroups.Add(currentGroup);
            }
            // Combine the AND conditions
            var finalWhereClause = string.Join(" AND ", whereClauses);
            if (string.IsNullOrEmpty(finalWhereClause))
            {
                finalWhereClause = "1=1";
            }

            // If there are OR groups, add them to the WHERE clause
            if (orGroups.Any())
            {
                var orConditions = orGroups
                    .Select(group => $"({string.Join(" OR ", group)})")
                    .ToList();
                finalWhereClause = $"{finalWhereClause} AND ({string.Join(" OR ", orConditions)})";
            }

            // Handle pagination
            string paginationClause = "";
            if (page > 0 && pageSize > 0)
            {
                int skip = (page - 1) * pageSize ?? 0;
                paginationClause = $"OFFSET {skip} ROWS FETCH NEXT {pageSize} ROWS ONLY";
            }
            string selectClause = "*";
            if (columns != null && columns.Any())
            {
                selectClause = string.Join(", ", columns);
            }
            // Construct the full SQL query
            // Construct the full SQL query
            string sqlQuery = $@"
                SELECT {selectClause} 
                FROM {tableName}
                WHERE {finalWhereClause}";

            if (!string.IsNullOrEmpty(order))
            {
                sqlQuery += $@" ORDER BY {key} {order} ";
            }
            sqlQuery += paginationClause;
            var result = await _unitOfWork.Connection.QueryAsync<T>(sqlQuery, null, _unitOfWork.Transaction);
            return result.ToList();
        }

        private string? BuildCondition(string columnName, string operatorValue, object value)
        {
            switch (operatorValue.ToLower())
            {
                case "=":
                case "eq":
                    return $"{columnName} = '{value}'";
                case "<>":
                case "!=":
                case "ne":
                    return $"{columnName} <> '{value}'";
                case "<":
                case "lt":
                    return $"{columnName} < '{value}'";
                case "<=":
                case "lte":
                    return $"{columnName} <= '{value}'";
                case ">":
                case "gt":
                    return $"{columnName} > '{value}'";
                case ">=":
                case "gte":
                    return $"{columnName} >= '{value}'";
                case "like":
                    return $"{columnName} LIKE '%{value}%'";
                case "startwith":
                case "starts":
                    return $"{columnName} LIKE '{value}%'";
                case "endwith":
                case "ends":
                    return $"{columnName} LIKE '%{value}'";
                case "in":
                    var inList = ((IList)value).Cast<object>().Select(v => $"'{v}'").ToList();
                    return $"{columnName} IN ({string.Join(", ", inList)})";
                case "not in":
                    var notInList = ((IList)value).Cast<object>().Select(v => $"'{v}'").ToList();
                    return $"{columnName} NOT IN ({string.Join(", ", notInList)})";
                default:
                    return null;
            }
        }
    }

}
