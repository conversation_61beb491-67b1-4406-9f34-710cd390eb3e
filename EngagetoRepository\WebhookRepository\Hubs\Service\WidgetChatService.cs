﻿using Engageto.Hubs;
using EngagetoContracts.MetaContracts;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.WidgetChatHubDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.AspNetCore.SignalR;
using System.Text.Json;

namespace EngagetoRepository.WebhookRepository.Hubs.Service
{
    public class WidgetChatService : IWidgetChatService
    {
        private readonly IMetaApiService _metaApiService;
        private readonly IGenericRepository _genericRepository;
        private readonly IHubContext<WidgetChatHub, IWidgetChatHub> _widgetChatHub;
        private readonly IHubContext<MessageHub, IMessageHubClient> _messageChatHub;
        JsonSerializerOptions options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
        public WidgetChatService(IMetaApiService metaApiService, IGenericRepository genericRepository, IHubContext<WidgetChatHub, IWidgetChatHub> widgetChatHub, IHubContext<MessageHub, IMessageHubClient> messageChatHub)
        {
            _metaApiService = metaApiService;
            _genericRepository = genericRepository;
            _widgetChatHub = widgetChatHub;
            _messageChatHub = messageChatHub;
        }

        public async Task<WidgetChatDto> SendWidgetMessageToWhatsappAsync(WidgetChatDto message)
        {
            try
            {
                WidgetEntity? widget = (await _genericRepository.GetByObjectAsync<WidgetEntity>(new Dictionary<string, object> { { "Id", message.Id ?? Guid.Empty } }))?.FirstOrDefault();
                
                if (widget != null)
                {
                    var metaBusinessDetails = (await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object>() { { "BusinessId", widget.BusinessId ?? string.Empty } }))?.FirstOrDefault();
                    if (metaBusinessDetails != null && !string.IsNullOrEmpty(widget.BusinessId))
                    {
                        (string mimeType, string mediaType) = message?.BodyMessage?.Media == null && message?.BodyMessage?.Text != null ? ("text", string.Empty) : (await GetMediaTypeAsync(message.BodyMessage.Media?.MediaUrl ?? string.Empty));
                        //var caption = !string.IsNullOrEmpty(message.BodyMessage.MediaUrl) ? message.BodyMessage.Message : string.Empty;
                        var text = message.BodyMessage?.Text?.Text ?? message?.BodyMessage?.Media?.Caption ?? string.Empty;
                        var conv = GetTextMediaConversationFormate(message?.BodyMessage?.Text?.Text, message.From, widget.BusinessId, mediaType, message.BodyMessage.Media?.Caption, mimeType, message.BodyMessage.Media?.MediaUrl, null, null);
                        var response = await _metaApiService.SendTextWithMediaMessageAsync(message.From, widget.PhoneNumber, text, mediaType, message.BodyMessage.Media?.MediaUrl, null, metaBusinessDetails.Token, metaBusinessDetails.PhoneNumberID, metaBusinessDetails.WhatsAppBusinessAccountID);
                        if (response.IsSuccess)
                        {
                            var result = response.Result.ToObject<WhatsAppResponse>();
                            conv.Status = ConvStatus.sent;
                            conv.WhatsAppMessageId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty;
                            message.BodyMessage.Status = ConvStatus.sent;

                        }
                        else
                        {
                            var result = response.Result.ToObject<WhatsAppErrorResponse>();
                            conv.Status = ConvStatus.failed;
                            conv.ErrorMessage = result?.Error?.Message ?? string.Empty;
                            conv.ErrorDetails = Newtonsoft.Json.JsonConvert.SerializeObject(response.Result) ?? string.Empty;
                            message.BodyMessage.Status = ConvStatus.failed;
                            message.BodyMessage.ErrorMessage = conv.ErrorMessage;
                        }
                        var insert = await _genericRepository.InsertRecordsAsync("Conversations", StringHelper.GetPropertyNames<Conversations>(false), new List<Conversations>() { conv });
                        await _messageChatHub.Clients.Group(widget.BusinessId.ToLower() + widget.UserId.ToString().ToLower()).ReceiveMessageFromServer(new List<ConversationDto> { conv.Adapt<ConversationDto>() });
                        return message;
                    }
                }
                message.BodyMessage.Status = ConvStatus.failed;
                return message;
            }
            catch (Exception ex)
            {
                message.BodyMessage.Status = ConvStatus.failed;
                return message;
            }
        }
        private Conversations GetTextMediaConversationFormate(string? textMessage, string from, string to, string? mediaType, string? caption, string? mediaMimeType, string? mediaUrl, string waMessageId, string? replyId)
        {
            Conversations conv = new Conversations
            {
                Id = Guid.NewGuid(),
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = waMessageId,
                TextMessage = textMessage ?? string.Empty,
                MediaCaption = caption ?? string.Empty,
                ReplyId = replyId,
                To = to.ToLowerInvariant().Replace("+", ""),
                From = from.ToString().ToLowerInvariant().Replace("+", ""),
                MediaUrl = mediaUrl,
                Status = ConvStatus.sent,
                MediaMimeType = mediaMimeType
            };
            return conv;
        }

        private async Task<(string MimeType, string MediaType)> GetMediaTypeAsync(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    return ("text", "text");
                }
                var client = new HttpClient();
                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                    throw new InvalidOperationException("Failed to retrieve content from the provided URL.");

                var mimeType = response.Content.Headers.ContentType?.MediaType;
                if (string.IsNullOrEmpty(mimeType))
                    throw new InvalidOperationException("Content type not found.");

                var mediaType = GetMediaCategory(mimeType);
                return (mimeType, mediaType);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("The provided URL is not valid or accessible.", ex);
            }
        }
        private string GetMediaCategory(string mimeType)
        {
            if (mimeType.StartsWith("application/"))
            {
                var documentTypes = new HashSet<string>
                {
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "text/plain"
                };

                return documentTypes.Contains(mimeType) ? "document" : "Others";
            }

            var mediaType = mimeType.Split('/')[0]; // Example: "image/jpeg" -> "image"
            return mediaType switch
            {
                "image" => "image",
                "video" => "video",
                "audio" => "audio",
                _ => "Others"
            };
        }

        public async Task ReceiveMessageToWidgetHubAsync(ConversationDto message)
        {
            try
            {
                string businessId = string.Empty;
                if (Guid.TryParse(message.To, out var companyId))
                {
                    businessId = message.To;
                }
                else
                {
                    businessId = message.From;
                }

                if (string.IsNullOrEmpty(message.TemplateBody))
                {
                    Enum.TryParse<ConvStatus>(message.Status, true, out ConvStatus status);
                    WidgetEntity? widget = (await _genericRepository.GetByObjectAsync<WidgetEntity>(new Dictionary<string, object> { { "BusinessId", businessId } }))?.FirstOrDefault();
                    
                    if (widget == null) return;

                    var widgetMessage = new WidgetChatDto()
                    {
                        From = message.From,
                        To = message.To,
                        
                    };
                    var bodyMessage = new BodyMessage
                    {
                        Status = status,
                        ErrorMessage = message.ErrorMessage,
                    };
                    if (string.IsNullOrEmpty(message.MediaUrl))
                    {
                        TextMessage textMessage = new()
                        {
                            Text = message.TextMessage ?? string.Empty,
                        };
                        bodyMessage.Text =  textMessage = new()
                        {
                            Text = message.TextMessage ?? string.Empty,
                        };
                        bodyMessage.Type = MediaType.TEXT;
                    }
                    else
                    {
                        (string mediaType, string mimeType) = await GetMediaTypeAsync(message.MediaUrl);
                        MediaMessage media = new()
                        {
                            MediaUrl = message.MediaUrl,
                            Caption = message.MediaCaption,
                            MimeType = mimeType
                        };
                        bodyMessage.Media = media;
                        _ = Enum.TryParse<MediaType>(mediaType, true, out MediaType type);
                        bodyMessage.Type = type;
                    }
                    widgetMessage.Body = JsonSerializer.Serialize(bodyMessage,options);
                    await _widgetChatHub.Clients.Group(widget.Id.ToString().ToLowerInvariant()).ReceiveMessageAsync(widgetMessage);
                };
            }
            catch (Exception ex) { }
        }
    }
}

