﻿namespace EngagetoEntities.Dtos.CampaignDto
{
    public class CampaignFilter
    {
        public CampaignSearch? Searching { get; set; }
        public CampaignSort? Sorting { get; set; }
        public CampaignFilterGroup? Filtering { get; set; }
    }
    public class CampaignSearch
    {
        public string? Value { get; set; }
    }
    public class CampaignSort
    {
        public string? Column { get; set; }
        public string? Order { get; set; }
    }

    public class CampaignFilterGroup
    {
        public string? FilterType { get; set; }
        public List<CampaignFilterCondition>? Conditions { get; set; }
    }

    public class CampaignFilterCondition
    {
        public string? Column { get; set; }
        public string? Operator { get; set; }
        public string? Value { get; set; }
    }
}









