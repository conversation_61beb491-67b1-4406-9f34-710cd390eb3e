﻿using EngagetoDapper.Data.HttpService;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Response;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace EngagetoDapper.Data.Dapper.Services.UserServices
{
    public class WebhookService : IWebhookService
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IHttpService _httpService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<WebhookService> _logger;
        JsonSerializerOptions options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
        public WebhookService(IGenericRepository genericRepository,
            IHttpService httpService,
            ILogger<WebhookService> logger,
            IHttpClientFactory httpClientFactory)
        {
            _genericRepository = genericRepository;
            _httpService = httpService;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
        }
        public async Task<bool> SaveWebhookEnpointAsync(WebhookEndpointRequestDto webhookEndpointRequestDto)
        {
            try
            {
                WebhookEndpointEntity webhook = new WebhookEndpointEntity(
                    webhookEndpointRequestDto.CompanyId,
                    webhookEndpointRequestDto.UserId,
                    webhookEndpointRequestDto.WebhookUrl,
                    webhookEndpointRequestDto.Description,
                    webhookEndpointRequestDto.ApiKeyName,
                    webhookEndpointRequestDto.ApiKey
                    );
                var result = await _genericRepository.SaveAsync(webhook);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<List<WebhookEndpointEntity>> GetWebhookEnpointAsync(string companyId)
        {
            try
            {
                return await _genericRepository
                    .GetByObjectAsync<WebhookEndpointEntity>(new Dictionary<string, object>()
                    {
                        { "CompanyId",companyId },
                        { "IsActive", true }
                    });
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> SaveWebhookEventsAsync(string companyId, string eventName, Conversations conversations)
        {
            var conversationDto = conversations.Adapt<WebhookResponseDto>();
            conversationDto.Field = FieldName.Message;
            var webhook = (await GetWebhookEnpointAsync(companyId))?.FirstOrDefault();

            if (webhook == null || conversations == null)
                return false;

            conversationDto.TextMessage = (conversationDto.TextMessage ?? string.Empty +
                         conversations.TemplateBody ?? string.Empty +
                         "\n" + (conversations.TemplateFooter ?? string.Empty) +
                         conversationDto.MediaCaption ?? string.Empty)
                        .Replace("null", string.Empty);

            conversationDto.MediaUrl = conversations.MediaUrl ?? conversations.TemplateMediaUrl;
            conversationDto.TemplateMediaType = string.IsNullOrEmpty(conversationDto.MediaUrl)
                   ? "Text"
                   : await GetMediaTypeAsync(conversationDto.MediaUrl ?? string.Empty);

            if (!Guid.TryParse(conversations.From, out var businessId))
            {
                conversationDto.ContactNo = conversations.From;
            }
            else
            {
                conversationDto.ContactNo = conversations.To;
            }

            var phoneNumer = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(conversationDto.ContactNo);
            var contactDetail = (await _genericRepository.GetByObjectAsync<Contacts>(new() { { "Contact", phoneNumer.NationalNumber }, { "BusinessId", Guid.Parse(companyId) } }))?.FirstOrDefault();

            conversationDto.ContactName = contactDetail?.Name ?? string.Empty;

            ApiResponse<List<WebhookResponseDto>> apiResponse = new ApiResponse<List<WebhookResponseDto>>()
            {
                Success = true,
                Data = new List<WebhookResponseDto> { conversationDto },
                Errors = null
            };
            try
            {
                Dictionary<string, string> headers = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(webhook.ApiKey))
                    headers.Add(webhook.ApiKeyName, webhook.ApiKey);
                //pre save 
                var webhookEvents = new WebhookEvents(
                    webhook.Id,
                    "PreSendMessage",
                    JsonSerializer.Serialize(conversationDto, options),
                    StringHelper.GetIndianDateTime(),
                    (int)HttpStatusCode.SeeOther,
                    null,
                    1
                );
                await SaveWebhookEventAsync(webhookEvents);
                //end
                var response = await _httpService.PostAsync<object>(webhook.WebhookUrl, headers, apiResponse);

                string json = JsonSerializer.Serialize(apiResponse, options);
                webhookEvents = new WebhookEvents(
                    webhook.Id,
                    eventName,
                    json,
                    StringHelper.GetIndianDateTime(),
                    (int)HttpStatusCode.OK,
                    JsonSerializer.Serialize(response),
                    1
                );

                return await SaveWebhookEventAsync(webhookEvents);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving webhook event: {ex.Message}");
                _logger.LogError($"Error saving webhook event: {ex.Message}, error: {ex.StackTrace}");

                var webhookEvents = new WebhookEvents(
                    webhook.Id,
                    eventName,
                    JsonSerializer.Serialize(apiResponse, options),
                    StringHelper.GetIndianDateTime(),
                    (int)HttpStatusCode.InternalServerError,
                    JsonSerializer.Serialize(ex),
                    1
                );
                return await SaveWebhookEventAsync(webhookEvents);
            }
        }

        public async Task<bool> SendTemplateWebhookAsync(string companyId, Guid templateId)
        {
            var webhook = (await GetWebhookEnpointAsync(companyId))?.FirstOrDefault();
            List<WebhookResponseTemplateDto> webhookTempaltes = new List<WebhookResponseTemplateDto>();
            ApiResponse<List<WebhookResponseTemplateDto>> apiResponse = new ApiResponse<List<WebhookResponseTemplateDto>>()
            {
                Success = true,
                Errors = null
            };
            try
            {
                if (webhook != null)
                {
                    var templates = await _genericRepository
                        .GetByObjectAsync<WebhookResponseTemplateDto>(new Dictionary<string, object>
                            {
                                { "TemplateId", templateId },
                                { "Status", WATemplateStatus.APPROVED }
                            }, "Templates");

                    if (templates?.Any() == true)
                    {
                        webhookTempaltes = templates.Adapt<List<WebhookResponseTemplateDto>>();

                        var buttons = await _genericRepository.GetByObjectAsync<Button>(new Dictionary<string, object> { { "TemplateId", templateId } }, "ButtonDetails");

                        Dictionary<string, string> headers = new Dictionary<string, string>();

                        webhookTempaltes.ForEach(x =>
                        {
                            x.Buttons = buttons;
                            x.Field = FieldName.Template;
                        });

                        if (!string.IsNullOrEmpty(webhook.ApiKey))
                            headers.Add(webhook.ApiKeyName ?? "Api-Key", webhook.ApiKey);

                        apiResponse.Data = webhookTempaltes;
                        var response = await _httpService.PostAsync<object>(webhook.WebhookUrl, headers, apiResponse);
                        string json = JsonSerializer.Serialize(apiResponse, options);

                        var webhookEvents = new WebhookEvents
                            (
                                webhook.Id,
                                "TemplateSendWebhookNotification",
                                json,
                                StringHelper.GetIndianDateTime(),
                                (int)HttpStatusCode.OK,
                                JsonSerializer.Serialize(response),
                                1
                            );
                        return await SaveWebhookEventAsync(webhookEvents);
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                apiResponse.Data = webhookTempaltes;
                var webhookEvents = new WebhookEvents
                    (
                       webhook.Id,
                       "TemplateSendWebhookNotificationFaild",
                       JsonSerializer.Serialize(apiResponse, options),
                       StringHelper.GetIndianDateTime(),
                       (int)HttpStatusCode.InternalServerError,
                       JsonSerializer.Serialize(ex),
                       1
                    );
                return await SaveWebhookEventAsync(webhookEvents);
            }
        }
        private async Task<bool> SaveWebhookEventAsync(WebhookEvents webhookEvents)
        {
            try
            {
                var result = await _genericRepository.SaveAsync(webhookEvents);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error saving webhook event: {ex.Message}, error: {ex.StackTrace}");
                Console.WriteLine($"Error saving webhook event to repository: {ex.Message}");
                return false;
            }
        }

        private async Task<string> GetMediaTypeAsync(string url)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(url);
                string type = "None";
                if (response.IsSuccessStatusCode)
                {
                    var contentType = response.Content.Headers.ContentType?.MediaType;
                    var fileName = Path.GetFileName(new Uri(url).LocalPath);
                    if (!string.IsNullOrEmpty(contentType))
                    {
                        var mediaType = contentType.Split('/')[0];
                        if (contentType.StartsWith("application/pdf") ||
                            contentType.StartsWith("application/msword") ||
                            contentType.StartsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                            contentType.StartsWith("application/vnd.ms-excel") ||
                            contentType.StartsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                            contentType.StartsWith("text/plain"))
                        {
                            type = "document";
                        }
                        else
                        {
                            type = mediaType switch
                            {
                                "image" => "Image",
                                "video" => "Video",
                                "audio" => "Audio",
                                _ => "None"
                            };
                        }
                    }
                }
                else
                {
                    throw new Exception();
                }
                return type;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Url is not valid.");
            }
        }
    }
}
