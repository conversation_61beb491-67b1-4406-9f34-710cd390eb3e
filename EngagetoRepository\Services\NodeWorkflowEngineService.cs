using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoEntities.Constants;
using EngagetoRepository.MetaServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Text;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoEntities.Dtos.AutomationDtos;
using Mapster;
using EngagetoEntities.Dtos.TemplateDtos;
using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Asn1.X509;
using Microsoft.IdentityModel.Tokens;
using System.Linq;
using EngagetoContracts.Services;
using EngagetoContracts.AttributeName;
using EngagetoContracts.WorkflowRepository;
using EngagetoEntities.Dtos.AttributeNameDtos;
using DocumentFormat.OpenXml.Bibliography;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Dtos.MetaDto;
using Microsoft.AspNetCore.Http;

namespace EngagetoRepository.Services
{
    public class NodeWorkflowEngineService : INodeWorkflowEngineService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<NodeWorkflowEngineService> _logger;
        private readonly IMetaApiService _metaApiService;
        private readonly IGenericRepository _genericRepository;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IWorkflowCustomResponseService _workflowCustomResponseService;
        private readonly IAttributeNameService _attributeNameService;
        private readonly ICustomerWorkflowTrackerRepository _customerWorkflowTrackerRepository;
        private readonly IUserIdentityService _userIdentityService;

        public NodeWorkflowEngineService(
            ApplicationDbContext dbContext,
            ILogger<NodeWorkflowEngineService> logger,
            IMetaApiService metaApiService,
            IGenericRepository genericRepository,
            IHttpClientFactory httpClientFactory,
            IServiceProvider serviceProvider,
            IWorkflowCustomResponseService workflowCustomResponseService,
            IAttributeNameService attributeNameService,
            ICustomerWorkflowTrackerRepository customerWorkflowTrackerRepository,
            IUserIdentityService userIdentityService
            )
        {
            _dbContext = dbContext;
            _logger = logger;
            _metaApiService = metaApiService;
            _genericRepository = genericRepository;
            _httpClientFactory = httpClientFactory;
            _serviceProvider = serviceProvider;
            _workflowCustomResponseService = workflowCustomResponseService;
            _attributeNameService = attributeNameService;
            _customerWorkflowTrackerRepository = customerWorkflowTrackerRepository;
            _userIdentityService = userIdentityService;
        }

        public async Task ProcessWorkflowAsync(Contacts contact, string? textMessage, bool? isNewCustomer = false, bool? isStatusChange = false, bool? isProjectChange = false)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                var nodeStartTime = DateTime.UtcNow;
                var currentNode = await GetStartingNodeAsync(contact, textMessage, isNewCustomer, isStatusChange, isProjectChange);
                if (currentNode == null)
                {
                    return;
                }

                var processStartTime = DateTime.UtcNow;
                var nextNodeId = await ProcessNodeAsync(currentNode, contact, textMessage);

                if (nextNodeId != null)
                {
                    contact.WorkFlowNodeId = nextNodeId;
                    contact.WorkflowId = currentNode.WorkflowId;
                    _dbContext.Contacts.Update(contact);
                    await _dbContext.SaveChangesAsync();

                    await ProcessWorkflowAsync(contact, null, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing workflow for customer {CustomerId}", contact.ContactId);
            }
            finally
            {
                _logger.LogInformation("Total workflow processing time: {Duration}ms",
                    (DateTime.UtcNow - startTime).TotalMilliseconds);
            }
        }

        private async Task<WorkflowNode?> GetStartingNodeAsync(Contacts contact, string? textMessage, bool? isNewCustomer, bool? isStatusChange = false, bool? isProjectChange = false)
        {
            try
            {
                if (contact.WorkFlowNodeId != null && contact.WorkFlowNodeId != Guid.Empty)
                {
                    var activeNode = await _dbContext.WorkflowNodes
                        .FirstOrDefaultAsync(n => n.Id == contact.WorkFlowNodeId);

                    if (activeNode != null)
                    {
                        return activeNode;
                    }
                    else
                    {
                        contact.WorkFlowNodeId = null;
                    }
                }

                if (isStatusChange == true)
                {
                    try
                    {
                        var statusWorkflows = await _dbContext.WorkflowNodes
                            .Include(n => n.Workflow)
                            .Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted && n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();


                        var matchingWorkflows = statusWorkflows
                            .Where(n =>
                                n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.Status &&
                                n.PayloadModel?.FlowStartModel?.LeadStatus != null &&
                                n.PayloadModel.FlowStartModel.LeadStatus.Any(ls =>
                                    ls.Status?.ToLower() == contact.LeadStatus?.ToString()?.ToLower() &&
                                    (
                                        ls.SubStatus == null || ls.SubStatus.Count == 0 || string.IsNullOrEmpty(contact.LeadSubStatus?.ToString()) ||
                                        (ls.SubStatus != null && ls.SubStatus.Contains(contact.LeadSubStatus.ToString()))
                                    ))).ToList();



                        if (matchingWorkflows.Any())
                        {
                            var selectedNode = matchingWorkflows
                                .OrderByDescending(n => n.CreatedAt)
                                .FirstOrDefault();

                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;

                                await _dbContext.SaveChangesAsync();
                                return selectedNode;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing status change workflow for contact {ContactId}", contact.ContactId);
                        throw;
                    }
                }

                if (isProjectChange == true)
                {
                    try
                    {
                        var projectWorkflows = await _dbContext.WorkflowNodes
                                             .Include(n => n.Workflow)
                                              .Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted && n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();


                        var matchingWorkflows = projectWorkflows
                                              .Where(n => n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.Project &&
                                                         n.PayloadModel?.FlowStartModel?.LeadProject != null &&
                                                         n.PayloadModel.FlowStartModel.LeadProject.Any(ls =>
                                                         ls?.ToLower() == contact.Project?.ToString()?.ToLower())).ToList();

                        if (matchingWorkflows.Any())
                        {
                            var selectedNode = matchingWorkflows
                                .OrderByDescending(n => n.CreatedAt)
                                .FirstOrDefault();

                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;

                                await _dbContext.SaveChangesAsync();
                                return selectedNode;
                            }
                        }


                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing project change workflow for contact {ContactId}", contact.ContactId);
                        throw;
                    }

                }

                if (isNewCustomer == true)
                {
                    var potentialNodes = await _dbContext.WorkflowNodes
                                       .Include(n => n.Workflow).Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted &&
                                        n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();

                    var sourceMatchingNodes = potentialNodes
                        .Where(n =>
                            n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead &&
                            n.PayloadModel?.FlowStartModel?.LeadSource != null &&
                            n.PayloadModel.FlowStartModel.LeadSource.Any(ls =>
                                ls.Source == contact.Source.ToString() &&
                                (
                                    string.IsNullOrEmpty(contact.SubSource?.ToString()) || ls.SubSource != null && ls.SubSource.Contains(contact.SubSource.ToString())
                                ))).ToList();



                    if (sourceMatchingNodes.Any())
                    {
                        var selectedNode = SelectPriorityWorkflow(sourceMatchingNodes);
                        contact.WorkflowId = selectedNode.WorkflowId;
                        return selectedNode;
                    }

                    var allDefaultNodes = await _dbContext.WorkflowNodes
                       .Include(n => n.Workflow)
                       .Where(n => n.Type == NodeType.FlowStart &&
                                  n.IsEntry &&
                                  n.Workflow.IsActive &&
                                  !n.Workflow.IsDeleted &&
                                  n.Workflow.CompanyId == contact.BusinessId.ToString())
                       .ToListAsync();

                    var defaultNodes = allDefaultNodes
                        .Where(n => n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead)
                        .ToList();

                    if (defaultNodes.Any())
                    {
                        var selectedNode = SelectPriorityWorkflow(defaultNodes);
                        contact.WorkflowId = selectedNode.WorkflowId;

                        return selectedNode;
                    }
                    return null;
                }

                // For existing customers with a message, check keywords in WorkflowKeywords table
                // First get active workflow IDs
                // Then find matching keywords for active workflows

                if (!string.IsNullOrEmpty(textMessage) || !string.IsNullOrWhiteSpace(textMessage))
                {

                    var matchingKeywords = await _dbContext.WorkflowKeywords
                       .Include(k => k.Workflow)
                       .Where(k => !k.IsDeleted &&
                            k.BusinessId == contact.BusinessId.ToString() &&
                            k.Keyword.ToLower() == textMessage.ToLower() &&
                            k.Workflow.IsActive)
                       .Select(k => new
                       {
                           k.WorkflowId,
                           WorkflowNodeId = Guid.Parse(k.WorkflowNodeId.ToString()),
                           k.Keyword,
                           k.Workflow
                       })
                       .ToListAsync();

                    if (matchingKeywords.Any())
                    {
                        // Get the matching nodes directly using WorkflowNodeId
                        var matchingNodes = await _dbContext.WorkflowNodes
                            .Where(n => matchingKeywords.Select(k => k.WorkflowNodeId).Contains(n.Id) && n.Type == NodeType.FlowStart && !n.IsDeleted).ToListAsync();

                        if (matchingNodes.Any())
                        {
                            var selectedNode = SelectPriorityWorkflow(matchingNodes);
                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;
                                return selectedNode;
                            }
                        }
                    }

                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error finding starting node for customer {CustomerId}",
                    contact.ContactId);
                return null;
            }
        }
        private WorkflowNode SelectPriorityWorkflow(List<WorkflowNode> nodes)
        {
            if (nodes == null || !nodes.Any())
                throw new ArgumentException("No nodes provided for selection");

            var byCreationDate = nodes.OrderByDescending(n => n.CreatedAt).FirstOrDefault();
            if (byCreationDate != null)
                return byCreationDate;

            return nodes.First();
        }

        private async Task<Guid?> ProcessNodeAsync(WorkflowNode node, Contacts contact, string? textMessage)
        {

            try
            {
                Guid? nextNode = null;

                switch (node.Type)
                {
                    case NodeType.FlowStart:
                        nextNode = await ProcessFlowStartNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.InteractiveMessage:
                        nextNode = await ProcessInteractiveMessageNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.HttpRequest:
                        nextNode = await ProcessHttpRequestNodeAsync(node, contact);
                        break;
                    case NodeType.Template:
                        nextNode = await ProcessTemplateNodeAsync(node, contact);
                        break;
                    case NodeType.Condition:
                        nextNode = await ProcessConditionNodeAsync(node, contact, textMessage);
                        break;
                    default:
                        _logger.LogWarning("Unknown node type: {NodeType}", node.Type);
                        break;
                }
                if (nextNode != null)
                {
                    return nextNode;
                }
                var hasOutgoingEdges = await _dbContext.WorkflowEdges.AnyAsync(e => e.SourceId == node.Id);

                if (!hasOutgoingEdges)
                {
                    await MarkWorkflowCompletedAsync(contact);
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }

        }
        private async Task MarkWorkflowCompletedAsync(Contacts contact)
        {
            try
            {
                var activeTrackers = await _dbContext.CustomerWorkflowTrackers
              .Where(t =>
                t.ContactId == contact.ContactId &&
                t.WorkflowId == contact.WorkflowId &&
               !t.CompletedAt.HasValue)
               .ToListAsync();
                if (activeTrackers.Any())
                {
                    foreach (var tracker in activeTrackers)
                    {
                        tracker.CompletedAt = DateTime.UtcNow;
                        _dbContext.CustomerWorkflowTrackers.Update(tracker);
                    }
                }
                contact.WorkflowId = null;
                contact.WorkFlowNodeId = null;
                _dbContext.Contacts.Update(contact);

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {

            }
        }
        private async Task<Guid?> ProcessFlowStartNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                if (edge == null || !edge.Targets.Any())
                {

                    return null;
                }
                var nextNodeId = edge.Targets.First().TargetNodeId;

                return nextNodeId;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task<Guid?> ProcessInteractiveMessageNodeAsync(WorkflowNode node, Contacts contact, string? textMessage)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var interactiveMessage = node.PayloadModel?.InteractiveMessage;
                if (interactiveMessage == null)
                {
                    _logger.LogWarning("Interactive message payload is null for node {NodeId}", node.Id);
                    return null;
                }
                var customerResponse = await _workflowCustomResponseService.GetWorkflowCustomResponseAsync(
                    node.Id,
                    node.WorkflowId,
                    contact.BusinessId
                );

                string? attributeName = null;
                if (customerResponse != null && customerResponse.AttributeId.HasValue)
                {
                    attributeName = await _attributeNameService.GetAttributeNameByIdAsync(customerResponse.AttributeId.Value);
                }

                if (!string.IsNullOrEmpty(textMessage))
                {

                    var existingTracker = await _dbContext.CustomerWorkflowTrackers
                     .FirstOrDefaultAsync(t =>
                         t.ContactId == contact.ContactId &&
                         t.WorkflowId == node.WorkflowId &&
                         t.NodeId == node.Id &&
                         t.CompletedAt == null);

                    if (existingTracker != null)
                    {
                        existingTracker.CustomerReponse = textMessage;
                        existingTracker.AttributeName = attributeName;
                        existingTracker.UpdatedAt = DateTime.UtcNow;

                        _dbContext.CustomerWorkflowTrackers.Update(existingTracker);
                    }
                    else
                    {
                        var tracker = new CustomerWorkflowTracker
                        {
                            Id = Guid.NewGuid(),
                            BusinessId = contact.BusinessId,
                            ContactId = contact.ContactId,
                            WorkflowId = node.WorkflowId,
                            NodeId = node.Id,
                            NodeType = node.Type,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CustomerReponse = textMessage,
                            AttributeName = attributeName
                        };
                        await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);
                    }
                    try
                    {
                        await _dbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                    }

                    var edges = await _dbContext.WorkflowEdges
                        .Include(e => e.Targets)
                        .Where(e => e.SourceId == node.Id)
                        .ToListAsync();

                    if (!edges.Any())
                    {
                        return null;
                    }

                    var targets = edges.SelectMany(e => e.Targets).ToList();

                    if (!targets.Any())
                    {
                        return null;
                    }

                    if (targets.Count == 1)
                    {
                        return targets[0].TargetNodeId;
                    }

                    if (targets.Count > 1)
                    {
                        foreach (var target in targets)
                        {
                            if (string.IsNullOrEmpty(target.Condition))
                                continue;

                            var conditionParts = target.Condition.Split(new[] { "==" }, StringSplitOptions.RemoveEmptyEntries);
                            if (conditionParts.Length != 2)
                                continue;

                            var expectedValue = conditionParts[1].Trim().Trim('\'', '"');

                            if (textMessage.Equals(expectedValue, StringComparison.OrdinalIgnoreCase))
                            {
                                var targetNode = await _dbContext.WorkflowNodes.FindAsync(target.TargetNodeId);
                                return target.TargetNodeId;
                            }
                        }
                        var defaultTarget = targets.FirstOrDefault(t => string.IsNullOrEmpty(t.Condition));
                        if (defaultTarget != null)
                        {
                            return defaultTarget.TargetNodeId;
                        }
                    }
                    return targets[0].TargetNodeId;
                }
                else
                {
                    var existingTracker = await _dbContext.CustomerWorkflowTrackers
                   .FirstOrDefaultAsync(t =>
                    t.ContactId == contact.ContactId &&
                    t.WorkflowId == node.WorkflowId &&
                    t.NodeId == node.Id &&
                    t.CompletedAt == null);

                    if (existingTracker != null)
                    {
                        existingTracker.CustomerReponse = textMessage;
                        existingTracker.AttributeName = attributeName;
                        existingTracker.UpdatedAt = DateTime.UtcNow;

                        _dbContext.CustomerWorkflowTrackers.Update(existingTracker);
                        await _dbContext.SaveChangesAsync();
                    }
                    else
                    {
                        var tracker = new CustomerWorkflowTracker
                        {
                            Id = Guid.NewGuid(),
                            BusinessId = contact.BusinessId,
                            ContactId = contact.ContactId,
                            WorkflowId = node.WorkflowId,
                            NodeId = node.Id,
                            NodeType = node.Type,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CustomerReponse = textMessage,
                            AttributeName = attributeName
                        };

                        await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);

                        try
                        {
                            await _dbContext.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                    var account = await _dbContext.BusinessDetailsMetas
                        .FirstOrDefaultAsync(x => x.BusinessId == contact.BusinessId.ToString());

                    if (account == null)
                    {
                        return null;
                    }

                    var phoneNumber = $"{contact.CountryCode}{contact.Contact}";

                    var bodyMessage = await ProcessInteractiveMessageVariablesAsync(interactiveMessage, contact);

                    try
                    {
                        if (interactiveMessage.Type == InteractiveType.Button && interactiveMessage.Buttons != null && interactiveMessage.Buttons.Any())
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, interactiveMessage.Header, interactiveMessage.Type, interactiveMessage.Buttons, null);

                            string actionJson = JsonConvert.SerializeObject(new
                            {
                                buttons = interactiveMessage.Buttons.Select(b => new
                                {
                                    type = "reply",
                                    reply = new
                                    {
                                        id = b.Id,
                                        title = b.Name
                                    }
                                }).ToList()
                            });

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                MessageType = MessageType.Interactive,
                                TextMessage = bodyMessage,
                                Action = actionJson,
                                QuickReplies = String.Join(",", interactiveMessage.Buttons),
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();
                        }
                        else if (interactiveMessage.Type == InteractiveType.List &&
                                interactiveMessage.List != null)
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, interactiveMessage.Header, interactiveMessage.Type, null, interactiveMessage.List);

                            string actionJson = JsonConvert.SerializeObject(new
                            {
                                type = "list",
                                button = interactiveMessage.List.ButtonText,
                                sections = interactiveMessage.List.Sections.Select(s => new
                                {
                                    title = s.Title,
                                    rows = s.Rows.Select(r => new
                                    {
                                        id = r.Id,
                                        title = r.Title,
                                        description = r.Description
                                    }).ToList()
                                }).ToList()
                            });

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                MessageType = MessageType.Interactive,
                                TextMessage = bodyMessage,
                                Action = actionJson,
                                QuickReplies = String.Join(",", interactiveMessage.List),
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();

                        }
                        else
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, null, interactiveMessage.Type, null, null);

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                MessageType = MessageType.Normal,
                                TextMessage = bodyMessage,
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };
                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private string ResolveVariableValue(string variable, string? userResponse, string? variableValue, string? fallbackValue, Contacts? contact = null)
        {
            // Priority 1: User Response
            if (!string.IsNullOrEmpty(userResponse))
            {
                return userResponse;
            }

            // Priority 2: Contact Property based on attribute name
            if (contact != null && !string.IsNullOrEmpty(variableValue))
            {
                // Get the property value from contact based on attribute name
                var propertyInfo = typeof(Contacts).GetProperty(variableValue);
                if (propertyInfo != null)
                {
                    var value = propertyInfo.GetValue(contact);
                    if (value != null)
                    {
                        // Special handling for phone number
                        if (variableValue.Equals("Contact", StringComparison.OrdinalIgnoreCase))
                        {
                            return $"{contact.CountryCode}{value}";
                        }
                        return value.ToString();
                    }
                }
            }

            // Priority 3: Variable Value
            if (!string.IsNullOrEmpty(variableValue))
            {
                return variableValue;
            }

            // Priority 4: Fallback Value
            return fallbackValue ?? "Unknown";
        }

        private async Task<string> ReplaceVariablesInJsonBody(string jsonBody, List<VariableModel> variableValues, Contacts contact)
        {
            try
            {
                if (jsonBody.StartsWith("\"") && jsonBody.EndsWith("\""))
                {
                    jsonBody = JsonConvert.DeserializeObject<string>(jsonBody);
                }

                foreach (var variable in variableValues)
                {
                    if (!string.IsNullOrEmpty(variable.Variable))
                    {
                        var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contact.ContactId, contact.WorkflowId);

                        var matchingTracker = previousResponses.FirstOrDefault(t => t.AttributeName != null && t.AttributeName.Equals(variable.Value, StringComparison.OrdinalIgnoreCase));

                        // Priority: 1. User Response, 2. Contact Property, 3. Variable Value, 4. Fallback
                        string replacementValue = ResolveVariableValue(variable.Variable, matchingTracker?.CustomerReponse, variable.Value, variable.FallbackValue, contact);
                        jsonBody = jsonBody.Replace(variable.Variable, replacementValue);
                    }
                }
                var parsedJson = JsonConvert.DeserializeObject(jsonBody);
                return JsonConvert.SerializeObject(parsedJson);
            }
            catch (Exception ex)
            {
                return jsonBody;
            }
        }

        /// <summary>
        /// Auto-detect and populate LeadRat variables from Swagger input
        /// If user only provides Variable and FallbackValue, automatically set Value field for LeadRat variables
        /// </summary>
        private List<VariableModel> AutoPopulateLeadRatVariables(List<VariableModel> variables)
        {
            if (variables == null || !variables.Any())
                return variables;

            var processedVariables = new List<VariableModel>();

            foreach (var variable in variables)
            {
                var processedVariable = new VariableModel
                {
                    Variable = variable.Variable,
                    Value = variable.Value,
                    FallbackValue = variable.FallbackValue
                };

                // Auto-populate Value field for LeadRat variables if it's null/empty
                if (string.IsNullOrEmpty(processedVariable.Value) &&
                    LeadRatVariableMapper.IsLeadRatVariable(processedVariable.Variable))
                {
                    var fieldPath = LeadRatVariableMapper.GetLeadRatVariableFieldPath(processedVariable.Variable);
                    if (!string.IsNullOrEmpty(fieldPath))
                    {
                        processedVariable.Value = fieldPath;
                        var apiSource = LeadRatVariableMapper.GetApiSource(processedVariable.Variable);

                        _logger.LogInformation("Auto-populated LeadRat variable: {Variable} → {FieldPath} (API: {ApiSource})",
                            processedVariable.Variable, fieldPath, apiSource);
                    }
                }

                processedVariables.Add(processedVariable);
            }

            return processedVariables;
        }

        /// <summary>
        /// Process interactive message variables using EXACT same pattern as campaigns
        /// Similar to ProcessTemplateWithLeadratVariable in your campaign system
        /// Enhanced with automatic LeadRat variable detection and population
        /// </summary>
        private async Task<string> ProcessInteractiveMessageVariablesAsync(InteractiveMessageModel interactiveMessage, Contacts contact)
        {
            try
            {
                var messageBody = interactiveMessage.Body;
                var variables = interactiveMessage.Variables;

                if (variables == null || !variables.Any())
                {
                    return messageBody;
                }

                // Check if any variables are Leadrat variables (same logic as campaigns)
                var hasLeadratVariables = variables.Any(v => StringHelper.IsLeadratVariable(v.Variable));

                if (!hasLeadratVariables)
                {
                    // Process with local variables only ({{1}}, {{2}}) - similar to ProcessTemplateWithLocalVariable
                    return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                }
                else
                {
                    // Process with Leadrat variables (#name#, #phone#) - similar to ProcessTemplateWithLeadratVariable
                    // Only extract variables when we have Leadrat variables
                    var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);

                    if (!extractedVariables.Any())
                    {
                        // No Leadrat variables found in message body, fallback to local processing
                        return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                    }

                    return await ProcessInteractiveMessageWithLeadratVariablesAsync(updatedMessage, variables, extractedVariables, contact);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing interactive message variables, using fallback");
                return ProcessInteractiveMessageWithLocalVariables(interactiveMessage.Body, interactiveMessage.Variables, contact);
            }
        }
        private string ProcessInteractiveMessageWithLocalVariables(string messageBody, List<VariableModel> variables, Contacts contact)
        {
            try
            {
                if (variables == null || !variables.Any())
                {
                    return messageBody;
                }

                var contactProperties = typeof(Contacts).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(contact)?.ToString() ?? string.Empty);
                var variableValuesList = new List<string>();

                var sortedVariables = variables
                    .Where(v => v.Variable.StartsWith("{{") && v.Variable.EndsWith("}}"))
                    .OrderBy(v =>
                    {
                        var numberStr = v.Variable.Trim('{', '}');
                        return int.TryParse(numberStr, out var number) ? number : int.MaxValue;
                    })
                    .ToList();

                foreach (var variable in sortedVariables)
                {
                    var value = contactProperties.ContainsKey(variable.Value) && !string.IsNullOrEmpty(contactProperties[variable.Value])
                        ? contactProperties[variable.Value]
                        : variable.FallbackValue;
                    variableValuesList.Add(value);
                }

                return StringHelper.ReplacePlaceholders(messageBody, variableValuesList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing local variables");
                return messageBody;
            }
        }
        private async Task<string> ProcessInteractiveMessageWithLeadratVariablesAsync(string messageBody, List<VariableModel> variables, List<string> extractedVariables, Contacts contact)
        {
            try
            {
                var businessDetails = await _dbContext.BusinessDetails.FirstOrDefaultAsync(i => i.Id == contact.BusinessId);

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Add("tenant", businessDetails.TenantId);

                // **Step 1: Get Leadrat Data**
                _logger.LogInformation("Simple Direct Mapping: Getting Leadrat data for contact: {Contact}", contact.Contact);
                var leadratResponse = await httpClient.GetAsync(LeadratApiUrls.GetLeadApiUrl(contact.Contact, contact.CountryCode));

                if (!leadratResponse.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Leadrat API failed, using local variables");
                    return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                }

                var leadratJsonString = await leadratResponse.Content.ReadAsStringAsync();
                var leadratJsonResponse = string.IsNullOrWhiteSpace(leadratJsonString) ? new JObject() : JsonConvert.DeserializeObject<JObject>(leadratJsonString);
                var leadratDataDict = ObjectHelper.ConvertJObjectToDictionary(leadratJsonResponse);

                // **Step 2: Get User Data (if assignment ID exists)**
                Dictionary<string, object> userDataDict = new Dictionary<string, object>();
                var assignmentIdValue = GetValueFromApiData(leadratDataDict, "data.assignTo") ?? GetValueFromApiData(leadratDataDict, "assignTo");

                if (!string.IsNullOrEmpty(assignmentIdValue) && Guid.TryParse(assignmentIdValue, out var assignmentId))
                {
                    try
                    {
                        _logger.LogInformation("Simple Direct Mapping: Getting user details for assignment ID: {AssignmentId}", assignmentId);
                        var userResponse = await httpClient.GetAsync(LeadratApiUrls.GetUserDetailsApiUrl(assignmentId));

                        if (userResponse.IsSuccessStatusCode)
                        {
                            var userJsonString = await userResponse.Content.ReadAsStringAsync();
                            var userJsonResponse = string.IsNullOrWhiteSpace(userJsonString) ? new JObject() : JsonConvert.DeserializeObject<JObject>(userJsonResponse);
                            userDataDict = ObjectHelper.ConvertJObjectToDictionary(userJsonResponse);
                            _logger.LogInformation("Simple Direct Mapping: Got user data with {Count} fields", userDataDict.Count);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "User API call failed, continuing with Leadrat data only");
                    }
                }

                // **Step 3: Process Variables Using LeadRatVariableMapper**
                _logger.LogInformation("Processing variables using LeadRatVariableMapper approach");

                // Separate agent variables from regular Leadrat variables
                var agentVariables = extractedVariables.Where(LeadRatVariableMapper.IsAgentVariable).ToList();
                var regularLeadratVariables = extractedVariables.Except(agentVariables).ToList();

                _logger.LogInformation("Found {AgentVarCount} agent variables and {RegularVarCount} regular variables",
                    agentVariables.Count, regularLeadratVariables.Count);

                // Process agent variables using LeadRatVariableMapper
                var agentVariableMappings = new Dictionary<string, string>();
                if (agentVariables.Any())
                {
                    agentVariableMappings = await ProcessAgentVariablesAsync(agentVariables, variables, contact);
                    _logger.LogInformation("LeadRatVariableMapper processed {Count} agent variables", agentVariableMappings.Count);
                }

                // Process regular Leadrat variables using existing logic
                var regularVariableMappings = new Dictionary<string, string>();
                foreach (var variable in regularLeadratVariables)
                {
                    var matchingVariable = variables.FirstOrDefault(v => v.Variable == variable);
                    if (matchingVariable != null)
                    {
                        var value = GetValueFromApiData(leadratDataDict, matchingVariable.Value, variable) ?? matchingVariable.FallbackValue;
                        regularVariableMappings[variable] = value;
                    }
                }

                // Combine all variable mappings and build final values list
                var variableValuesList = new List<string>();
                foreach (var extractedVar in extractedVariables)
                {
                    string value = null;

                    // Try agent variables first
                    if (agentVariableMappings.TryGetValue(extractedVar, out var agentValue))
                    {
                        value = agentValue;
                        _logger.LogDebug("Variable {Variable} resolved from agent mapping: {Value}", extractedVar, value);
                    }
                    // Then try regular variables
                    else if (regularVariableMappings.TryGetValue(extractedVar, out var regularValue))
                    {
                        value = regularValue;
                        _logger.LogDebug("Variable {Variable} resolved from regular mapping: {Value}", extractedVar, value);
                    }
                    // Finally use fallback
                    else
                    {
                        var matchingVariable = variables.FirstOrDefault(v => v.Variable == extractedVar);
                        value = matchingVariable?.FallbackValue ?? extractedVar;
                        _logger.LogDebug("Variable {Variable} using fallback: {Value}", extractedVar, value);
                    }

                    variableValuesList.Add(value);
                }

                return StringHelper.ReplacePlaceholders(messageBody, variableValuesList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LeadRatVariableMapper processing, falling back to local variables");
                return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
            }
        }

        private string GetValueFromApiData(Dictionary<string, object> apiData, string keyPath, string variableName = null)
        {
            try
            {

                object foundValue = null;
                if (apiData.ContainsKey(keyPath))
                {
                    foundValue = apiData[keyPath];
                }
                else
                {
                    var exactMatch = apiData.FirstOrDefault(kvp =>
                        string.Equals(kvp.Key, keyPath, StringComparison.OrdinalIgnoreCase));
                    if (!exactMatch.Equals(default(KeyValuePair<string, object>)))
                    {
                        foundValue = exactMatch.Value;
                    }
                    else
                    {
                        // Third, try partial match (if keyPath is "name", look for "*.name" patterns)
                        var partialMatch = apiData.FirstOrDefault(kvp =>
                            kvp.Key.EndsWith($".{keyPath}", StringComparison.OrdinalIgnoreCase) ||
                            kvp.Key.EndsWith($".{keyPath.ToLower()}", StringComparison.OrdinalIgnoreCase));
                        if (!partialMatch.Equals(default(KeyValuePair<string, object>)))
                        {
                            foundValue = partialMatch.Value;
                        }
                        else
                        {
                            // Fourth, try contains match (if keyPath is "name", look for any key containing "name")
                            var containsMatch = apiData.FirstOrDefault(kvp =>
                                kvp.Key.Contains(keyPath, StringComparison.OrdinalIgnoreCase));
                            if (!containsMatch.Equals(default(KeyValuePair<string, object>)))
                            {
                                foundValue = containsMatch.Value;
                            }
                        }
                    }
                }

                if (foundValue == null)
                {
                    return null;
                }

                // Apply special formatting based on variable name (similar to your campaign example)
                if (!string.IsNullOrEmpty(variableName))
                {
                    // Parse datetime and convert UTC to local time
                    if (DateTime.TryParse(foundValue.ToString(), out var parsedDateTime))
                    {
                        // Convert UTC to local time (similar to your StringHelper.GetLocalDateTime)
                        DateTime localDateTime;
                        if (parsedDateTime.Kind == DateTimeKind.Utc || foundValue.ToString().EndsWith("Z", StringComparison.OrdinalIgnoreCase))
                        {
                            // Force UTC then convert
                            localDateTime = DateTime.SpecifyKind(parsedDateTime, DateTimeKind.Utc).ToLocalTime();
                        }
                        else if (parsedDateTime.Kind == DateTimeKind.Unspecified)
                        {
                            // If you know your data is UTC but just missing the 'Z', force convert
                            localDateTime = DateTime.SpecifyKind(parsedDateTime, DateTimeKind.Utc).ToLocalTime();
                        }
                        else
                        {
                            localDateTime = parsedDateTime; // Already local
                        }

                        // Handle date formatting (similar to your #date# variable)
                        if (variableName.Equals("#date#", StringComparison.OrdinalIgnoreCase) ||
                            variableName.Contains("date", StringComparison.OrdinalIgnoreCase))
                        {
                            return localDateTime.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
                        }

                        // Handle time formatting (similar to your #time# variable)
                        if (variableName.Equals("#time#", StringComparison.OrdinalIgnoreCase) ||
                            variableName.Contains("time", StringComparison.OrdinalIgnoreCase))
                        {
                            return localDateTime.ToString("hh:mm tt", CultureInfo.InvariantCulture);
                        }

                        // Handle datetime formatting for scheduledDate
                        if (keyPath.Contains("scheduledDate", StringComparison.OrdinalIgnoreCase))
                        {
                            if (variableName.Equals("#date#", StringComparison.OrdinalIgnoreCase) ||
                                variableName.Contains("date", StringComparison.OrdinalIgnoreCase))
                            {
                                return localDateTime.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
                            }
                            else if (variableName.Equals("#time#", StringComparison.OrdinalIgnoreCase) ||
                                     variableName.Contains("time", StringComparison.OrdinalIgnoreCase))
                            {
                                return localDateTime.ToString("HH:mm tt", CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                return localDateTime.ToString("dd-MMM-yyyy HH:mm tt", CultureInfo.InvariantCulture);
                            }
                        }

                        // Default datetime format if no specific formatting matched
                        return localDateTime.ToString("dd-MMM-yyyy HH:mm tt", CultureInfo.InvariantCulture);
                    }
                }

                return foundValue?.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Simple Direct Mapping - बहुत आसान approach
        /// Try both APIs and return first found value
        /// </summary>
        private string GetSimpleVariableValue(string fieldName, Dictionary<string, object> leadratData, Dictionary<string, object> userData, string variableName = null)
        {
            try
            {
                // पहले Leadrat API में try करें
                var leadratValue = GetValueFromApiData(leadratData, fieldName, variableName);
                if (!string.IsNullOrEmpty(leadratValue))
                {
                    _logger.LogDebug("Found {FieldName} in Leadrat API: {Value}", fieldName, leadratValue);
                    return leadratValue;
                }

                // फिर User API में try करें
                if (userData.Any())
                {
                    var userValue = GetValueFromApiData(userData, fieldName, variableName);
                    if (!string.IsNullOrEmpty(userValue))
                    {
                        _logger.LogDebug("Found {FieldName} in User API: {Value}", fieldName, userValue);
                        return userValue;
                    }
                }

                _logger.LogDebug("Field {FieldName} not found in either API", fieldName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting simple variable value for {FieldName}", fieldName);
                return null;
            }
        }

        private async Task<Guid?> ProcessHttpRequestNodeAsync(WorkflowNode node, Contacts contacts)
        {
            var httpRequest = node.PayloadModel.HttpRequest;
            if (httpRequest == null)
            {
                return null;
            }
            try
            {
                var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contacts.ContactId, contacts.WorkflowId);
                var variableValues = new List<VariableModel>();
                if (httpRequest.VariableValues != null)
                {
                    foreach (var variable in httpRequest.VariableValues)
                    {
                        var matchingTracker = previousResponses
                         .FirstOrDefault(t => t.AttributeName != null &&
                                  t.AttributeName.Equals(variable.Value, StringComparison.OrdinalIgnoreCase));

                        var variableModel = new VariableModel
                        {
                            Variable = variable.Variable,
                            Value = matchingTracker?.CustomerReponse ?? variable.Value,
                            FallbackValue = variable.FallbackValue
                        };
                        variableValues.Add(variableModel);
                    }
                }

                string processedJsonBody = httpRequest.JsonBody;
                if (!string.IsNullOrEmpty(processedJsonBody))
                {
                    processedJsonBody = await ReplaceVariablesInJsonBody(processedJsonBody, variableValues, contacts);
                }
                using var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromSeconds(30);

                var request = new HttpRequestMessage(
                    new HttpMethod(httpRequest.Method),
                    httpRequest.Url
                );

                if (httpRequest.Headers != null)
                {
                    foreach (var header in httpRequest.Headers)
                    {
                        if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                            continue;

                        request.Headers.Add(header.Key, header.Value.ToString());
                    }
                }
                if (httpRequest.QueryParameters != null)
                {
                    var queryString = string.Join("&",
                        httpRequest.QueryParameters.Select(kvp =>
                            $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value.ToString())}"));

                    if (!string.IsNullOrEmpty(queryString))
                    {
                        request.RequestUri = new Uri($"{httpRequest.Url}?{queryString}");
                    }
                }

                if (!string.IsNullOrEmpty(processedJsonBody))
                {
                    request.Content = new StringContent(
                        processedJsonBody,
                        Encoding.UTF8,
                        "application/json"
                    );
                }
                else if (httpRequest.FormData != null && httpRequest.FormData.Any())
                {
                    var formContent = new MultipartFormDataContent();
                    foreach (var item in httpRequest.FormData)
                    {
                        formContent.Add(new StringContent(item.Value.ToString()), item.Key);
                    }
                    request.Content = formContent;
                }
                var response = await client.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                if (!response.IsSuccessStatusCode)
                {
                    // return null;
                    return edge?.Targets.FirstOrDefault()?.TargetNodeId;
                }
                return edge?.Targets.FirstOrDefault()?.TargetNodeId;
            }
            catch (HttpRequestException ex)
            {
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task<Guid?> ProcessTemplateNodeAsync(WorkflowNode node, Contacts contacts)
        {
            var template = new TemplateModel();
            try
            {
                template = node.PayloadModel.Template;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            if (template == null)
            {
                return null;
            }

            try
            {
                var business = await _dbContext.BusinessDetailsMetas
                    .FirstOrDefaultAsync(b => b.BusinessId == contacts.BusinessId.ToString());

                if (business == null)
                {
                    return null;
                }

                var templateDetails = (await _genericRepository.GetByObjectAsync<Template>(new Dictionary<string, object> { { "BusinessId", business.BusinessId }, { "TemplateId", template.TemplateId ?? Guid.Empty } }, "Templates"))
                          .FirstOrDefault();
                var buttons = await _genericRepository.GetByObjectAsync<Button>(new Dictionary<string, object> { { "TemplateId", template.TemplateId ?? Guid.Empty } }, "ButtonDetails");

                if (templateDetails == null)
                {
                    return null;
                }
                var phoneNumber = $"{contacts.CountryCode}{contacts.Contact}";

                var bodyVariables = template.BodyVariableValues ?? new List<string>();
                var headerVariable = template.HeaderValue ?? string.Empty;

                var templateRequest = new TemplateRequestDto
                {
                    TemplateName = templateDetails.TemplateName,
                    CountryCode = contacts.CountryCode,
                    Contact = contacts.Contact,
                    Name = contacts.Name,
                    HeaderValue = headerVariable,
                    BodyVariableValues = bodyVariables
                };

                Guid companyId = Guid.Parse(business.BusinessId);
                var templateService = _serviceProvider.GetRequiredService<ITemplate>();

                var conv = await templateService.GetSendTemplateAsync(business.BusinessId, companyId, templateRequest);

                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                return edge?.Targets.FirstOrDefault()?.TargetNodeId;
            }
            catch (Exception ex)
            {

                return null;
            }
        }
        private async Task<Guid?> ProcessConditionNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var condition = node.PayloadModel?.Condition;
                if (condition == null)
                {
                    return null;
                }
                var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contacts.ContactId, contacts.WorkflowId);
                if (previousResponses == null || !previousResponses.Any())
                {
                    return null;
                }

                var matchingTracker = previousResponses
                                    .FirstOrDefault(t => t.AttributeName != null &&
                                     t.AttributeName.Equals(condition.Attribute, StringComparison.OrdinalIgnoreCase));

                if (matchingTracker == null)
                {

                    matchingTracker = previousResponses
                        .OrderByDescending(t => t.CreatedAt)
                        .FirstOrDefault(t => t.NodeType == NodeType.InteractiveMessage && !string.IsNullOrEmpty(t.CustomerReponse));
                }
                var edges = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .Where(e => e.SourceId == node.Id)
                    .ToListAsync();

                if (!edges.Any())
                {
                    return null;
                }

                var allTargets = edges.SelectMany(e => e.Targets).ToList();

                var conditionButtons = condition.Buttons;
                if (conditionButtons != null && conditionButtons.Any())
                {
                    var buttonTargetMap = new Dictionary<string, WorkflowEdgeTargetNode>();
                    foreach (var target in allTargets)
                    {
                        // Extract the button ID from the source handle
                        var sourceHandle = edges.FirstOrDefault(e => e.Targets.Contains(target))?.SourceHandle;
                        if (sourceHandle != null && sourceHandle.Contains("conditionButtonId-"))
                        {
                            var buttonId = sourceHandle.Split("conditionButtonId-").Last();
                            buttonTargetMap[buttonId] = target;
                        }
                    }

                    // Find the true path button
                    var trueButton = conditionButtons.FirstOrDefault(b => b.Name.Contains("True", StringComparison.OrdinalIgnoreCase));
                    var falseButton = conditionButtons.FirstOrDefault(b => b.Name.Contains("False", StringComparison.OrdinalIgnoreCase));

                    if (trueButton != null && falseButton != null &&
                        buttonTargetMap.ContainsKey(trueButton.Id) &&
                        buttonTargetMap.ContainsKey(falseButton.Id))
                    {
                        bool conditionResult = false;

                        if (matchingTracker != null)
                        {
                            switch (condition.Operator)
                            {
                                case ConditionOperator.Equals:
                                    conditionResult = string.Equals(matchingTracker.CustomerReponse, condition.Value, StringComparison.OrdinalIgnoreCase);
                                    break;
                                case ConditionOperator.NotEquals:
                                    conditionResult = !string.Equals(matchingTracker.CustomerReponse, condition.Value, StringComparison.OrdinalIgnoreCase);
                                    break;
                                case ConditionOperator.GreaterThan:
                                    conditionResult = double.TryParse(matchingTracker.CustomerReponse, out double inputNum) &&
                                                    double.TryParse(condition.Value, out double conditionNum) &&
                                                    inputNum > conditionNum;
                                    break;
                                case ConditionOperator.LessThan:
                                    conditionResult = double.TryParse(matchingTracker.CustomerReponse, out double inputNum2) &&
                                                    double.TryParse(condition.Value, out double conditionNum2) &&
                                                    inputNum2 < conditionNum2;
                                    break;
                                case ConditionOperator.Contains:
                                    conditionResult = matchingTracker.CustomerReponse?.Contains(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                case ConditionOperator.StartsWith:
                                    conditionResult = matchingTracker.CustomerReponse?.StartsWith(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                case ConditionOperator.EndsWith:
                                    conditionResult = matchingTracker.CustomerReponse?.EndsWith(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                default:
                                    conditionResult = false;
                                    break;
                            }
                        }

                        var targetButton = conditionResult ? trueButton : falseButton;
                        var targetNodeId = buttonTargetMap[targetButton.Id].TargetNodeId;
                        return targetNodeId;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// Process agent variables using the static LeadRatVariableMapper
        /// Handles the two-step API process internally
        /// </summary>
        private async Task<Dictionary<string, string>> ProcessAgentVariablesAsync(List<string> agentVariables, List<VariableModel> variables, Contacts contact)
        {
            var result = new Dictionary<string, string>();

            try
            {
                // Get user details using two-step API process
                var userDetailsDict = await GetUserDetailsForAgentVariablesAsync(contact);

                if (!userDetailsDict.Any())
                {
                    _logger.LogWarning("No user details retrieved for agent variables");
                    // Return fallback values
                    foreach (var variable in agentVariables)
                    {
                        var matchingVariable = variables.FirstOrDefault(v => v.Variable == variable);
                        result[variable] = matchingVariable?.FallbackValue ?? "N/A";
                    }
                    return result;
                }

                // Map each agent variable using LeadRatVariableMapper
                foreach (var variable in agentVariables)
                {
                    var fieldPath = LeadRatVariableMapper.GetAgentVariableFieldPath(variable);
                    if (!string.IsNullOrEmpty(fieldPath))
                    {
                        var value = GetValueFromApiData(userDetailsDict, fieldPath, variable);
                        if (!string.IsNullOrEmpty(value))
                        {
                            result[variable] = value;
                            _logger.LogDebug("Agent variable {Variable} mapped to: {Value}", variable, value);
                        }
                        else
                        {
                            var matchingVariable = variables.FirstOrDefault(v => v.Variable == variable);
                            result[variable] = matchingVariable?.FallbackValue ?? "N/A";
                            _logger.LogDebug("Agent variable {Variable} using fallback: {Value}", variable, result[variable]);
                        }
                    }
                    else
                    {
                        var matchingVariable = variables.FirstOrDefault(v => v.Variable == variable);
                        result[variable] = matchingVariable?.FallbackValue ?? "N/A";
                        _logger.LogDebug("Agent variable {Variable} not found in mapping, using fallback: {Value}", variable, result[variable]);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing agent variables");
                // Return fallback values for all variables
                foreach (var variable in agentVariables)
                {
                    var matchingVariable = variables.FirstOrDefault(v => v.Variable == variable);
                    result[variable] = matchingVariable?.FallbackValue ?? "N/A";
                }
                return result;
            }
        }

        /// <summary>
        /// Execute the two-step API process to get user details for agent variables
        /// Step 1: Get assignment ID from Leadrat API
        /// Step 2: Get user details using assignment ID
        /// </summary>
        private async Task<Dictionary<string, object>> GetUserDetailsForAgentVariablesAsync(Contacts contact)
        {
            try
            {
                var businessDetails = await _dbContext.BusinessDetails.FirstOrDefaultAsync(i => i.Id == contact.BusinessId);
                if (businessDetails == null)
                {
                    _logger.LogWarning("Business details not found for contact: {ContactId}", contact.ContactId);
                    return new Dictionary<string, object>();
                }

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Add("tenant", businessDetails.TenantId);

                // **Step 1: Get Assignment ID from Leadrat API**
                _logger.LogInformation("LeadRatMapper - Step 1: Calling Leadrat API for contact: {Contact}", contact.Contact);
                var leadratResponse = await httpClient.GetAsync(LeadratApiUrls.GetLeadApiUrl(contact.Contact, contact.CountryCode));

                if (!leadratResponse.IsSuccessStatusCode)
                {
                    _logger.LogWarning("LeadRatMapper - Step 1 failed: Leadrat API call failed with status: {StatusCode}", leadratResponse.StatusCode);
                    return new Dictionary<string, object>();
                }

                var leadratJsonString = await leadratResponse.Content.ReadAsStringAsync();
                var leadratJsonResponse = string.IsNullOrWhiteSpace(leadratJsonString) ? new JObject() : JsonConvert.DeserializeObject<JObject>(leadratJsonString);
                var leadratDataDict = ObjectHelper.ConvertJObjectToDictionary(leadratJsonResponse);

                // Extract Assignment ID from the "data.assignTo" field
                var assignmentIdValue = GetValueFromApiData(leadratDataDict, "data.assignTo") ??
                                       GetValueFromApiData(leadratDataDict, "assignTo");

                if (string.IsNullOrEmpty(assignmentIdValue) || !Guid.TryParse(assignmentIdValue, out var assignmentId))
                {
                    _logger.LogWarning("LeadRatMapper - Step 1: Assignment ID not found or invalid in Leadrat response");
                    return new Dictionary<string, object>();
                }

                _logger.LogInformation("LeadRatMapper - Step 1 success: Found assignment ID: {AssignmentId}", assignmentId);

                // **Step 2: Get User Details using Assignment ID**
                _logger.LogInformation("LeadRatMapper - Step 2: Calling User Details API for assignment ID: {AssignmentId}", assignmentId);
                var userDetailsResponse = await httpClient.GetAsync(LeadratApiUrls.GetUserDetailsApiUrl(assignmentId));

                if (!userDetailsResponse.IsSuccessStatusCode)
                {
                    _logger.LogWarning("LeadRatMapper - Step 2 failed: User Details API call failed with status: {StatusCode}", userDetailsResponse.StatusCode);
                    return new Dictionary<string, object>();
                }

                var userJsonString = await userDetailsResponse.Content.ReadAsStringAsync();
                var userJsonResponse = string.IsNullOrWhiteSpace(userJsonString) ? new JObject() : JsonConvert.DeserializeObject<JObject>(userJsonResponse);
                var userDataDict = ObjectHelper.ConvertJObjectToDictionary(userJsonResponse);

                _logger.LogInformation("LeadRatMapper - Step 2 success: Retrieved user details with {Count} fields", userDataDict.Count);
                return userDataDict;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "LeadRatMapper - Error in two-step API process");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Process regular Leadrat variables (non-agent variables)
        /// </summary>
        private async Task<Dictionary<string, string>> ProcessRegularLeadratVariablesAsync(List<string> regularVariables, List<VariableModel> variables, Contacts contact)
        {
            var result = new Dictionary<string, string>();

            try
            {
                var businessDetails = await _dbContext.BusinessDetails.FirstOrDefaultAsync(i => i.Id == contact.BusinessId);
                if (businessDetails == null)
                {
                    return result;
                }

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Add("tenant", businessDetails.TenantId);

                var leadratResponse = await httpClient.GetAsync(LeadratApiUrls.GetLeadApiUrl(contact.Contact, contact.CountryCode));

                if (leadratResponse.IsSuccessStatusCode)
                {
                    var leadratJsonString = await leadratResponse.Content.ReadAsStringAsync();
                    var leadratJsonResponse = string.IsNullOrWhiteSpace(leadratJsonString) ? new JObject() : JsonConvert.DeserializeObject<JObject>(leadratJsonString);
                    var leadratDataDict = ObjectHelper.ConvertJObjectToDictionary(leadratJsonResponse);

                    foreach (var variable in regularVariables)
                    {
                        var matchingVariable = variables.FirstOrDefault(v => v.Variable == variable);
                        if (matchingVariable != null)
                        {
                            var value = GetValueFromApiData(leadratDataDict, matchingVariable.Value, variable) ?? matchingVariable.FallbackValue;
                            result[variable] = value;
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing regular Leadrat variables");
                return result;
            }
        }

    }
}
