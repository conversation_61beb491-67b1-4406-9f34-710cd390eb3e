using System.ComponentModel.DataAnnotations;

namespace EngagetoEntities.Entities
{
    /// <summary>
    /// Tracks scheduled Hangfire jobs for workflow nodes
    /// Used to clean up jobs when nodes are modified or deleted
    /// </summary>
    public class ScheduledJobTracker
    {
        [Key]
        public Guid Id { get; set; }
        
        /// <summary>
        /// Hangfire Job ID
        /// </summary>
        public string JobId { get; set; } = string.Empty;
        
        /// <summary>
        /// Workflow Node ID that scheduled this job
        /// </summary>
        public Guid NodeId { get; set; }
        
        /// <summary>
        /// Workflow ID
        /// </summary>
        public Guid WorkflowId { get; set; }
        
        /// <summary>
        /// Contact ID for whom the job was scheduled
        /// </summary>
        public Guid ContactId { get; set; }
        
        /// <summary>
        /// Business ID
        /// </summary>
        public string BusinessId { get; set; } = string.Empty;
        
        /// <summary>
        /// Job type (e.g., "reminder", "template", etc.)
        /// </summary>
        public string JobType { get; set; } = "reminder";
        
        /// <summary>
        /// Template name or job description
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// When the job was scheduled
        /// </summary>
        public DateTime ScheduledAt { get; set; }
        
        /// <summary>
        /// When the job is supposed to execute
        /// </summary>
        public DateTime ExecuteAt { get; set; }
        
        /// <summary>
        /// Job status (Scheduled, Executed, Cancelled, Failed)
        /// </summary>
        public string Status { get; set; } = "Scheduled";
        
        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// When this record was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// Navigation property to WorkflowNode
        /// </summary>
        public virtual WorkflowNode? Node { get; set; }
    }
}
