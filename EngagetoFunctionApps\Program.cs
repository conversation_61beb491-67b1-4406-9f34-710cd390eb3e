using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoContracts.TemplateContracts;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Dapper.Repositories.GenericRepositories;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dapper.Services.LogHistoryServices;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.UserEntities.Models;
using EngagetoRepository.GeneralServices;
using EngagetoRepository.MetaServices;
using EngagetoRepository.Services;
using EngagetoRepository.TemplateRepository;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Data;


var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
       .ConfigureAppConfiguration((context, config) =>
       {
           config.SetBasePath(Directory.GetCurrentDirectory())
               .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
               .AddEnvironmentVariables();
       })
    .ConfigureServices((context, services) =>
    {
        // Add Application Insights
        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        // Add Configuration
        services.AddSingleton<IConfiguration>(context.Configuration);

        var connectionString = context.Configuration["ConnStr"];
        services.AddDbContext<EngagetoEntities.DdContext.ApplicationDbContext>(options =>
               options.UseSqlServer(connectionString));
        
        services.AddScoped<IDbConnection>(sp =>
        {
            var connection = new SqlConnection(connectionString);
            return connection;
        });

        services.AddScoped<IUnitOfWork>(sp =>
        {
            var dbConnection = sp.GetRequiredService<IDbConnection>();
            dbConnection.Open();
            return new UnitOfWork(dbConnection);
        });

        services.AddScoped<IDapperConnectionFactory>(sp =>
        {
            return new DapperConnectionFactory(connectionString);
        });

        // Register Services
        services.AddScoped<IGenericRepository, GenericRepostry>();
        services.AddScoped<ICampaignScheduler, CampaignScheduler>();
        services.AddScoped<IEnvironmentService, EnvironmentService>();
        services.AddScoped<ILogHistoryService, LogHistoryService>();
        services.AddScoped<IInboxRepository, InboxRepository>();
        services.AddScoped<IMetaApiService, MetaApiService>();
        services.AddScoped<IMetaPayloadService, MetaPayloadService>();
        services.AddScoped<IUserIdentityService, UserIdentityService>();

        services.AddLogging(loggingBuilder =>
        {
            loggingBuilder.AddConsole();
            loggingBuilder.AddDebug();
        });

        services.AddHttpClient();

        services.AddCors(options =>
        {
            options.AddPolicy("AllowAllOrigins", builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            });
        });
    })
    .Build();

host.Run();