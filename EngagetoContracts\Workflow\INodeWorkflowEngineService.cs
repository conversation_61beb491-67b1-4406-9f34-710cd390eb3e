using EngagetoEntities.Entities;

namespace EngagetoContracts.Workflow
{
    public interface INodeWorkflowEngineService
    {
        Task ProcessWorkflowAsync(Contacts contacts, string? textMessage, bool? isNewCustomer = false, bool? isStatusChange = false, bool?  isProjectChange = false);

        /// <summary>
        /// Clean up scheduled jobs for a specific workflow node
        /// Call this when a node is edited, time changed, or deleted
        /// </summary>
        Task<int> CleanupScheduledJobsForNode(Guid nodeId);

        /// <summary>
        /// Clean up scheduled jobs for an entire workflow
        /// Call this when a workflow is deleted
        /// </summary>
        Task<int> CleanupScheduledJobsForWorkflow(Guid workflowId);
    }
}