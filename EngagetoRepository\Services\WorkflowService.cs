﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.Services;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.Workflow;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Text.Json;

namespace EngagetoRepository.Services
{
    public class WorkflowService : IWorkflowService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserIdentityService _userIdentityService;
        private readonly IWorkflowCustomResponseService _workflowCustomResponseService;
        private readonly INodeWorkflowEngineService _workflowEngineService;
        private readonly IMediaURL _mediUrl;
        private readonly IJobService _jobService;

        public WorkflowService(
            ApplicationDbContext dbContext,
            ILogger<WorkflowService> logger,
            IUserIdentityService userIdentityService,
            IWorkflowCustomResponseService workflowCustomResponseService,
            INodeWorkflowEngineService workflowEngineService,
            IMediaURL mediUrl,
            IJobService jobService)
        {
            _dbContext = dbContext;
            _userIdentityService = userIdentityService;
            _workflowCustomResponseService = workflowCustomResponseService;
            _workflowEngineService = workflowEngineService;
            _mediUrl = mediUrl;
            _jobService = jobService;
        }

        public async Task<ViewWorkFlowDto> CreateWorkflowAsync(CreateWorkflowDto workflow, Guid userId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var existingWorkflow = await _dbContext.Workflows
                    .FirstOrDefaultAsync(w => w.CompanyId == businessId
                                         && w.Name == workflow.Name && w.IsDeleted == false);
                if (existingWorkflow != null)
                {
                    throw new Exception("Workflow with this name already exists");
                }

                var newWorkflow = new Workflow
                {
                    Id = Guid.NewGuid(),
                    Name = workflow.Name,
                    CompanyId = businessId,
                    IsActive = true,
                    CreatedBy = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedBy = userId,
                    UpdatedAt = DateTime.UtcNow,

                };
                _dbContext.Workflows.Add(newWorkflow);
                await _dbContext.SaveChangesAsync();
                return newWorkflow.Adapt<ViewWorkFlowDto>();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<WorkflowResponseDto>> GetWorkflowsAsync(Guid companyId)
        {

            var businessId = _userIdentityService.BusinessId;

            var workflows = await _dbContext.Workflows
            .Where(w => w.CompanyId == businessId && !w.IsDeleted)
            .OrderByDescending(w => w.UpdatedAt)
            .Select(w => new WorkflowResponseDto
            {
                Id = w.Id,
                Name = w.Name,
                IsActive = w.IsActive,
                CreatedAt = w.CreatedAt,
                UpdatedAt = w.UpdatedAt
            })
          .ToListAsync();

            return workflows;
        }

        public async Task<bool> UpdateWorkflowAsync(Guid workflowId, UpdateWorkflowDto workflow)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var userId = _userIdentityService.UserId;
                var existingWorkflow = await _dbContext.Workflows
                    .FirstOrDefaultAsync(w => w.Id == workflowId && w.CompanyId == businessId && w.IsDeleted == false);

                if (existingWorkflow == null)
                    throw new Exception("Workflow not found");

                var existingEdges = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .Where(e => e.WorkflowId == existingWorkflow.Id && e.IsDeleted == false)
                    .ToListAsync();

                var existingNodes = await _dbContext.WorkflowNodes
                    .Where(n => n.WorkflowId == existingWorkflow.Id && n.IsDeleted == false)
                    .ToListAsync();

                var updatedNodeIds = workflow.Nodes?.Select(n => n.Id).ToList() ?? new List<Guid>();

                var updatedEdgeIds = workflow.Edges?
                    .Where(e => e.Id != null && e.Id != Guid.Empty)
                    .Select(e => e.Id)
                    .ToList() ?? new List<Guid>();

                var nodesToDelete = existingNodes
                    .Where(n => !updatedNodeIds.Contains(n.Id))
                    .Select(n => n.Id)
                    .ToList();

                // **CLEANUP SCHEDULED JOBS FOR DELETED NODES**
                if (nodesToDelete.Any())
                {
                    await CleanupScheduledJobsForNodes(nodesToDelete);
                }

                var edgesToDelete = existingEdges
                    .Where(e => !updatedEdgeIds.Contains(e.Id))
                    .ToList();

                var targetsToDelete = existingEdges
                    .SelectMany(e => e.Targets)
                    .Where(t =>
                        nodesToDelete.Contains(t.TargetNodeId) || // Target references a node being deleted
                        edgesToDelete.Select(e => e.Id).Contains(t.EdgeId)) // Target is part of an edge being deleted
                    .ToList();

                if (targetsToDelete.Any())
                {
                    _dbContext.WorkflowEdgeTargetNodes.RemoveRange(targetsToDelete);
                    await _dbContext.SaveChangesAsync(); // Save to delete targets first
                }

                // Now delete edges that are no longer needed
                if (edgesToDelete.Any())
                {
                    _dbContext.WorkflowEdges.RemoveRange(edgesToDelete);
                    await _dbContext.SaveChangesAsync(); // Save to delete edges next
                }

                // Now it's safe to delete nodes that are no longer needed
                var nodeEntitiesToDelete = existingNodes
                    .Where(n => !updatedNodeIds.Contains(n.Id))
                    .ToList();

                if (nodeEntitiesToDelete.Any())
                {
                    _dbContext.WorkflowNodes.RemoveRange(nodeEntitiesToDelete);
                    await _dbContext.SaveChangesAsync(); // Save to delete nodes
                }

                // Now handle the remaining nodes (update existing and add new)
                var newNodes = new List<WorkflowNode>();
                var customerResponses = new List<WorkflowCustomerResponse>();

                if (workflow.Nodes != null)
                {
                    foreach (var nodeDto in workflow.Nodes)
                    {
                        // Validate node data
                        ValidateNodeData(nodeDto.Type, nodeDto.Data);

                        // Add media file validation for interactive message nodes
                        if (nodeDto.Type == NodeType.InteractiveMessage && nodeDto.Data?.InteractiveMessage != null)
                        {
                            var interactiveMessage = nodeDto.Data.InteractiveMessage;
                            if (interactiveMessage.mediaType != MediaType.TEXT && interactiveMessage.mediaType != MediaType.NONE)
                            {
                                if (string.IsNullOrEmpty(interactiveMessage.mediaFile))
                                {
                                    throw new Exception("Media file URL is required for non-text media types.");
                                }

                                var result = await _mediUrl.ValidateMediaFile(interactiveMessage.mediaFile);
                                if (!result.IsValid)
                                {
                                    throw new Exception($"Invalid media file: {result.ErrorMessage}");
                                }
                            }
                        }

                        var existingNode = existingNodes.FirstOrDefault(n => n.Id == nodeDto.Id);

                        if (existingNode != null)
                        {
                            // **RESCHEDULE JOBS FOR UPDATED TEMPLATE NODES**
                            if (existingNode.Type == NodeType.Template && nodeDto.Type == NodeType.Template)
                            {
                                await RescheduleTemplateJobsForNode(existingNode.Id, nodeDto.Data?.Template);
                            }

                            // Update existing node
                            existingNode.Type = nodeDto.Type;
                            existingNode.Payload = nodeDto.Data != null ? System.Text.Json.JsonSerializer.Serialize(nodeDto.Data) : null;
                            existingNode.Position = System.Text.Json.JsonSerializer.Serialize(new PositionModel
                            {
                                X = nodeDto.PositionX,
                                Y = nodeDto.PositionY
                            });
                            existingNode.IsEntry = nodeDto.IsEntry;
                            existingNode.IsFinal = nodeDto.IsFinal;
                            existingNode.UpdatedBy = userId;
                            existingNode.UpdatedAt = DateTime.UtcNow;

                        }
                        else
                        {
                            // Insert new node
                            var newNode = new WorkflowNode
                            {
                                Id = nodeDto.Id,
                                WorkflowId = existingWorkflow.Id,
                                BusinessId = businessId,
                                Type = nodeDto.Type,
                                Payload = nodeDto.Data != null ? System.Text.Json.JsonSerializer.Serialize(nodeDto.Data) : null,
                                Position = System.Text.Json.JsonSerializer.Serialize(new PositionModel
                                {
                                    X = nodeDto.PositionX,
                                    Y = nodeDto.PositionY
                                }),
                                IsEntry = nodeDto.IsEntry,
                                IsFinal = nodeDto.IsFinal,
                                CreatedBy = userId,
                                CreatedAt = DateTime.UtcNow,
                                UpdatedBy = userId,
                                UpdatedAt = DateTime.UtcNow
                            };
                            newNodes.Add(newNode);
                        }

                        if (nodeDto.AttributeId != null && nodeDto.AttributeId != Guid.Empty.ToString())
                        {
                            var existingCustomResponse = await _dbContext.WorkflowCustomerResponse
                                                        .FirstOrDefaultAsync(r =>
                                                         r.WorkflowId == existingWorkflow.Id &&
                                                         r.NodeId == nodeDto.Id && !r.IsDeleted);

                            if (existingCustomResponse != null)
                            {
                                // Update existing record
                                existingCustomResponse.AttributeId = Guid.Parse(nodeDto.AttributeId);
                                existingCustomResponse.UpdatedAt = DateTime.UtcNow;
                                existingCustomResponse.UpdatedBy = userId;
                            }
                            else
                            {
                                // Insert new record
                                WorkflowCustomerResponse workflowCustomResponse = new WorkflowCustomerResponse
                                {
                                    Id = Guid.NewGuid(),
                                    NodeId = nodeDto.Id,
                                    WorkflowId = existingWorkflow.Id,
                                    BusinessId = Guid.Parse(businessId),
                                    UserId = userId,
                                    AttributeId = Guid.Parse(nodeDto.AttributeId),
                                    CreatedAt = DateTime.UtcNow,
                                    CreatedBy = userId,
                                    UpdatedAt = DateTime.UtcNow,
                                    UpdatedBy = userId
                                };
                                await _dbContext.WorkflowCustomerResponse.AddAsync(workflowCustomResponse);
                            }
                        }
                    }
                    if (customerResponses.Any())
                    {
                        await _dbContext.WorkflowCustomerResponse.AddRangeAsync(customerResponses);
                        await _dbContext.SaveChangesAsync();
                    }
                    if (newNodes.Any())
                    {
                        await _dbContext.WorkflowNodes.AddRangeAsync(newNodes);
                        await _dbContext.SaveChangesAsync(); // Save new nodes

                    }
                }

                // Finally, handle edges (update existing and add new)
                if (workflow.Edges != null && workflow.Edges.Any())
                {
                    var newEdges = new List<WorkflowEdge>();
                    var newTargets = new List<WorkflowEdgeTargetNode>();

                    // Get all node IDs after creation/update
                    var allNodeIds = await _dbContext.WorkflowNodes
                        .Where(n => n.WorkflowId == existingWorkflow.Id && n.IsDeleted == false)
                        .Select(n => n.Id)
                        .ToListAsync();

                    foreach (var edgeDto in workflow.Edges)
                    {
                        if (!allNodeIds.Contains(edgeDto.SourceNodeId))
                            throw new Exception($"Source Node ID {edgeDto.SourceNodeId} not found in Workflow");

                        foreach (var targetDto in edgeDto.Targets)
                        {
                            if (!allNodeIds.Contains(targetDto.TargetNodeId))
                                throw new Exception($"Target Node ID {targetDto.TargetNodeId} not found in Workflow");
                        }

                        WorkflowEdge edge;
                        if (edgeDto.Id != null && edgeDto.Id != Guid.Empty)
                        {
                            // Try to find existing edge
                            edge = existingEdges.FirstOrDefault(e => e.Id == edgeDto.Id);
                            if (edge != null)
                            {
                                // Update existing edge
                                edge.SourceId = edgeDto.SourceNodeId;
                                edge.SourceHandle = edgeDto.SourceHandle ?? "output";
                                edge.TargetHandle = edgeDto.TargetHandle ?? "input";
                                edge.Type = edgeDto.Type ?? "default";
                                edge.UpdatedBy = userId;
                                edge.UpdatedAt = DateTime.UtcNow;

                                // Delete existing targets (already handled above)
                            }
                            else
                            {
                                // Create new edge with the provided ID
                                edge = new WorkflowEdge
                                {
                                    Id = edgeDto.Id,
                                    WorkflowId = existingWorkflow.Id,
                                    BusinessId = businessId,
                                    SourceId = edgeDto.SourceNodeId,
                                    SourceHandle = edgeDto.SourceHandle ?? "output",
                                    TargetHandle = edgeDto.TargetHandle ?? "input",
                                    Type = edgeDto.Type ?? "default",
                                    CreatedBy = userId,
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedBy = userId,
                                    UpdatedAt = DateTime.UtcNow
                                };
                                newEdges.Add(edge);
                            }
                        }
                        else
                        {
                            // Create new edge with a generated ID
                            edge = new WorkflowEdge
                            {
                                Id = Guid.NewGuid(),
                                WorkflowId = existingWorkflow.Id,
                                BusinessId = businessId,
                                SourceId = edgeDto.SourceNodeId,
                                SourceHandle = edgeDto.SourceHandle ?? "output",
                                TargetHandle = edgeDto.TargetHandle ?? "input",
                                Type = edgeDto.Type ?? "default",
                                CreatedBy = userId,
                                CreatedAt = DateTime.UtcNow,
                                UpdatedBy = userId,
                                UpdatedAt = DateTime.UtcNow
                            };
                            newEdges.Add(edge);
                        }

                        // Add targets for edges
                        foreach (var targetDto in edgeDto.Targets)
                        {
                            // For existing edges, check if this target already exists
                            bool targetExists = false;

                            if (edge.Id != Guid.Empty && existingEdges.Any(e => e.Id == edge.Id))
                            {
                                // Get existing targets for this edge
                                var existingTargetsForEdge = await _dbContext.WorkflowEdgeTargetNodes
                                    .Where(t => t.EdgeId == edge.Id && t.TargetNodeId == targetDto.TargetNodeId)
                                    .ToListAsync();

                                if (existingTargetsForEdge.Any())
                                {
                                    // Update the existing target if needed
                                    var existingTarget = existingTargetsForEdge.First();
                                    existingTarget.Condition = targetDto.Condition;
                                    existingTarget.UpdatedBy = userId;
                                    existingTarget.UpdatedAt = DateTime.UtcNow;
                                    targetExists = true;
                                }
                            }

                            // Only create a new target if it doesn't exist
                            if (!targetExists)
                            {
                                var targetEdge = new WorkflowEdgeTargetNode
                                {
                                    Id = Guid.NewGuid(),
                                    EdgeId = edge.Id,
                                    BusinessId = businessId,
                                    TargetNodeId = targetDto.TargetNodeId,
                                    Condition = targetDto.Condition,
                                    CreatedBy = userId,
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedBy = userId,
                                    UpdatedAt = DateTime.UtcNow
                                };
                                newTargets.Add(targetEdge);
                            }
                        }
                    }

                    // Save changes
                    if (newEdges.Any())
                    {
                        await _dbContext.WorkflowEdges.AddRangeAsync(newEdges);
                        await _dbContext.SaveChangesAsync(); // Save new edges
                    }

                    if (newTargets.Any())
                    {
                        await _dbContext.WorkflowEdgeTargetNodes.AddRangeAsync(newTargets);
                        await _dbContext.SaveChangesAsync(); // Save new targets
                    }
                }

                // Update the workflow itself
                existingWorkflow.Name = workflow.Name ?? existingWorkflow.Name;
                existingWorkflow.IsActive = workflow.isActive;
                existingWorkflow.UpdatedBy = userId;
                existingWorkflow.UpdatedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync(); // Save workflow updates

                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> DeleteWorkflowAsync(Guid workflowId, Guid companyId)
        {
            var businessId = _userIdentityService.BusinessId;

            var workflow = await _dbContext.Workflows
                .FirstOrDefaultAsync(w => w.Id == workflowId && w.CompanyId == businessId);

            if (workflow == null)
                throw new Exception("Workflow not found");

            // **CLEANUP ALL SCHEDULED JOBS FOR THIS WORKFLOW**
            await CleanupScheduledJobsForWorkflow(workflowId);

            var edgeIds = await _dbContext.WorkflowEdges
                .Where(e => e.WorkflowId == workflowId)
                .Select(e => e.Id)
                .ToListAsync();

            var edgeTargetNodes = _dbContext.WorkflowEdgeTargetNodes
                .Where(etn => edgeIds.Contains(etn.EdgeId));
            _dbContext.WorkflowEdgeTargetNodes.RemoveRange(edgeTargetNodes);

            var edges = _dbContext.WorkflowEdges
                .Where(e => e.WorkflowId == workflowId);
            _dbContext.WorkflowEdges.RemoveRange(edges);

            var nodes = _dbContext.WorkflowNodes
                .Where(n => n.WorkflowId == workflowId);
            _dbContext.WorkflowNodes.RemoveRange(nodes);

            var entities = _dbContext.WorkflowEntities
                .Where(we => we.WorkflowListId == workflowId);
            _dbContext.WorkflowEntities.RemoveRange(entities);

            var keywords = _dbContext.WorkflowKeywords
                .Where(wk => wk.WorkflowId == workflowId).ToList();

            var nodeIds = keywords.Select(k => k.WorkflowNodeId).Distinct().ToList();

            var nodesToDelete = _dbContext.WorkflowNodes
                .Where(n => nodeIds.Contains(n.Id)).ToList();
            _dbContext.WorkflowNodes.RemoveRange(nodesToDelete);

            _dbContext.WorkflowKeywords.RemoveRange(keywords);

            _dbContext.Workflows.Remove(workflow);

            await _dbContext.SaveChangesAsync();
            return true;
        }


        public async Task<WorkflowDetailDto> GetWorkflowByIdAsync(Guid id)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var workflow = await _dbContext.Workflows
                    .Include(w => w.Nodes)
                        .ThenInclude(n => n.SourceEdges)
                            .ThenInclude(e => e.Targets).Where(e => !e.IsDeleted)
                    .Include(w => w.Nodes)
                        .ThenInclude(n => n.TargetEdges)
                    .FirstOrDefaultAsync(w => w.Id == id && w.CompanyId == businessId && !w.IsDeleted);

                if (workflow == null)
                {
                    throw new Exception("Workflow not found");
                }
                // Get all custom responses for this workflow to avoid multiple database queries
                var customResponses = await _dbContext.WorkflowCustomerResponse
                    .Where(r => r.WorkflowId == workflow.Id && r.BusinessId.ToString() == businessId && !r.IsDeleted)
                    .ToListAsync();

                var workflowDto = new WorkflowDetailDto
                {
                    Id = workflow.Id,
                    Name = workflow.Name,
                    IsActive = workflow.IsActive,
                    CreatedAt = workflow.CreatedAt,
                    UpdatedAt = workflow.UpdatedAt,
                    Nodes = workflow.Nodes.Select(n =>
                    {
                        // Find the custom response for this node
                        var customResponse = customResponses.FirstOrDefault(r => r.NodeId == n.Id);

                        // Create the node DTO
                        var nodeDto = new WorkflowNodeDto
                        {
                            Id = n.Id,
                            Type = n.Type,
                            Data = new NodePayloadDto
                            {
                                Template = n.PayloadModel?.Template?.Adapt<TemplateDto>(),
                                InteractiveMessage = n.PayloadModel?.InteractiveMessage?.Adapt<InteractiveMessageDto>(),
                                FlowStart = n.PayloadModel?.FlowStartModel?.Adapt<FlowStartDto>(),
                                Condition = n.PayloadModel?.Condition?.Adapt<ConditionDto>()
                            },
                            AttributeId = customResponse?.AttributeId?.ToString(),
                            PositionX = n.PositionJson?.X ?? 0,
                            PositionY = n.PositionJson?.Y ?? 0
                        };

                        if (n.Type == NodeType.HttpRequest && n.PayloadModel?.HttpRequest != null)
                        {
                            var httpRequest = n.PayloadModel.HttpRequest;
                            var httpRequestDto = new HttpRequestDto
                            {
                                Url = httpRequest.Url,
                                Method = httpRequest.Method,
                                ContentType = httpRequest.ContentType,
                                JsonBody = httpRequest.JsonBody,
                                VariableValues = httpRequest.VariableValues?.Adapt<List<VariableModel>>()
                            };
                            if (httpRequest.Headers != null)
                            {
                                httpRequestDto.Headers = new Dictionary<string, object>();
                                foreach (var header in httpRequest.Headers)
                                {
                                    string headerValue = ExtractStringValue(header.Value);
                                    httpRequestDto.Headers[header.Key] = headerValue;
                                }
                            }

                            if (httpRequest.QueryParameters != null)
                            {
                                httpRequestDto.QueryParameters = new Dictionary<string, object>();
                                foreach (var param in httpRequest.QueryParameters)
                                {
                                    string paramValue = ExtractStringValue(param.Value);
                                    httpRequestDto.QueryParameters[param.Key] = paramValue;
                                }
                            }

                            if (httpRequest.FormData != null)
                            {
                                httpRequestDto.FormData = new Dictionary<string, object>();
                                foreach (var form in httpRequest.FormData)
                                {
                                    // Extract the actual string value
                                    string formValue = ExtractStringValue(form.Value);
                                    httpRequestDto.FormData[form.Key] = formValue;
                                }
                            }

                            nodeDto.Data.HttpRequest = httpRequestDto;
                        }

                        return nodeDto;
                    }).ToList(),
                    Edges = workflow.Edges.Select(e => new WorkflowEdgeDto
                    {
                        Id = e.Id,
                        SourceNodeId = e.SourceId,
                        SourceHandle = e.SourceHandle,
                        TargetHandle = e.TargetHandle,
                        Type = e.Type,
                        Targets = e.Targets.Select(t => new WorkflowEdgeTargetDto
                        {
                            TargetNodeId = t.TargetNodeId,
                            Condition = t.Condition
                        }).ToList()
                    }).ToList()
                };

                return workflowDto;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private void ValidateNodeData(NodeType type, NodePayloadDto data)
        {
            try
            {
                switch (type)
                {
                    case NodeType.FlowStart:
                        // FlowStart doesn't need validation as it's a starting point
                        break;

                    case NodeType.InteractiveMessage:
                        var messageData = data.InteractiveMessage;
                        if (string.IsNullOrEmpty(messageData.Body))
                            throw new Exception("Interactive message must have text content");


                        break;

                    case NodeType.Template:
                        var templateData = data.Template;
                        if (templateData == null)
                            throw new Exception("Template data is required");
                        if (string.IsNullOrEmpty(templateData.TemplateName))
                            throw new Exception("Template name is required");
                        break;

                    case NodeType.HttpRequest:
                        var httpData = data.HttpRequest;
                        if (string.IsNullOrEmpty(httpData?.Url))
                            throw new Exception("HTTP request must have a URL");
                        if (string.IsNullOrEmpty(httpData.Method))
                            throw new Exception("HTTP method is required");
                        break;

                    case NodeType.Condition:
                        var conditionData = data.Condition;
                        if (!Enum.IsDefined(typeof(ConditionOperator), conditionData.Operator))
                            throw new Exception("Condition must have a valid operator");
                        break;

                    default:
                        throw new Exception($"Unknown node type: {type}");
                }
            }
            catch (Exception)
            {
                throw new Exception($"Invalid data format for node type {type}");
            }
        }

        public async Task<AddKeyWordResponseDto> AddKeywordsToWorkflowAsync(AddKeywordDto request)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var existingKeywords = await _dbContext.WorkflowKeywords
                    .Where(k => !k.IsDeleted &&
                                k.Workflow.CompanyId == businessId &&
                                !k.Workflow.IsDeleted)
                    .Select(k => k.Keyword.ToLower())
                    .ToListAsync();

                var addedKeywords = new List<string>();
                var failedKeywords = new List<string>();

                var newKeywords = request.Keywords?
                    .Where(keyword => !existingKeywords.Contains(keyword))
                    .Distinct()
                    .ToList();

                if (newKeywords != null && newKeywords.Any())
                {
                    var keywordEntities = newKeywords.Select(keyword => new WorkflowKeyword
                    {
                        Id = Guid.NewGuid(),
                        WorkflowId = request.WorkflowId,
                        Keyword = keyword.ToLower(),
                        CreatedBy = _userIdentityService.UserId,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedBy = _userIdentityService.UserId,
                        UpdatedAt = DateTime.UtcNow,
                        BusinessId = businessId,
                        WorkflowNodeId = request.WorkflowNodeId
                    }).ToList();

                    await _dbContext.WorkflowKeywords.AddRangeAsync(keywordEntities);
                    await _dbContext.SaveChangesAsync();

                    addedKeywords.AddRange(newKeywords);
                }

                failedKeywords.AddRange(request.Keywords.Except(addedKeywords));

                return new AddKeyWordResponseDto
                {
                    IsSuccess = true,
                    Message = "Keyword added completed.",
                    AddedKeywords = addedKeywords,
                    FailedKeywords = failedKeywords
                };
            }
            catch (Exception ex)
            {

                return new AddKeyWordResponseDto
                {
                    IsSuccess = false,
                    Message = "An unexpected error occurred while adding the keywords."
                };
            }
        }

        public async Task<bool> DeleteKeywordsAsync(Guid workflowId, List<string> keywords, Guid userId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var workflow = await _dbContext.Workflows
                    .AsNoTracking()
                    .FirstOrDefaultAsync(w => w.Id == workflowId && w.CompanyId == businessId);

                if (workflow == null)
                    throw new Exception("Workflow not found");

                var keywordsToDelete = await _dbContext.WorkflowKeywords
                    .Where(k => k.WorkflowId == workflowId &&
                                !k.IsDeleted &&
                                keywords.Contains(k.Keyword) &&
                                k.BusinessId == businessId)
                    .ToListAsync();

                if (!keywordsToDelete.Any())
                    throw new Exception("Keywords does not exist st the given workflow id.");

                //foreach (var keyword in keywordsToDelete)
                //{
                //    keyword.IsDeleted = true;
                //    keyword.UpdatedBy = userId;
                //    keyword.UpdatedAt = DateTime.UtcNow;
                //}

                _dbContext.WorkflowKeywords.RemoveRange(keywordsToDelete);
                await _dbContext.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public async Task<List<string>> GetAllKeywordsAsync(Guid workflowId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var workflowExists = await _dbContext.Workflows
               .AnyAsync(w => w.Id == workflowId && w.CompanyId == businessId && !w.IsDeleted);

                if (!workflowExists)
                    throw new Exception("Workflow not found");
                // Fetch keywords
                var keywords = await _dbContext.WorkflowKeywords
                    .Where(k => k.WorkflowId == workflowId && k.BusinessId == businessId && !k.IsDeleted)
                    .Select(k => k.Keyword)
                    .ToListAsync();

                return keywords;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<string>> GetKeywordsByBusinessId(Guid businessId)
        {
            try
            {
                var keywords = await _dbContext.WorkflowKeywords
                    .Where(k => k.BusinessId == businessId.ToString() && !k.IsDeleted)
                    .Select(k => k.Keyword)
                    .ToListAsync();

                return keywords;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<List<WorkflowNodeDto>> GetFlowStartNodesByBusinessIdAsync(Guid businessId)
        {
            var workflowNodes = await _dbContext.WorkflowNodes
                .Where(node => node.BusinessId == businessId.ToString() && node.Type == NodeType.FlowStart)
                .ToListAsync();

            var workflowNodeDtos = workflowNodes.Select(node => new WorkflowNodeDto
            {
                Id = node.Id,
                Type = node.Type,
                Data = new NodePayloadDto
                {
                    FlowStart = node.PayloadModel?.FlowStartModel?.Adapt<FlowStartDto>(),
                }
            }).ToList();

            return workflowNodeDtos;
        }

        public async Task<bool> ToggleWorkflowActiveStatusAsync(Guid workflowId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var userId = _userIdentityService.UserId;

                var workflow = await _dbContext.Workflows
                    .FirstOrDefaultAsync(w => w.Id == workflowId &&
                                             w.CompanyId == businessId &&
                                             !w.IsDeleted);

                if (workflow == null)
                    throw new Exception("Workflow not found");

                workflow.IsActive = !workflow.IsActive;
                workflow.UpdatedBy = userId;
                workflow.UpdatedAt = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();

                return workflow.IsActive;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private string ExtractStringValue(object value)
        {
            if (value == null)
                return string.Empty;
            try
            {
                if (value is string stringValue)
                {
                    return stringValue;
                }
                try
                {
                    var json = JsonConvert.SerializeObject(value);
                    var dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                    if (dict != null && dict.TryGetValue("valueKind", out var kindObj))
                    {
                        if (kindObj is long valueKind && valueKind == 3)
                        {
                            if (dict.TryGetValue("value", out var valueObj))
                            {
                                return valueObj?.ToString() ?? string.Empty;
                            }
                            var newDict = new Dictionary<string, object>(dict);
                            newDict.Remove("valueKind");
                            if (newDict.Count == 1)
                            {
                                return newDict.First().Value?.ToString() ?? string.Empty;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                }
                return value.ToString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                return value?.ToString() ?? string.Empty;
            }
        }



        public async Task<bool> ProcessWorkflowAsync(Contacts contacts, string message, bool isNewCustomer, bool isStatusChange, bool isProjectChange)
        {
            try
            {

                var businessId = contacts.BusinessId;

                var existingContact = await _dbContext.Contacts.FirstOrDefaultAsync(i => i.BusinessId == businessId && i.Contact == contacts.Contact);

                if (existingContact != null)
                {
                    await _workflowEngineService.ProcessWorkflowAsync(existingContact, message, isNewCustomer, isStatusChange, isProjectChange);
                    return true;
                }
                else
                {
                    await _workflowEngineService.ProcessWorkflowAsync(contacts, message, isNewCustomer, isStatusChange, isProjectChange);
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing workflow: {ex.Message}");
                return false;
            }
        }

        private async Task RescheduleTemplateJobsForNode(Guid nodeId, TemplateDto? newTemplate)
        {
            try
            {
                if (newTemplate == null)
                {
                    await _workflowEngineService.CleanupScheduledJobsForNode(nodeId);
                    return;
                }

                // **GET ALL CONTACTS WITH SCHEDULED JOBS FOR THIS NODE**
                // FIXED: Also include contacts that completed workflow but still have jobs from this node
                var contactsWithJobs = await _dbContext.Contacts
                    .Where(c => !string.IsNullOrEmpty(c.DelayResponseJobID) &&
                               (c.WorkFlowNodeId == nodeId ||
                                (c.WorkFlowNodeId == null && c.WorkflowId != null)))
                    .ToListAsync();

                // **FILTER TO ONLY CONTACTS THAT ACTUALLY BELONG TO THIS NODE'S WORKFLOW**
                var relevantContacts = new List<Contacts>();
                foreach (var contact in contactsWithJobs)
                {
                    if (contact.WorkFlowNodeId == nodeId)
                    {
                        // Contact is currently on this node
                        relevantContacts.Add(contact);
                    }
                    else if (contact.WorkFlowNodeId == null && contact.WorkflowId != null)
                    {
                        // Contact completed workflow, check if this node belongs to their workflow
                        var nodeInWorkflow = await _dbContext.WorkflowNodes
                            .AnyAsync(n => n.Id == nodeId && n.WorkflowId == contact.WorkflowId);
                        if (nodeInWorkflow)
                        {
                            relevantContacts.Add(contact);
                        }
                    }
                }

                Console.WriteLine($"Found {relevantContacts.Count} contacts with jobs for node {nodeId} (total checked: {contactsWithJobs.Count})");
                contactsWithJobs = relevantContacts;

                if (!contactsWithJobs.Any())
                {
                    return; // No jobs to reschedule
                }

                foreach (var contact in contactsWithJobs)
                {
                    try
                    {
                        // **RESCHEDULE JOB WITH UPDATED TEMPLATE**
                        await RescheduleJobForContact(contact, newTemplate);
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue with other contacts
                        Console.WriteLine($"Error rescheduling job for contact {contact.ContactId}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log but don't fail the workflow update
                Console.WriteLine($"Error rescheduling template jobs for node {nodeId}: {ex.Message}");
            }
        }
        private async Task RescheduleJobForContact(Contacts contact, TemplateDto template)
        {
            try
            {
                if (string.IsNullOrEmpty(contact.DelayResponseJobID))
                {
                    return; // No job to reschedule
                }
                if (!template.IsScheduleReminder)
                {
                    // If scheduling is disabled, delete job and clear ID
                    _jobService.Delete(contact.DelayResponseJobID);
                    contact.DelayResponseJobID = null;
                    await _dbContext.SaveChangesAsync();
                    return;
                }
                if (!template.TimeInMinutes.HasValue || template.TimeInMinutes.Value <= 0)
                {
                    _jobService.Delete(contact.DelayResponseJobID);
                    contact.DelayResponseJobID = null;
                    await _dbContext.SaveChangesAsync();
                    return;
                }

                if (!contact.ScheduledAt.HasValue)
                {
                    _jobService.Delete(contact.DelayResponseJobID);
                    contact.DelayResponseJobID = null;
                    await _dbContext.SaveChangesAsync();
                    return;
                }

                // **CALCULATE NEW REMINDER TIME**
                var scheduledDate = contact.ScheduledAt.Value;
                var timeInMinutes = template.TimeInMinutes.Value;

                DateTime scheduledDateUtc = scheduledDate.Kind == DateTimeKind.Utc
                    ? scheduledDate
                    : DateTime.SpecifyKind(scheduledDate, DateTimeKind.Utc);

                var reminderTime = scheduledDateUtc.AddMinutes(-timeInMinutes);
                var now = DateTime.UtcNow;

                if (reminderTime <= now)
                {
                    // Reminder time is in the past, delete job and clear ID
                    _jobService.Delete(contact.DelayResponseJobID);
                    contact.DelayResponseJobID = null;
                    await _dbContext.SaveChangesAsync();
                    return;
                }

                // **RESCHEDULE EXISTING JOB WITH NEW TIME**
                var reminderTimeOffset = new DateTimeOffset(reminderTime, TimeSpan.Zero);
                bool rescheduleSuccess = _jobService.Reschedule(contact.DelayResponseJobID, reminderTimeOffset);

                if (rescheduleSuccess)
                {
                    await _dbContext.SaveChangesAsync();
                    Console.WriteLine($"Successfully rescheduled job {contact.DelayResponseJobID} for contact {contact.ContactId} at {reminderTime}");
                }
                else
                {
                    contact.DelayResponseJobID = null;
                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                contact.DelayResponseJobID = null;
                await _dbContext.SaveChangesAsync();
            }
        }
        private async Task CleanupScheduledJobsForNodes(List<Guid> nodeIds)
        {
            try
            {
                foreach (var nodeId in nodeIds)
                {
                    await _workflowEngineService.CleanupScheduledJobsForNode(nodeId);
                }
            }
            catch (Exception ex)
            {

            }
        }

        private async Task CleanupScheduledJobsForWorkflow(Guid workflowId)
        {
            try
            {
                await _workflowEngineService.CleanupScheduledJobsForWorkflow(workflowId);
            }
            catch (Exception ex)
            {
                // Log but don't fail the workflow deletion
                // The workflow deletion should succeed even if job cleanup fails
            }
        }

    }
}



