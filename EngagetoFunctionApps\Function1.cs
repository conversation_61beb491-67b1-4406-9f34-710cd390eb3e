using EngagetoFunctionApps.Functions;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using System.Net;

namespace EngagetoFunctionApps
{
    public class Function1
    {
        [Function("ScheduleCampaign")]
        public static async Task<HttpResponseData> HttpStart(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "ScheduleCampaign")] HttpRequestData req,
            [DurableClient] DurableTaskClient client,
             FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("Processing ScheduleCampaign request.");
            var input = await req.ReadFromJsonAsync<InputPayload>();
            string instanceId = await client.ScheduleNewOrchestrationInstanceAsync(
                nameof(CampaignOrchestrator.RunCampaignOrchestrator),
                input);

            log.LogInformation($"Started orchestration with ID = '{instanceId}'.");

            return await client.CreateCheckStatusResponseAsync(req, instanceId);
        }


        [Function("ProcessingCampaignSubBatches")]
        public static async Task<HttpResponseData> ProcessingCampaignSubBatches(
           [HttpTrigger(AuthorizationLevel.Function, "post", Route = "ProcessCampaignSubBatches")] HttpRequestData req,
           [DurableClient] DurableTaskClient client,
            FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("Processing campaign sub batches request.");
            var input = await req.ReadFromJsonAsync<InputPayload>();
            string instanceId = await client.ScheduleNewOrchestrationInstanceAsync(
                nameof(CampaignOrchestrator.ProcessCampaignSubBatches),
                input);

            log.LogInformation($"Started orchestration with ID = '{instanceId}'.");

            return await client.CreateCheckStatusResponseAsync(req, instanceId);
        }


        [Function("TerminateCampaignJob")]
        public static async Task<HttpResponseData> TerminateCampaignJob(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "TerminateCampaignJob")] HttpRequestData req,
            [DurableClient] DurableTaskClient client,
            FunctionContext executionContext)
        {
            var log = executionContext.GetLogger("TerminateCampaignJob");
            var requestBody = await req.ReadFromJsonAsync<TerminateJobPayload>();

            if (string.IsNullOrEmpty(requestBody?.JobId))
            {
                return req.CreateResponse(HttpStatusCode.BadRequest);
            }

            try
            {
                await client.TerminateInstanceAsync(requestBody.JobId, "Terminated by user or business logic.");
                log.LogInformation($"Terminated orchestration with ID = '{requestBody.JobId}'.");
                return req.CreateResponse(HttpStatusCode.OK);
            }
            catch (Exception ex)
            {
                log.LogError(ex, $"Failed to terminate orchestration with ID = '{requestBody.JobId}'.");
                return req.CreateResponse(HttpStatusCode.InternalServerError);
            }
        }

    }
}
