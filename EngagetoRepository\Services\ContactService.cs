﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.UserDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace EngagetoRepository.Services
{
    public class ContactService : IContactService
    {
        private readonly IJobService _jobService;
        private readonly IContactImportScheduler _contactImportScheduler;
        private readonly IBlobStorageService _blobStorageService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IUserIdentityService _userIdentityService;

        public ContactService(IJobService jobService, IContactImportScheduler contactImportScheduler, IBlobStorageService blobStorageService, ApplicationDbContext dbContext, IContactRepositoryBase contactRepositoryBase , IUserIdentityService userIdentityService)
        {
            _jobService = jobService;
            _contactImportScheduler = contactImportScheduler;
            _blobStorageService = blobStorageService;
            _dbContext = dbContext;
            _contactRepository = contactRepositoryBase;
            _userIdentityService = userIdentityService;
        }

        public async Task<int> ContactImportAsync(UploadFileDto data, Guid businessId, Guid userId)
        {
            try
            {
                var uploadFiles = await _blobStorageService.UploadAsync(data);
                var tempFilePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid() + Path.GetExtension(data.File.FileName));
                using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await data.File.CopyToAsync(fileStream);
                }

                var contactImportTracker = new ContactImportTracker
                {
                    status = UploadStatus.Initiated,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId,
                    // FileName = Path.GetFileName(data),
                    FileName = uploadFiles.Name,
                    UploadedFilesId = uploadFiles.Id,
                    BusinessId =  businessId,
                    UserId = userId
                    
                };
                await _contactRepository.AddAsync(contactImportTracker);

                _jobService.Enqueue(() => _contactImportScheduler.ProcessImportAsync(tempFilePath, businessId, userId, contactImportTracker.Id, uploadFiles.UploadedFileName));
                return uploadFiles.Id;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }

        public async Task<PaginatedImportTrackerDto> GetContactImportTrackersAsync(int pageNumber, int pageSize)
        {
            try
            {
                  string businessId  =  _userIdentityService.BusinessId;
                var totalCount = _dbContext.ContactImportTrackers.Where(i=> i.BusinessId.ToString()==businessId).AsQueryable().Count();
                var paginatedData = await _dbContext.ContactImportTrackers.Where(i => i.BusinessId.ToString() == businessId)
                    .OrderByDescending(tracker => tracker.CreatedAt)
                    .Join(
                        _dbContext.Users,
                        tracker => tracker.CreatedBy,
                        user => user.Id,
                        (tracker, user) => new
                        {
                            Tracker = tracker, 
                            User = user.Adapt<UserInfoDto>()
                        }
                    )
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();
               
                var contactImportTrackerDtos = paginatedData
                    .Select(x =>
                    {
                        var trackerDto = x.Tracker.Adapt<ContactImportTrackerDto>();
                        trackerDto.CreateUser = x.User; 
                        trackerDto.UpdateUser = x.User;
                        return trackerDto;
                    })
                    .ToList();

                var result = new PaginatedImportTrackerDto
                {
                    TotalCount = totalCount,
                    Trackers = contactImportTrackerDtos
                };

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetContactImportTrackersAsync: {ex}");
                throw new InvalidOperationException($"Something went wrong: {ex.Message}");
            }
        }
        public async Task<ContactImportTracker> GetContactTrackerByIdAsync(int uploadedId)
        {
            var contactImportTracker = await _dbContext.ContactImportTrackers.FirstOrDefaultAsync(i => i.UploadedFilesId == uploadedId);
            return contactImportTracker ?? new();
        }
    }
}
