﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.MetaContracts;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.Json;


namespace EngagetoBackGroundJobs.Implementation
{
    public class CampaignScheduler : ICampaignScheduler
    {
        private readonly IGenericRepository _genericRepository;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IInboxRepository _inboxRepository;
        private readonly IMetaApiService _metaApiService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public CampaignScheduler(IGenericRepository genericRepository,
            ILogHistoryService logHistoryService, IInboxRepository inboxRepository,
            IMetaApiService metaApiService,
            IConfiguration configuration, IHttpClientFactory httpClientFactory)
        {
            _genericRepository = genericRepository;
            _logHistoryService = logHistoryService;
            _inboxRepository = inboxRepository;
            _metaApiService = metaApiService;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;

        }

        public async Task PartitionCampaignBatchesAsync(Campaign campaign, List<string> audienceList, bool isDevelopment)
        {
            var errorLog = new List<object>();
            await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignAsync", $"{"batchNumber"}:{audienceList.Count()},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", "ex.Message", JsonConvert.SerializeObject("ex.StackTrace"));
            int batchNumber = 0;
            try
            {

                if (audienceList == null)
                {
                    audienceList = new List<string>();
                }
                if ((campaign.UploadedFileId ?? 0) > 0)
                {
                    var audiance = await GetContactIdsToExcel(campaign.UploadedFileId ?? 0, campaign);
                    audienceList.AddRange(audiance);
                    audienceList = audienceList.Distinct().ToList();
                }
                int totalAudienceCount = audienceList?.Count ?? 0;
                var audienceBatches = new List<List<string>>();
                const int batchSize = 2000;
                const int pauseDurationMs = 60000;
                List<string> childScheduledJobIdsList = new List<string>();
                for (int i = 0; i < totalAudienceCount; i += batchSize)
                {
                    if (audienceList != null)
                    {
                        audienceBatches.Add(audienceList.Skip(i).Take(batchSize).ToList());
                    }
                }
                var baseScheduledTime = campaign.DateSetLive ?? DateTime.UtcNow;
                for (int i = 0; i < audienceBatches.Count; i++)
                {
                    var batchAudience = audienceBatches[i];

                    var batchPayload = new
                    {
                        CampaignId = campaign.CampaignId,
                        CampaignJsonData = campaign,
                        ScheduledTime = baseScheduledTime.AddMinutes(i),
                        Audiances = batchAudience
                    };

                    var response = await CallFunctionAsync(batchPayload, isDevelopment);
                    using var jsonDoc = JsonDocument.Parse(response);
                    var scheduledJobId = jsonDoc.RootElement.GetProperty("Id").GetString();
                    childScheduledJobIdsList.Add(scheduledJobId ?? string.Empty);

                    if (i <= audienceBatches.Count - 1)
                    {
                        // await Task.Delay(pauseDurationMs);
                        await _logHistoryService.SaveInformationLogHistoryAsyn(
                            "Campaign Batch Processing",
                            $"Pausing for 1 minute after processing batch {i + 1} of {audienceBatches.Count}",
                            campaign,
                            "Campaign Information");
                    }
                    batchNumber = i + 1;
                }
                string childScheduledJobIds = string.Join(",", childScheduledJobIdsList);
                var audiences = audienceList?.Select(audience => audience.ToString()).ToList();
                campaign.Audiance = audienceList != null && audienceList.Count() > 1000 ? string.Join(",", audiences) : null;
                var conditions = new Dictionary<string, object>()
                {
                     {"BusinessId", campaign.BusinessId },
                     {"CampaignId", campaign.CampaignId }
                };
                var columns = new List<string>() { "ChildScheduleJobIds" };
                await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", columns, campaign, conditions);
            }
            catch (Exception ex)
            {
                errorLog.Add(new
                {
                    BatchNumber = batchNumber,
                    BatchSize = audienceList.Count(),
                    ErrorType = "BatchProcessingFailed",
                    ErrorMessage = ex.Message,
                    ErrorDetails = ex.StackTrace,
                    Timestamp = DateTime.UtcNow
                });
                Console.WriteLine(ex.Message);
                await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignAsync", $"{batchNumber}:{audienceList.Count()},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));
            }

        }

        private async Task<string> CallFunctionAsync<T>(T requestBody, bool isDevelopment)
        {
            string functionUrl = "";
            if (isDevelopment)
            {
                functionUrl = _configuration["FunctionSettings:Dev_ProcessCampaignSubBatchesUrl"] ?? "";
            }
            else
            {
                functionUrl = _configuration["FunctionSettings:Prod_ProcessCampaignSubBatchesUrl"] ?? "";
            }
            if (string.IsNullOrEmpty(functionUrl))
            {
                throw new InvalidOperationException("Function URL is not configured.");
            }
            var client = _httpClientFactory.CreateClient("LongRunning");
            var jsonPayload = System.Text.Json.JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(functionUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            return result;
        }

        public async Task ProcessCampaignAsync(Campaign campaign, List<string> audienceList)
        {
            var campaignData = (await _genericRepository.GetByObjectAsync<Campaign>(new() { { "CampaignId", campaign.CampaignId } }, "Campaigns"))
              .FirstOrDefault();
            audienceList ??= new List<string>();
            if (campaignData.UploadedFileId != null && campaignData.UploadedFileId != 0)
            {
                var audiences = await GetContactIdsToExcel(campaign.UploadedFileId ?? 0, campaign);
                audienceList.AddRange(audiences);
                 audienceList.Distinct().ToList();
            }
            int totalAudienceCount = audienceList?.Count ?? 0;
            int pageSize = audienceList?.Count() ?? 0;
            var errorLog = new List<object>();
            if (totalAudienceCount > 0)
            {              
                int totalPages = (int)Math.Ceiling((double)totalAudienceCount / pageSize);

                for (int page = 1; page <= totalPages; page++)
                {
                    try
                    {
                        var isCompleted = page == totalPages;
                        bool success = false;
                        int retryCount = 0;
                        int maxRetries = 2;
                        TimeSpan delay = TimeSpan.FromSeconds(5);
                        while (!success && retryCount < maxRetries)
                        {
                            try
                            {
                                await ProcessCampaignBatchAsync(pageSize, page, campaignData ?? new(), audienceList ?? new(), isCompleted);
                                success = true;
                            }
                            catch (Exception ex)
                            {
                                retryCount++;
                                Console.WriteLine($"Attempt {retryCount} failed: {ex.Message}");

                                if (retryCount >= maxRetries)
                                {
                                    await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignAsync", $"{page}:{pageSize},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));
                                    throw;
                                }
                                errorLog.Add(new
                                {
                                    BatchNumber = page + 1,
                                    BatchSize = totalPages,
                                    ErrorType = "BatchProcessingFailed",
                                    ErrorMessage = ex.Message,
                                    ErrorDetails = ex.StackTrace,
                                    Timestamp = DateTime.UtcNow
                                });
                                await Task.Delay(delay);
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        errorLog.Add(new
                        {
                            BatchNumber = page + 1,
                            BatchSize = totalPages,
                            ErrorType = "BatchProcessingFailed",
                            ErrorMessage = ex.Message,
                            ErrorDetails = ex.StackTrace,
                            Timestamp = DateTime.UtcNow
                        });
                        Console.WriteLine(ex.Message);
                        await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignAsync", $"{page}:{pageSize},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));
                    }
                }
            }
        }

        public async Task ProcessErrorLogAsync(List<Object> errorLog, Campaign campaign)
        {
            if (errorLog.Any())
            {

                var campaignData = (await _genericRepository.GetByObjectAsync<Campaign>(
                    new() { { "CampaignId", campaign.CampaignId } }, "Campaigns"))
                    .FirstOrDefault();

                if (campaignData != null)
                {
                    var errorJson = new
                    {
                        LastUpdated = DateTime.UtcNow,
                        TotalErrors = errorLog.Count,
                        Errors = errorLog
                    };

                    campaignData.ErrorCause = JsonConvert.SerializeObject(errorJson);
                    await _genericRepository.UpdateRecordAsync<Campaign>(
                        "Campaigns",
                        new List<string> { "ErrorMessage" },
                        campaignData,
                        new() { { "CampaignId", campaign.CampaignId } });

                }
            }
        }
        public async Task ProcessCampaignBatchAsync(int pageSize, int page, Campaign campaign, List<string> audiences, bool isCompleted = false)
        {
            //Declare the variables
            var campaignTrackers = new List<CampaignTracker>();
            var conversations = new List<Conversations>();
            Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
            var campaignData = (await _genericRepository.GetByObjectAsync<Campaign>(new() { { "CampaignId", campaign.CampaignId } }, "Campaigns"))
                .FirstOrDefault();
            campaign = campaignData ?? campaign;

            if (isCompleted && campaignData != null)
            {
                campaignData.State = CampaignState.Completed;
            }
            List<string> waIds = new List<string>();
            List<Guid> sendContactListIds = new List<Guid>();
            var errorLog = new List<object>();
            try
            {
                (string mimeType, string mediaType) = string.IsNullOrEmpty(campaign.MediaUrl) ? ("text", string.Empty) : (await GetMediaTypeAsync(campaign.MediaUrl));

                await _logHistoryService.SaveInformationLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignBatchAsync", $"{page}:{pageSize},{JsonConvert.SerializeObject(audiences)}", campaign, "Campaign Information");
                var metaDetails = (await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object> { { "BusinessId", campaign.BusinessId.ToString() } }))?.FirstOrDefault();
                if (audiences?.Any() == true && metaDetails != null)
                {
                    var validGuids = audiences
                                     .Where(x => Guid.TryParse(x, out _))
                                     .Select(x => Guid.Parse(x))
                                     .ToList();
                    var contacts = await _inboxRepository.GetContactDetailsByIds(campaign.BusinessId, validGuids, CancellationToken.None);

                    //var contactNumbers = contacts.Select(x => $"{x.CountryCode.Trim('+')}{x.Contact}");
                    var contactNumbersDict = contacts.ToDictionary(dict => dict.ContactId, dict => $"{dict.CountryCode.Trim('+')}{dict.Contact}");
                    Template? template = null;
                    List<Button>? buttons = new List<Button>();
                    if (campaign.TemplateId != null && campaign.TemplateId != Guid.Empty)
                    {
                        template = (await _genericRepository.GetByObjectAsync<Template>(new Dictionary<string, object> { { "BusinessId", campaign.BusinessId }, { "TemplateId", campaign.TemplateId ?? Guid.Empty } }, "Templates"))
                            .FirstOrDefault();
                        buttons = await _genericRepository.GetByObjectAsync<Button>(new Dictionary<string, object> { { "TemplateId", campaign.TemplateId ?? Guid.Empty } }, "ButtonDetails");
                    }
                    foreach (var dict in contactNumbersDict)
                    {
                        var contact = contacts.FirstOrDefault(x => x.ContactId == dict.Key);
                        var campaignTracker = CreateCampaignTracker(campaign, contact.ContactId.ToString());

                        if (template != null)
                        {
                            string? bodyValues = contact != null ? MapContact(contact, campaign.BodyValues ?? string.Empty) : campaign.BodyValues;
                            string? headerValue = contact != null ? MapContact(contact, campaign.HeaderValue ?? string.Empty) : campaign.HeaderValue;
                            if (template.MediaType != MediaType.TEXT && template.MediaType != MediaType.NONE)
                            {
                                headerValue = template.MediaAwsUrl;
                            }

                            List<CarouselCardVariableDto> carouselVariableValues = JsonConvert.DeserializeObject<List<CarouselCardVariableDto>>(campaign.CarouselVariables ?? "[]") ?? new();

                            var conv = GetConversationFormateForTemplate(template, buttons, bodyValues?.Split(",").ToList(), headerValue, campaign.CampaignId, carouselVariableValues).Clone() as Conversations;
                            conv.To = dict.Value;

                            try
                            {
                                string existingBody = "";
                                var regex = StringHelper.GetVariableRegexs();
                                var body = StringHelper.FormateTemplateComponents(template.Body);
                                var bodyReplaced = StringHelper.ReplaceAndExtractVariables(body).UpdatedMessage;
                                if (template.Body != null && StringHelper.GetVariableRegexs().IsMatch(bodyReplaced))
                                {
                                    existingBody = template.Body;
                                }

                                template.Buttons = buttons;

                                var urlButtons = template.Buttons.Where(b => b.ButtonType == "URL").ToList();
                                var phoneNumberButtons = template.Buttons.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
                                var quickReplyButtons = template.Buttons.Where(b => b.ButtonType == "QUICK_REPLY").ToList();

                                if (template.MediaType == MediaType.CAROUSEL)
                                {

                                    List<CarouselCardsDto> jsonCardsConverted = JsonCardsConverter(template.CarouselCardsJson ?? string.Empty, carouselVariableValues);

                                    var carouselBody = "";
                                    foreach (var card in jsonCardsConverted)
                                    {
                                        var formateCardBody = StringHelper.FormateTemplateComponents(card.Body);
                                        var leadratCardVarBody = StringHelper.ReplaceAndExtractVariables(formateCardBody).UpdatedMessage;
                                        if (regex.IsMatch(leadratCardVarBody))
                                        {
                                            carouselBody = string.Join(",", carouselVariableValues.Select(card => string.Join(",", card.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}"))));
                                        }
                                    }

                                    var carouselCardsPayload = SendCarouselCardsPayload(jsonCardsConverted, template, carouselVariableValues);
                                    var jsonPayload = CreateTemplatePayloads.SendCarouselTemplatePayload(template.TemplateName, dict.Value, bodyValues?.Split(",").ToList() ?? new(), template.LanguageCode, carouselCardsPayload, carouselBody);

                                    JObject json = JObject.Parse(jsonPayload);
                                    var response = await _metaApiService.SendCarouselTemplateAsync(campaign.BusinessId, json, metaDetails.PhoneNumberID, metaDetails.Token);

                                    if (response.Success)
                                    {
                                        var waId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty;
                                        conv.WhatsAppMessageId = waId;
                                        conv.Status = ConvStatus.sent;
                                        waIds.Add(waId);
                                        campaignTracker.WhatsAppMessagesId = waId;
                                        campaignTrackers.Add(campaignTracker);
                                        sendContactListIds.Add(dict.Key);

                                    }
                                    else
                                    {
                                        conv.WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty; ;
                                        conv.Status = ConvStatus.failed;
                                        conv.ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error";
                                        conv.ErrorDetails = JsonConvert.SerializeObject(response.Result);
                                        waIds.Add(conv.WhatsAppMessageId);

                                        errorLog.Add(new
                                        {
                                            BatchNumber = page,
                                            BatchSize = pageSize,
                                            ErrorType = "BatchProcessingException",
                                            ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error",
                                            ErrorDetails = JsonConvert.SerializeObject(response.Result),
                                            Timestamp = DateTime.UtcNow
                                        });
                                    }
                                }
                                else
                                {
                                    var response = await _metaApiService.SendTemplateAsync(campaign.BusinessId.ToString(), template.LanguageCode.ToString(),
                                                     dict.Value, template.TemplateName, bodyValues?.Split(",").ToList(), template.MediaType,
                                                     headerValue, metaDetails.Token, metaDetails.PhoneNumberID, metaDetails.WhatsAppBusinessAccountID);


                                    if (response.IsSuccess)
                                    {
                                        var waId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty;
                                        conv.WhatsAppMessageId = waId;
                                        conv.Status = ConvStatus.sent;
                                        waIds.Add(waId);
                                        campaignTracker.WhatsAppMessagesId = waId;
                                        campaignTrackers.Add(campaignTracker);
                                        sendContactListIds.Add(dict.Key);

                                    }
                                    else
                                    {
                                        conv.WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty; ;
                                        conv.Status = ConvStatus.failed;
                                        conv.ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error";
                                        conv.ErrorDetails = JsonConvert.SerializeObject(response.Result);
                                        waIds.Add(conv.WhatsAppMessageId);

                                        errorLog.Add(new
                                        {
                                            BatchNumber = page,
                                            BatchSize = pageSize,
                                            ErrorType = "BatchProcessingException",
                                            ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error",
                                            ErrorDetails = JsonConvert.SerializeObject(response.Result),
                                            Timestamp = DateTime.UtcNow
                                        });
                                    }
                                }
                                conversations.Add(conv);
                            }
                            catch (Exception ex)
                            {
                                errorLog.Add(new
                                {
                                    BatchNumber = page,
                                    BatchSize = pageSize,
                                    ErrorType = "BatchProcessingException",
                                    ErrorMessage = ex.Message,
                                    ErrorDetails = ex.StackTrace,
                                    Timestamp = DateTime.UtcNow
                                });
                                Console.WriteLine(ex.Message);
                                await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignBatchAsync->1", $"{page}:{pageSize},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));
                            }
                        }
                        else
                        {
                            var response = await _metaApiService.SendTextWithMediaMessageAsync(campaign.BusinessId, dict.Value, StringHelper.FormateEscapeSequences(campaign.SendTextType ?? string.Empty), mediaType, campaign.MediaUrl, null, metaDetails.Token, metaDetails.PhoneNumberID, metaDetails.WhatsAppBusinessAccountID);
                            var caption = !string.IsNullOrEmpty(campaign.MediaUrl) ? campaign.SendTextType : string.Empty;
                            var text = !string.IsNullOrEmpty(campaign.MediaUrl) ? string.Empty : campaign.SendTextType;
                            var conv = GetTextMediaConversationFormate(text, campaign.BusinessId, dict.Value, mediaType, caption, mimeType, campaign.MediaUrl, string.Empty, string.Empty, campaign.CampaignId);
                            if (response.IsSuccess)
                            {
                                var result = response.Result.ToObject<WhatsAppResponse>();
                                conv.Status = ConvStatus.sent;
                                conv.WhatsAppMessageId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty;
                                waIds.Add(conv.WhatsAppMessageId);
                                campaignTracker.WhatsAppMessagesId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty; ;
                                campaignTrackers.Add(campaignTracker);
                                sendContactListIds.Add(dict.Key);

                            }
                            else
                            {
                                var result = response.Result.ToObject<WhatsAppErrorResponse>();
                                conv.Status = ConvStatus.failed;
                                conv.ErrorMessage = result?.Error?.Message ?? string.Empty;
                                conv.ErrorDetails = JsonConvert.SerializeObject(response.Result) ?? string.Empty;
                                waIds.Add(conv.WhatsAppMessageId);
                                errorLog.Add(new
                                {
                                    BatchNumber = page,
                                    BatchSize = pageSize,
                                    ErrorType = "BatchProcessingException",
                                    ErrorMessage = StringHelper.GetValueFromResponse(response.Result, "error.message") ?? "Unknown error",
                                    ErrorDetails = JsonConvert.SerializeObject(response.Result),
                                    Timestamp = DateTime.UtcNow
                                });
                            }
                            conversations.Add(conv);
                        }
                        if (conversations.Count >= 5)
                        {
                            await SaveConversationsAsync(conversations);
                            conversations.Clear();
                        }

                        if (campaignTrackers.Count >= 100)
                        {
                            await SaveCampaignTracker(campaignTrackers);
                            await UpdateLastMessageInContactsAsync(sendContactListIds);
                            campaignTrackers.Clear();
                            sendContactListIds.Clear();
                        }
                    }
                    if (conversations.Any())
                    {
                        await SaveConversationsAsync(conversations);
                    }
                    if (campaignTrackers.Any())
                    {
                        await SaveCampaignTracker(campaignTrackers);
                        await UpdateLastMessageInContactsAsync(sendContactListIds);
                    }
                }
                await _logHistoryService.SaveInformationLogHistoryAsyn("Campaign Updated WAMessageIds",
                            $"Campaign {campaign.CampaignId} updated successfully.", string.Join(",", waIds), "Update Success");
            }
            catch (Exception ex)
            {
                errorLog.Add(new
                {
                    BatchNumber = page,
                    BatchSize = pageSize,
                    ErrorType = "BatchProcessingException",
                    ErrorMessage = ex.Message,
                    ErrorDetails = ex.StackTrace,
                    Timestamp = DateTime.UtcNow
                });
                Console.WriteLine(ex.Message);
                await _logHistoryService.SaveErrorLogHistoryAsyn("CreateCampaignAsync/ProcessCampaignBatchAsync->2", $"{page}:{pageSize},{JsonConvert.SerializeObject(campaign)}", "Campaign Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));
            }
            finally
            {
                // Ensure campaign is updated even in case of failure
                if (campaignData != null)
                {
                    try
                    {
                        var columns = new List<string> { "State" };
                        var conditions = new Dictionary<string, object>
                        {
                            { "BusinessId", campaignData.BusinessId },
                            { "CampaignId", campaignData.CampaignId }
                        };

                        await _genericRepository.UpdateRecordAsync<Campaign>("Campaigns", columns, campaignData, conditions);

                        await _logHistoryService.SaveInformationLogHistoryAsyn("Campaign Updated",
                            $"Campaign {campaignData.CampaignId} updated successfully.", campaignData, "Update Success");
                    }
                    catch (Exception updateEx)
                    {
                        errorLog.Add(new
                        {
                            BatchNumber = page,
                            BatchSize = pageSize,
                            ErrorType = "BatchProcessingException",
                            ErrorMessage = updateEx.Message,
                            ErrorDetails = updateEx.StackTrace,
                            Timestamp = DateTime.UtcNow
                        });

                        Console.WriteLine(updateEx.Message);
                        await _logHistoryService.SaveErrorLogHistoryAsyn("Update Campaign Error",
                            $"Campaign {campaignData?.CampaignId} update failed.", "Campaign Error",
                            updateEx.Message, JsonConvert.SerializeObject(updateEx.StackTrace));
                    }
                }

            }
            await ProcessErrorLogAsync(errorLog, campaign);
        }
        #region Get conversation formate
        private Conversations GetConversationFormateForTemplate(Template templateDto, List<Button>? buttonDetailDto, List<string>? bodyValue, string? headerValue, Guid campaignId, List<CarouselCardVariableDto> carouselVariableValues)
        {
            var bodyMessage = StringHelper.ReplacePlaceholders(StringHelper.ReplaceAndExtractVariables(templateDto.Body).UpdatedMessage, bodyValue ?? new());

            var headerMessage = templateDto.MediaType == MediaType.TEXT
                ? StringHelper.ReplaceAndExtractVariables(templateDto.Header ?? string.Empty).UpdatedMessage.Replace("{{1}}", headerValue)
                : templateDto.Header;

            var phoneNumberButtons = buttonDetailDto?.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
            var urlButtons = buttonDetailDto?.Where(b => b.ButtonType == "URL").ToList();
            var quickReplyButtons = buttonDetailDto?.Where(b => b.ButtonType == "QUICK_REPLY").ToList();


            List<CarouselCardsDto> jsonCardsConverted = new List<CarouselCardsDto>();
            string conversationCarouselCards = "";
            if (templateDto.MediaType == MediaType.CAROUSEL)
            {
                jsonCardsConverted = JsonCardsConverter(templateDto.CarouselCardsJson ?? string.Empty, carouselVariableValues);
                conversationCarouselCards = UpdatedCarouselCardsJson(jsonCardsConverted, carouselVariableValues ?? new());
            }

            return new Conversations
            {
                Id = Guid.NewGuid(),
                From = templateDto.BusinessId,
                TemplateBody = bodyMessage,
                TemplateHeader = headerMessage,
                TemplateMediaUrl = templateDto.MediaAwsUrl,
                TemplateFooter = templateDto.Footer,
                TemplateMediaType = templateDto.MediaType,
                MessageType = MessageType.Campaigns,
                CreatedAt = DateTime.UtcNow,
                ReferenceId = campaignId.ToString(),
                CallButtonName = phoneNumberButtons?.ButtonName ?? "",
                PhoneNumber = phoneNumberButtons?.ButtonValue ?? "",
                UrlButtonNames = urlButtons?.Any() == true ? string.Join(",", urlButtons?.Select(m => m.ButtonName)) : string.Empty,
                RedirectUrls = urlButtons?.Any() == true ? string.Join(",", urlButtons?.Select(x => x.ButtonValue)) : string.Empty,
                QuickReplies = quickReplyButtons?.Any() == true ? string.Join(",", quickReplyButtons?.Select(m => m.ButtonValue)) : string.Empty,
                CarouselCards = conversationCarouselCards
            };
        }


        public string SendCarouselCardsPayload(List<CarouselCardsDto> carouselCards, Template template, List<CarouselCardVariableDto> variables)
        {
            var regex = StringHelper.GetVariableRegexs();
            if (carouselCards == null || !carouselCards.Any())
            {
                throw new Exception("Carousel cards not found!");
            }
            var mediaType = MediaExtentionsHelper.GetMediaTypeFromUrl(template.MediaAwsUrl ?? string.Empty);
            var cards = carouselCards.Select((card, index) =>
            {
                var cardVariables = variables.ElementAtOrDefault(index);
                var bodyParameters = !string.IsNullOrEmpty(card.Body) && cardVariables?.BodyCarouselVariableValues != null
                    ? cardVariables.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}").ToList()
                    : new List<string>();


                var formateBody = StringHelper.FormateTemplateComponents(card.Body);
                var leadratVarBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                var bodyPayload = regex.IsMatch(leadratVarBody) && bodyParameters.Any()
                    ? $@"{{ ""type"": ""body"",
                    ""parameters"": [{string.Join(",", bodyParameters)}]
                 }}"
                    : string.Empty;

                var headerPayload = !string.IsNullOrEmpty(template.MediaAwsUrl)
            ? $@"{{ ""type"": ""header"",
             ""parameters"": [{{
                                ""type"": ""{card.MediaUrlType?.ToString().ToLower()}"",
                                ""{card.MediaUrlType?.ToString().ToLower()}"": {{ ""link"": ""{card.HeaderMediaUrl}"" }}
                              }}]
           }}"
            : string.Empty;


                var buttonsPayload = SendCarouselButtonPayload(card, cardVariables ?? new());

                var cardComponents = new List<string> { headerPayload, bodyPayload, buttonsPayload }
                    .Where(component => !string.IsNullOrEmpty(component))
                    .ToList();

                return $@"{{ ""card_index"": ""{index}"", ""components"": [{string.Join(",", cardComponents)}] }}";
            }).ToList();

            return $@"{{ ""type"": ""carousel"", ""cards"": [{string.Join(",", cards)}] }}";
        }
        private static string SendCarouselButtonPayload(CarouselCardsDto card, CarouselCardVariableDto variables)
        {
            if (card.UrlButtonName?.Count > 0 && card.RedirectUrl?.Count == card.UrlButtonName.Count)
            {
                var buttonsArray = card.UrlButtonName.Select((name, index) => new JObject
                {
                   { "type", "button" },
                   { "sub_type", "url" },
                   { "index", index.ToString() },
                   { "parameters", new JArray  { new JObject
                    {
                        { "type", "text" },
                        { "text", variables?.RedirectUrlVariableValues != null && variables.RedirectUrlVariableValues.Length > index
                            ? variables.RedirectUrlVariableValues[index]
                            : name
                        }
                    }
                }
            }
            }).ToArray();

                return string.Join(",", buttonsArray.Select(x => x.ToString(Newtonsoft.Json.Formatting.None)));
            }
            return string.Empty;
        }
        public static string UpdatedCarouselCardsJson(List<CarouselCardsDto> jsonCardsConverted, List<CarouselCardVariableDto> carouselCardVariables)
        {
            try
            {
                var updatedCards = jsonCardsConverted.Select((card, index) =>
                {
                    if (index < carouselCardVariables.Count)
                    {
                        var carouselVariable = carouselCardVariables[index];

                        if (!string.IsNullOrEmpty(card.Body) && carouselVariable.BodyCarouselVariableValues != null)
                        {
                            var leadratVar = StringHelper.ReplaceAndExtractVariables(card.Body).UpdatedMessage;
                            card.Body = StringHelper.ReplacePlaceholders(leadratVar, carouselVariable.BodyCarouselVariableValues.ToList());

                        }

                        if (card.RedirectUrl != null && carouselVariable.RedirectUrlVariableValues != null)
                        {
                            for (int i = 0; i < card.RedirectUrl.Count(); i++)
                            {
                                if (i < carouselVariable.RedirectUrlVariableValues.Count())
                                {
                                    var leadratUrlVar = StringHelper.ReplaceAndExtractVariables(card.RedirectUrl[i]).UpdatedMessage;
                                    card.RedirectUrl[i] = StringHelper.ReplacePlaceholders(leadratUrlVar, new List<string> { carouselVariable.RedirectUrlVariableValues[i] });
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(carouselVariable.MediaUrl))
                        {
                            card.HeaderMediaUrl = carouselVariable.MediaUrl;
                        }
                    }
                    return card;
                }).ToList();

                var jsonArray = JArray.FromObject(updatedCards);
                return jsonArray.ToString();
            }
            catch (Exception ex)
            {
                return $"Error processing carouselCards: {ex.Message}";
            }
        }


        public static List<CarouselCardsDto> JsonCardsConverter(string carouselCardsJson, List<CarouselCardVariableDto>? carouselVariables)
        {
            if (string.IsNullOrEmpty(carouselCardsJson))
            {
                throw new ArgumentException("CarouselCardsJson cannot be null or empty.");
            }

            List<CarouselCardsDto> cards = new List<CarouselCardsDto>();
            try
            {
                var jsonArray = JsonConvert.DeserializeObject<List<string>>(carouselCardsJson);

                if (jsonArray != null)
                {
                    foreach (var jsonString in jsonArray)
                    {
                        var card = JsonConvert.DeserializeObject<CarouselCardsDto>(jsonString);
                        if (card != null)
                        {
                            cards.Add(card);
                        }
                    }
                }
                else
                {
                    cards = JsonConvert.DeserializeObject<List<CarouselCardsDto>>(carouselCardsJson) ?? new List<CarouselCardsDto>();
                }
            }
            catch (JsonSerializationException ex)
            {
                throw new ArgumentException("Error deserializing CarouselCardsJson. Ensure the JSON matches the expected format.", ex);
            }

            return cards;
        }


        public CampaignTracker CreateCampaignTracker(Campaign campaign, string contactId)
        {
            var campaignTracker = new CampaignTracker()
            {
                Id = Guid.NewGuid(),
                BusinessId = campaign.BusinessId,
                CampaignId = campaign.CampaignId,
                ContactId = contactId,
                UserId = campaign.UserId,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = campaign.UserId,
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = campaign.UserId,
            };
            return campaignTracker;
        }

        public Conversations GetTextMediaConversationFormate(string? textMessage, string from, string to, string? mediaType, string? caption, string? mediaMimeType, string? mediaUrl, string waMessageId, string? replyId, Guid campaignId)
        {
            Conversations conv = new Conversations
            {
                Id = Guid.NewGuid(),
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = waMessageId,
                TextMessage = textMessage ?? string.Empty,
                MediaCaption = caption ?? string.Empty,
                ReplyId = replyId,
                To = to.ToLowerInvariant().Replace("+", ""),
                From = from.ToString().ToLowerInvariant().Replace("+", ""),
                MediaUrl = mediaUrl,
                Status = EngagetoEntities.Enums.ConvStatus.sent,
                MediaMimeType = mediaMimeType,
                MessageType = MessageType.Campaigns,
                ReferenceId = campaignId.ToString()
            };
            return conv;
        }
        #endregion
        #region Save conversations
        private async Task SaveConversationsAsync(List<Conversations> conversations)
        {
            if (conversations == null || !conversations.Any())
                return;

            try
            {
                var columns = StringHelper.GetPropertyNames<Conversations>(isNotMappedAttributeColumn: false);
                bool isInserted = await _genericRepository.InsertRecordsAsync("Conversations", columns, conversations);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving conversations: {ex.Message}");
                return;
            }
        }

        private async Task UpdateLastMessageInContactsAsync(List<Guid> contactIds)
        {
            try
            {
                if (contactIds == null || !contactIds.Any())
                    return;

                const int batchSize = 2000;
                const int pauseDurationMs = 1000;

                var contactIdBatches = new List<List<Guid>>();
                for (int i = 0; i < contactIds.Count; i += batchSize)
                {
                    contactIdBatches.Add(contactIds.Skip(i).Take(batchSize).ToList());
                }

                var columnsToUpdate = new List<string> { "LastMessageAt" };
                var entity = new { LastMessageAt = DateTime.UtcNow };

                for (int batchIndex = 0; batchIndex < contactIdBatches.Count; batchIndex++)
                {
                    var currentBatch = contactIdBatches[batchIndex];
                    bool success = false;
                    int retryCount = 0;
                    const int maxRetries = 2;
                    TimeSpan retryDelay = TimeSpan.FromSeconds(5);

                    while (!success && retryCount < maxRetries)
                    {
                        try
                        {
                            await _genericRepository.UpdateRecordsByIdsAsync(
                                tableName: "Contacts",
                                columnsToUpdate: columnsToUpdate,
                                updateValues: entity,
                                idColumn: "ContactId",
                                ids: currentBatch
                            );

                            success = true;
                        }
                        catch (Exception ex)
                        {
                            retryCount++;
                            if (retryCount >= maxRetries)
                            {
                                await _logHistoryService.SaveErrorLogHistoryAsyn("UpdateLastMessageInContactsAsync", $"Batch {batchIndex + 1}:{currentBatch.Count}",
                                                                                  "LastMessage Update Error", ex.Message, JsonConvert.SerializeObject(ex.StackTrace));
                            }
                            else
                            {
                                await Task.Delay(retryDelay);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating LastMessageAt: {ex.Message}");
            }
        }

        #endregion

        private async Task SaveCampaignTracker(List<CampaignTracker> campaigntracker)
        {
            if (campaigntracker == null || !campaigntracker.Any())
                return;
            try
            {
                var columns = StringHelper.GetPropertyNames<CampaignTracker>(isNotMappedAttributeColumn: false);
                await _genericRepository.InsertRecordsAsync("CampaignTracker", columns, campaigntracker);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving conversations: {ex.Message}");
                return;
            }

        }

        #region Get Media type and mime type
        private async Task<(string MimeType, string MediaType)> GetMediaTypeAsync(string url)
        {
            try
            {
                var client = new HttpClient();
                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                    throw new InvalidOperationException("Failed to retrieve content from the provided URL.");

                var mimeType = response.Content.Headers.ContentType?.MediaType;
                if (string.IsNullOrEmpty(mimeType))
                    throw new InvalidOperationException("Content type not found.");

                var mediaType = GetMediaCategory(mimeType);
                return (mimeType, mediaType);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("The provided URL is not valid or accessible.", ex);
            }
        }
        private string GetMediaCategory(string mimeType)
        {
            if (mimeType.StartsWith("application/"))
            {
                var documentTypes = new HashSet<string>
                {
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "text/plain"
                };

                return documentTypes.Contains(mimeType) ? "document" : "Others";
            }

            var mediaType = mimeType.Split('/')[0]; // Example: "image/jpeg" -> "image"
            return mediaType switch
            {
                "image" => "image",
                "video" => "video",
                "audio" => "audio",
                _ => "Others"
            };
        }
        #endregion
        #region Map Contact details for body and header
        private string MapContact(ContactInfoDto contact, string variables)
        {
            if (contact == null || string.IsNullOrWhiteSpace(variables))
                return variables;

            List<string> values = new List<string>();
            var dictContact = ObjectHelper.ConvertToDictionary(contact);

            var result = variables
                .Split(',')
                .Select(x => dictContact.ContainsKey(x) ? !string.IsNullOrEmpty(dictContact[x]) ? dictContact[x] : x : x)
                .ToList();

            return string.Join(",", result);
        }

        #endregion
        public async Task<List<string>> GetContactIdsToExcel(int uploadFileId, Campaign campaign)
        {
            var uploadedFile = (await _genericRepository.GetByObjectAsync<UploadedFile>(
                new Dictionary<string, object> { { "Id", campaign.UploadedFileId ?? 0 } }
            ))?.FirstOrDefault();

            if (uploadedFile == null || string.IsNullOrEmpty(uploadedFile.FilePath))
            {
                Console.WriteLine("⚠️ Uploaded file not found or file path is empty.");
                return new List<string>(); ;
            }

            var excelProcessorService = new ExcelProcessorcs(ExcelMapping.ContactColumnMapping);
            var contactData = await excelProcessorService.ProcessExcel<Contacts>(uploadedFile.FilePath);

            if (contactData == null || !contactData.Any())
            {
                Console.WriteLine("⚠️ No valid contact data found in the file.");
                return new List<string>(); ;
            }

            // Clean and format contact data
            contactData.ForEach(x =>
            {
                if (string.IsNullOrEmpty(x.Name))
                    x.Name = x.Contact;
                if (string.IsNullOrEmpty(x.CountryCode))
                    x.CountryCode = "+91";
                else if (!x.CountryCode.StartsWith("+"))
                    x.CountryCode = $"+{x.CountryCode.TrimStart('+')}";
            });

            // Check existing contacts to avoid duplicates
            var contactNumbers = contactData.Select(x => x.Contact).ToList();
            var existingContacts = await _genericRepository.GetRecordByRequestFilter<Contacts>(
                new List<RequestFilterDto>
                {
                    new("Contact", contactNumbers, "in"),
                    new("IsActive", true, "="),
                    new("BusinessId", campaign.BusinessId, "=")
                },
                "Contacts", 0, 0, new List<string> { "ContactId", "Contact", "CountryCode", "Name" });

            var newContacts = contactData.Where(x => !existingContacts.Select(c => c.Contact).Contains(x.Contact)).ToList();

            if (!newContacts.Any())
            {
                Console.WriteLine("⚠️ No new contacts to insert.");
                return existingContacts.Select(x => x.ContactId.ToString()).ToList();
            }

            // Assign new properties to new contacts
            newContacts.ForEach(x =>
            {
                x.ContactId = Guid.NewGuid();
                x.BusinessId = Guid.Parse(campaign.BusinessId);
                x.ChatStatus = ChatStatus.New;
                x.IsOptIn = Is_OptIn.optin;
                x.CreatedDate = DateTime.UtcNow;
                x.Source = SourceType.Excel;
                x.IsActive = true;
            });

            // Batch insert with error handling
            int batchSize = 1000;
            int totalRecords = newContacts.Count;
            int processedRecords = 0;
            List<Contacts> failedRecords = new List<Contacts>();

            while (processedRecords < totalRecords)
            {
                var batch = newContacts.Skip(processedRecords).Take(batchSize).ToList();

                try
                {
                    var inserted = await _genericRepository.InsertRecordsAsync("Contacts",
                        StringHelper.GetPropertyNames<Contacts>(), batch);
                    if (!inserted)
                        throw new Exception("Batch insert failed.");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ Failed to insert batch {processedRecords}-{processedRecords + batchSize}: {ex.Message}");
                    failedRecords.AddRange(batch);
                }

                processedRecords += batchSize;
            }

            // Log failed records
            if (failedRecords.Any())
            {
                Console.WriteLine($"❌ Total Failed Records: {failedRecords.Count}");
                Console.WriteLine($"❌ Failed Records: {JsonConvert.SerializeObject(failedRecords.Select(x => new { x.Contact, x.Name, x.CountryCode }))}");
            }

            // Merge existing and newly inserted contact IDs
            var fullContactIds = existingContacts.Select(x => x.ContactId).ToList();
            fullContactIds.AddRange(newContacts.Select(x => x.ContactId));
            //var audience = string.Join(",", fullContactIds);


            // Update campaign audience
            var campaignDetail = (await _genericRepository.GetByObjectAsync<Campaign>(
                new() { { "CampaignId", campaign.CampaignId } }, "Campaigns"
            ))?.FirstOrDefault();

            if (campaignDetail != null)
            {
                await _genericRepository.UpdateRecordAsync("Campaigns",
                    new List<string> { "Audiance" },
                    campaign,
                    new() { { "CampaignId", campaign.CampaignId } });

                // Handle workflow automation if applicable
                if (!string.IsNullOrEmpty(campaignDetail.AutomationJson))
                {
                    var automation = JsonConvert.DeserializeObject<CampaignAutomationDto>(campaignDetail.AutomationJson);
                    if (automation?.WorkflowAutomation?.CustomerResponse == CustomerResponse.AnyCustomerResponse)
                    {
                        await _inboxRepository.UpdateContactForWorkflowAsync(
                            campaignDetail.BusinessId,
                            fullContactIds,
                            automation.WorkflowAutomation.WorkflowName ?? string.Empty,
                            0,
                            CancellationToken.None);
                    }
                }
            }

            return fullContactIds.Select(i => i.ToString()).ToList();
        }

        private async Task<Dictionary<string, Guid>> CreateAndGetTags(List<string> tags, string businessId, Guid userId)
        {
            Dictionary<string, Guid> tagDetails = new();
            var disctinictTags = tags.Select(x => x.ToLowerInvariant()).ToList().Distinct().ToList();
            var companyId = Guid.Parse(businessId);
            var existingTags = await _genericRepository.GetRecordByRequestFilter<Tags>(new List<RequestFilterDto>
            {
                new("Tag",disctinictTags,"in"),
                new("IsActive",true,"="),
                new("BusinessId",Guid.Parse(businessId),"=")
            }, "Tags");
            if (existingTags.Any())
            {
                tagDetails = existingTags
                    .GroupBy(tag => tag.Tag)
                    .Select(group => group.First())
                    .ToDictionary(dict => dict.Tag, dict => dict.Id);
            }
            var notExistingTags = disctinictTags.Except(existingTags.Select(x => x.Tag), StringComparer.OrdinalIgnoreCase);
            if (notExistingTags.Any())
            {
                var newTags = notExistingTags.Select(x => new Tags()
                {
                    Id = Guid.NewGuid(),
                    BusinessId = companyId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId.ToString(),
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = userId.ToString(),
                    UserId = userId,
                    Tag = x,
                    IsActive = true
                }).ToList();
                await _genericRepository.InsertRecordsAsync<Tags>("Tags", StringHelper.GetPropertyNames<Tags>(false), newTags);
                newTags.ForEach(tag => tagDetails.TryAdd(tag.Tag, tag.Id));
            }
            return tagDetails;
        }
    }

}
