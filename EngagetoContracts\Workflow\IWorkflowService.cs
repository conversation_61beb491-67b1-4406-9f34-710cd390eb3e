using EngagetoEntities.Dtos.WorkflowDtos;

namespace EngagetoContracts.Workflow
{
    public interface IWorkflowService
    {
        Task<ViewWorkFlowDto> CreateWorkflowAsync(CreateWorkflowDto workflow, Guid userId);
        Task<List<WorkflowResponseDto>> GetWorkflowsAsync(Guid companyId);
        Task<bool> UpdateWorkflowAsync(Guid workflowId, UpdateWorkflowDto workflow);
        Task<bool> DeleteWorkflowAsync(Guid workflowId, Guid companyId);
        Task<WorkflowDetailDto> GetWorkflowByIdAsync(Guid id);
        Task<AddKeyWordResponseDto> AddKeywordsToWorkflowAsync(AddKeywordDto request);
        Task<bool> DeleteKeywordsAsync(Guid workflowId, List<string> keywords, Guid userId);
        Task<List<string>> GetAllKeywordsAsync(Guid workflowId);
        Task<bool> ToggleWorkflowActiveStatusAsync(Guid workflowId);
        Task<List<string>> GetKeywordsByBusinessId(Guid businessId);
        Task<List<WorkflowNodeDto>> GetFlowStartNodesByBusinessIdAsync(Guid businessId);
    }
}