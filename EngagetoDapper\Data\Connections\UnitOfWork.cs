﻿using System.Data;


namespace EngagetoDapper.Data.Connections
{
    public sealed class UnitOfWork : IUnitOfWork
    {
        private readonly Guid _id;
        private readonly IDbConnection _dbConnection;
        private IDbTransaction _dbTransaction;

        public UnitOfWork(IDbConnection dbConnection)
        {
            _id = Guid.NewGuid();
            _dbConnection = dbConnection;
        }


        IDbConnection IUnitOfWork.Connection => _dbConnection;
        IDbTransaction IUnitOfWork.Transaction => _dbTransaction;
        Guid IUnitOfWork.Id => _id;


        public void Begin()
        {
            _dbTransaction = _dbConnection.BeginTransaction();
        }

        public void Commit()
        {
            _dbTransaction.Commit();
            Dispose();
        }

        public void Rollback()
        {
            _dbTransaction.Rollback();
            Dispose();
        }

        public void Dispose()
        {
            if (_dbTransaction != null)
                _dbTransaction.Dispose();

            _dbTransaction = null;
        }
    }
}
