﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerFastModeProjectMountDirectory>/home/<USER>/wwwroot</DockerFastModeProjectMountDirectory>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.DurableTask" Version="1.2.3" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="1.3.2" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.1" />
	  <PackageReference Include="Microsoft.Azure.WebJobs.Core" Version="3.0.41" />
	  <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.DurableTask" Version="3.0.4" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EngagetoBackGroundJobs\EngagetoBackGroundJobs.csproj" />
    <ProjectReference Include="..\EngagetoEntities\EngagetoEntities.csproj" />
    <ProjectReference Include="..\EngagetoRepository\EngagetoRepository.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
</Project>