﻿
using Engageto.Dapper.Data.Dapper.Services.CommonServices;
using EngagetoDapper.Data.Dapper.Repositories.GenericRepositories;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dapper.Services;
using EngagetoDapper.Data.Dapper.Services.EmailServices;
using EngagetoDapper.Data.Dapper.Services.FilterServices;
using EngagetoDapper.Data.Dapper.Services.LogHistoryServices;
using EngagetoDapper.Data.Dapper.Services.UserServices;
using EngagetoDapper.Data.HttpService;
using EngagetoDapper.Data.Interfaces.AnalyticsInterfaces;
using EngagetoDapper.Data.Interfaces.CommonInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.IEmailInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using Microsoft.Extensions.DependencyInjection;


namespace EngagetoDapper.InfraStructure
{
    public static class CommonStartup
    {
        public static IServiceCollection AddCommonStartup(this IServiceCollection services)
        {
            return services.AddScoped<IGenericRepository, GenericRepostry>()
                .AddScoped<ICommonService, CommonService>()
                .AddScoped<IEmailService, EmailService>()
                .AddScoped<IApiKeyService, ApiKeyService>()
                .AddScoped<IWebhookService, WebhookService>()
                .AddScoped<IHttpService, HttpService>()
                .AddScoped<IResourcePermissionService, ResourcePermissionService>()
                .AddScoped<IPlanService, PlanService>()
                .AddScoped<IInboxRepository, InboxRepository>()
                .AddScoped<IInboxService, InboxService>()
                .AddScoped<IAnalyticsService, AnalyticsService>()
                .AddScoped<ILogHistoryService, LogHistoryService>()
                .AddScoped<IFilterService, FilterService>();
        }
    }
}
