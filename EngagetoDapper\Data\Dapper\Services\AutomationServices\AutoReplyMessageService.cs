﻿using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace EngagetoDapper.Data.Dapper.Services.AutomationServices
{
    public class AutoReplyMessageService : IAutoReplyMessageService
    {
        private readonly IDapperConnectionFactory _connectionFactory;
        private readonly IAutoReplyAutomationRepostry _autoReplyAutomationRepostry;
        private readonly IAutoReplyMessageRepository _autoReplyCustomMessageRepository;
        private readonly IGenericRepository _genericRepository;
        private readonly ApplicationDbContext _automationDbContext;
        private readonly IInboxRepository _inboxRepository;
        public AutoReplyMessageService(IDapperConnectionFactory connectionFactory,
            IAutoReplyAutomationRepostry automationRepostry,
            IAutoReplyMessageRepository autoMessageRepository,
            IGenericRepository genericRepository,
            ApplicationDbContext automationDbContext,
            IInboxRepository inboxRepository
            )
        {
            _connectionFactory = connectionFactory;
            _autoReplyAutomationRepostry = automationRepostry;
            _autoReplyCustomMessageRepository = autoMessageRepository;
            _genericRepository = genericRepository;
            _automationDbContext = automationDbContext;
            _inboxRepository = inboxRepository;
        }

        public async Task<bool> SaveAutoReplyMessageAsync(AutoReplyMessageDto customMessageDto, Guid userId, bool isAutomation = false)
        {
            try
            {
                _connectionFactory.UnitOfWork.Begin();
                var autoReplyAutomation = customMessageDto.Adapt<AutoReplyAutomationEntity>();
                if (customMessageDto.AutoReplyAutomation.AutomationResponseType == ResponseType.Workflow)
                {
                    if (customMessageDto.AutoReplyAutomation.WorkflowName == null)
                    {
                        throw new Exception("Please choose a valid Workflow.");
                    }
                }
                var existingAutoReplyAutomations = await _autoReplyAutomationRepostry
                    .GetAutoReplyMessageByInputAsync<AutoReplyAutomationEntity>(autoReplyAutomation.CompanyId, autoReplyAutomation.Input, autoReplyAutomation.InputVariation);

                if (existingAutoReplyAutomations.Any(x => x.Id != autoReplyAutomation.Id))
                {
                    if (isAutomation)
                    {
                        autoReplyAutomation = existingAutoReplyAutomations.FirstOrDefault();
                        autoReplyAutomation.WorkflowName = customMessageDto.AutoReplyAutomation.WorkflowName;
                    }
                    else
                    {
                        throw new Exception("Please choose another input since this one already exists with Input or Input Variations.");
                    }
                }
                var autoReplyId = await _autoReplyAutomationRepostry.SaveAutoReplyAutomationAsync<Guid>(autoReplyAutomation);

                if (autoReplyId == Guid.Empty)
                {
                    throw new Exception("Failed to save AutoReplyAutomation.");
                }
                if (customMessageDto.AutoReplyAutomation.AutomationResponseType == ResponseType.CustomMessage)
                {
                    var autoCustomMessage = customMessageDto.Adapt<AutoReplyCustomMessageEntity>();
                    autoCustomMessage.AutoReplyId = autoReplyId;
                    var autoReplyCustomMessageId = await _autoReplyCustomMessageRepository.SaveAutoReplyMessageAsync<Guid>(autoCustomMessage, autoReplyAutomation.UserId);

                    if (autoReplyCustomMessageId == Guid.Empty)
                    {
                        throw new Exception("Failed to save AutoReplyCustomMessage.");
                    }

                    //delete existing Veriable 
                    var veriableTableName = StringHelper.GetTableName<VeriableEntity>();
                    await _genericRepository.DeleteRecordAsync<bool>(veriableTableName, new List<Dtos.RequestFilterDto>()
                    {
                        new Dtos.RequestFilterDto("ReferenceId",autoReplyCustomMessageId,"="),
                        new Dtos.RequestFilterDto("ReferenceTableType", ReferenceTableType.AutoReplyCustomMessage,"=")
                    });

                    if (customMessageDto.Veriables != null && customMessageDto.Veriables.Any())
                    {
                        var veriables = customMessageDto.Veriables.Adapt<List<VeriableEntity>>();
                        foreach (var veriable in veriables)
                        {
                            veriable.Id = Guid.NewGuid();
                            veriable.CompanyId = autoReplyAutomation.CompanyId;
                            veriable.UserId = autoReplyAutomation.UserId;
                            veriable.ReferenceId = autoReplyCustomMessageId;
                            veriable.ReferenceTableType = ReferenceTableType.AutoReplyCustomMessage;
                            veriable.CreatedAt = DateTime.Now;
                            veriable.CreatedBy = autoReplyAutomation.UserId;
                            veriable.UpdatedBy = autoReplyAutomation.UserId;
                        }

                        //var veriablesXml = StringHelpers.ConvertListToXml(veriables);
                        await _autoReplyAutomationRepostry.SaveVeriablesAsync(veriables);
                    }
                }

                _connectionFactory.UnitOfWork.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _connectionFactory.UnitOfWork.Rollback();
                throw;
            }
        }

        public async Task<bool> DeleteAutoReplyMessageAsync(Guid companyId, Guid userId,
            Guid autoReplyAutomationId)

        {
            try
            {
                var results = await _autoReplyCustomMessageRepository
                    .GetAutoReplyMessageAsync<AutoCustomMessageResultDto>(companyId, autoReplyAutomationId);
                if (!results.Any())
                    throw new Exception("Not found Auto reply custom message");
                var isDelete = await _autoReplyAutomationRepostry
                    .SoftDeleteAutoReplyMessageAsync(companyId, userId, autoReplyAutomationId);
                return isDelete > 0;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<IEnumerable<AutoReplyMessageDto>> GetAutoReplyMessageAsync(Guid companyId, Guid? autoReplyAutomationId)
        {
            try
            {
                // Initialize the result DTO list
                List<AutoReplyMessageDto> resultDtos = new List<AutoReplyMessageDto>();

                // Get the AutoReplyAutomationEntities based on companyId and optional autoReplyAutomationId
                var results = await _automationDbContext.AutoReplyAutomationEntities
                    .Where(m => m.CompanyId == companyId && !m.IsDeleted &&
                                (autoReplyAutomationId == null || m.Id == autoReplyAutomationId)).OrderByDescending(m => m.UpdatedAt)
                    .ToListAsync();

                if (results.Any())
                {
                    foreach (var result in results)
                    {
                        if (result.AutoReplyType == ResponseType.CustomMessage)
                        {

                            // Fetch all custom messages related to the automation results
                            var customMessages = await _automationDbContext.AutoReplyCustomMessageEntities
                                .Where(m => results.Select(r => r.Id).Contains(m.AutoReplyId) && !m.IsDeleted)
                                .ToListAsync();

                            // Fetch all variables related to the custom messages
                            var veriableEntities = await _automationDbContext.VeriableEntities
                                .Where(m => customMessages.Select(cm => cm.Id).Contains(m.ReferenceId) && !m.IsDeleted && m.ReferenceTableType == ReferenceTableType.AutoReplyCustomMessage)
                                .ToListAsync();

                            // Group variables by the custom message Id (ReferenceId)
                            var veriableGroup = veriableEntities.GroupBy(v => v.ReferenceId)
                                                                .ToDictionary(g => g.Key, g => g.Select(v => v.Adapt<VeriableDto>()).ToList());

                            // Map each AutoReplyAutomationEntity to AutoReplyMessageDto

                            var customMessage = customMessages.FirstOrDefault(m => m.AutoReplyId == result.Id);

                            // Use mapping for the DTO
                            var dto = result.Adapt<AutoReplyMessageDto>();

                            dto.Veriables = veriableGroup.ContainsKey(customMessage.Id) ? veriableGroup[customMessage.Id] : new List<VeriableDto>();


                            resultDtos.Add(dto);

                        }

                        else if (result.AutoReplyType == ResponseType.Workflow)
                        {
                            var dto = result.Adapt<AutoReplyMessageDto>();
                            resultDtos.Add(dto);
                        }

                    }
                }
                return resultDtos;
            }
            catch (Exception ex)
            {
                // Properly handle the exception
                throw new Exception("Error fetching auto-reply messages", ex);
            }
        }

        public async Task<bool> SetAutoReplyAutomationCampaignAsync(string companyId, Guid userId, CampaignAutomationDto campaignAutomationDto)
        {
            try
            {
                if (!Guid.TryParse(companyId, out var businessId))
                    throw new ArgumentException("Invalid companyId.");

                if (campaignAutomationDto.WorkflowAutomation != null && campaignAutomationDto.WorkflowAutomation.CustomerResponse == CustomerResponse.AnyCustomerResponse)
                {
                    await _inboxRepository.UpdateContactForWorkflowAsync(
                        companyId,
                        campaignAutomationDto.WorkflowAutomation.ContactIds,
                        campaignAutomationDto.WorkflowAutomation.WorkflowName ?? string.Empty,
                        0,
                        CancellationToken.None);
                }
                if (campaignAutomationDto.WorkflowAutomation != null || campaignAutomationDto.AutoCustomAutomation != null)
                {
                    await HandleOnButtonClickOrTypedReplyAsync(businessId, userId, campaignAutomationDto);
                }
                // Update campaign in the database
                var updateResult = await _genericRepository.FindAndUpdateAsync(
                    "Campaigns",
                    new() { { "AutomationJson", JsonConvert.SerializeObject(campaignAutomationDto) } },
                    new() { { "CampaignTitle", campaignAutomationDto.Name }, { "BusinessId", companyId } });

                return updateResult;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private async Task HandleOnButtonClickOrTypedReplyAsync(Guid businessId, Guid userId, CampaignAutomationDto campaignAutomationDto)
        {
            if (campaignAutomationDto.WorkflowAutomation != null && campaignAutomationDto.WorkflowAutomation.CustomerResponse != CustomerResponse.AnyCustomerResponse)
            {
                var existingAutoReplyAutomations = await _autoReplyAutomationRepostry
                    .GetAutoReplyMessageByInputAsync<AutoReplyAutomationEntity>(businessId, campaignAutomationDto.WorkflowAutomation.Input);
                await HandleWorkflowAutomationAsync(businessId, userId, campaignAutomationDto, existingAutoReplyAutomations);
            }
            if (campaignAutomationDto.AutoCustomAutomation != null)
            {
                var existingAutoReplyAutomations = await _autoReplyAutomationRepostry
                    .GetAutoReplyMessageByInputAsync<AutoReplyAutomationEntity>(businessId, campaignAutomationDto.AutoCustomAutomation.Input);
                if (!existingAutoReplyAutomations.Any())
                {
                    await CreateCustomAutomationAsync(businessId, userId, campaignAutomationDto);
                }
                else
                {
                    await _autoReplyAutomationRepostry.SoftDeleteAutoReplyMessageAsync(businessId, userId, existingAutoReplyAutomations.FirstOrDefault()?.Id ?? Guid.Empty);
                    await CreateCustomAutomationAsync(businessId, userId, campaignAutomationDto);
                }
            }
        }

        private async Task HandleWorkflowAutomationAsync(Guid businessId, Guid userId, CampaignAutomationDto campaignAutomationDto, IEnumerable<AutoReplyAutomationEntity> existingAutoReplyAutomations)
        {
            if (!existingAutoReplyAutomations.Any())
            {
                var entity = new AutoReplyAutomationEntity(
                    businessId,
                    userId,
                    campaignAutomationDto.WorkflowAutomation?.Input ?? string.Empty,
                    null,
                    ResponseType.Workflow,
                    campaignAutomationDto.WorkflowAutomation?.WorkflowName ?? string.Empty);
                entity.Id = Guid.Empty;
                await _autoReplyAutomationRepostry.SaveAutoReplyAutomationAsync<Guid>(entity);
            }
            else
            {
                var workflowExisting = existingAutoReplyAutomations
                    .FirstOrDefault(x => x.WorkflowName == campaignAutomationDto.WorkflowAutomation?.WorkflowName);

                if (workflowExisting != null)
                {
                    var inputWorkflowExisting = existingAutoReplyAutomations
                        .FirstOrDefault(x => x.Input == campaignAutomationDto.WorkflowAutomation?.Input);

                    if (inputWorkflowExisting != null)
                    {
                        await UpdateWorkflowAutomationAsync(inputWorkflowExisting, campaignAutomationDto);
                    }
                }
            }
        }

        private async Task UpdateWorkflowAutomationAsync(AutoReplyAutomationEntity inputWorkflowExisting, CampaignAutomationDto campaignAutomationDto)
        {
            if (inputWorkflowExisting.AutoReplyType == ResponseType.Workflow)
            {
                inputWorkflowExisting.WorkflowName = campaignAutomationDto.WorkflowAutomation?.WorkflowName;
            }
            else
            {
                await _genericRepository.DeleteRecordAsync<bool>(
                    "AutoReplyCustomMessageEntities",
                    new() { new RequestFilterDto("AutoReplyId", inputWorkflowExisting.Id, "=") });

                inputWorkflowExisting.AutoReplyType = ResponseType.Workflow;
                inputWorkflowExisting.WorkflowName = campaignAutomationDto.WorkflowAutomation?.WorkflowName;
            }

            await _autoReplyAutomationRepostry.SaveAutoReplyAutomationAsync<Guid>(inputWorkflowExisting);
        }

        private async Task CreateCustomAutomationAsync(Guid businessId, Guid userId, CampaignAutomationDto campaignAutomationDto)
        {
            var entity = new AutoReplyAutomationEntity(
                businessId,
                userId,
                campaignAutomationDto.AutoCustomAutomation?.Input ?? string.Empty,
                null,
                ResponseType.CustomMessage,
                null);
            entity.Id = Guid.Empty;
            var id = await _autoReplyAutomationRepostry.SaveAutoReplyAutomationAsync<Guid>(entity);

            var customMessageEntity = new AutoReplyCustomMessageEntity(
                id,
                userId,
                campaignAutomationDto.AutoCustomAutomation?.BodyMessage ?? string.Empty,
                null);
            customMessageEntity.Id = Guid.Empty;
            await _autoReplyCustomMessageRepository.SaveAutoReplyMessageAsync<Guid>(customMessageEntity, userId);
        }
    }
}
