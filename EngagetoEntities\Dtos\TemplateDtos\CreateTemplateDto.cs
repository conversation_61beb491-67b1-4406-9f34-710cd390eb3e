﻿using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class CreateTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid? TemplateId { get; set; } // this is for recreating template from draft
        public string TemplateName { get; set; }

        [Required(ErrorMessage = "Language can't be empty")]
        public Language Language { get; set; }

        [Required(ErrorMessage = "Category can't be empty")]
        public WATemplateCategory Category { get; set; }

        public string? SubCategory { get; set; }
        public MediaType MediaType { get; set; }
        public string? Header { get; set; }
        public string Body { get; set; }
        public string? Footer { get; set; }
        public string? CallButtonName { get; set; }
        public string? CountryCode { get; set; }
        public string? PhoneNumber { get; set; }
        public List<string>? UrlButtonName { get; set; }
        public List<string>? RedirectUrl { get; set; }
        public List<string>? QuickReply { get; set; }
        public string? MediaFile { get; set; }

        public void ValidateTemplateName(ref List<string> errors)
        {
            if (string.IsNullOrEmpty(TemplateName))
            {
                errors.Add("Template name can't be empty.");
            }
            else
            {
                if (!StringHelper.IsValidTemplateName(TemplateName))
                    errors.Add("Template should be lowercase letters, integers, and underscores.");
                if (TemplateName.Length > 512)
                    errors.Add("Maximum 512 characters allowed in TemplateName.");
            }
        }

        public void ValidateHeader(ref List<string> errors)
        {
            if (!string.IsNullOrEmpty(Header))
            {
                if (Header.Length > 60)
                    errors.Add("Header length must be between 0 and 60 characters.");
                if (StringHelper.IsValidVariable(Header))
                {
                    if (MediaType == MediaType.TEXT || MediaType == MediaType.NONE)
                    {
                        if (StringHelper.GetVariableCount(Header) > 1)
                            errors.Add("Only one variable is allowed in a header value.");
                    }
                }
            }
        }

        public void ValidateBody(ref List<string> errors)
        {
            if (string.IsNullOrEmpty(Body))
            {
                errors.Add("Body can't be empty.");
            }
            else
            {
                if (Body.Length > 1024)
                    errors.Add("Body length must be between 1 and 1024 characters.");

                Regex regexPlaceholder = StringHelper.GetVariableRegex();
                Regex regexWordsBetweenPlaceholders = StringHelper.GetPlaceholderWithWordsBetweenRegex();

                bool containsPlaceholders = regexPlaceholder.IsMatch(Body);
                bool containsValidPlaceholderStructure = regexWordsBetweenPlaceholders.IsMatch(Body);
                int placeholderCount = regexPlaceholder.Matches(Body).Count;

                if (containsPlaceholders)
                {
                    var segments = regexPlaceholder.Split(Body);
                    int wordsBetweenVariablesCount = segments.Sum(segment => segment.Trim().Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length);

                    if (wordsBetweenVariablesCount <= 2 * placeholderCount)
                        errors.Add("This template contains too many variable parameters relative to the message length. You need to decrease the number of variable parameters or increase the message length.");
                }
            }
        }

        public void ValidateFooter(ref List<string> errors)
        {
            if (!string.IsNullOrEmpty(Footer) && Footer.Length > 60)
                errors.Add("Footer cannot exceed 60 characters.");
        }

        public void ValidateCountryCode(ref List<string> errors)
        {
            if (string.IsNullOrEmpty(CountryCode))
                errors.Add("CountryCode can't be empty.");
            else if (CountryCode.Length < 1 || CountryCode.Length > 4)
                errors.Add("CountryCode length must be between 1 to 4 characters, including + (e.g., +91).");
        }

        public void ValidatePhoneNumber(ref List<string> errors)
        {
            if (string.IsNullOrEmpty(PhoneNumber))
                errors.Add("Phone number can't be empty.");
            else if (PhoneNumber.Length > 20)
                errors.Add("Phone number length must be between 0 and 20 characters.");
        }

        public void ValidateButtons(ref List<string> errors)
        {
            if (!string.IsNullOrEmpty(PhoneNumber))
            {
                ValidateCountryCode(ref errors);
                ValidatePhoneNumber(ref errors);
                if (string.IsNullOrEmpty(CallButtonName))
                    errors.Add("Call button name can't be empty.");
            }

            if (RedirectUrl != null && RedirectUrl.Count > 0)
            {
                if (RedirectUrl.Count > 2)
                    errors.Add("Maximum 2 UrlButtonName and RedirectUrl allowed.");
                if (RedirectUrl.Count != UrlButtonName?.Count)
                    errors.Add("The count of redirect URLs and URL button names is mismatched.");

                for (int i = 0; i < UrlButtonName?.Count; i++)
                {
                    if (string.IsNullOrEmpty(RedirectUrl[i]))
                        errors.Add($"URL is required for button '{UrlButtonName[i]}'.");
                    if (UrlButtonName[i].Length > 25)
                        errors.Add("UrlButton name text can't have more than 25 characters.");
                    if (RedirectUrl[i].Length > 2000)
                        errors.Add($"URL for button '{UrlButtonName[i]}' can't have more than 2000 characters.");
                    if (!StringHelper.IsValidUrl(RedirectUrl[i]))
                        errors.Add($"Invalid URL format: {RedirectUrl[i]}.");
                }
            }

            if (QuickReply != null && QuickReply.Count > 0)
            {
                if (QuickReply.Count > 10)
                    errors.Add("Maximum 10 QuickReply allowed.");

                foreach (var reply in QuickReply)
                {
                    if (reply.Length > 25)
                        errors.Add("QuickReply button text can't have more than 25 characters.");
                }
            }
        }

        public void ValidateTemplate(ref List<string> errors)
        {
            ValidateTemplateName(ref errors);
            ValidateHeader(ref errors);
            ValidateBody(ref errors);
            ValidateFooter(ref errors);
            ValidateButtons(ref errors);
        }
        public async Task<List<string>> ValidateMedia()
        {
            List<string> errors = new List<string>();

            if (MediaType != MediaType.TEXT && MediaType != MediaType.NONE)
            {
                if (string.IsNullOrEmpty(MediaFile))
                {
                    errors.Add("Media file can't be empty.");
                    return errors;
                }
                using var client = new HttpClient();
                try
                {
                    var res = await client.GetAsync(MediaFile);
                    if (!res.IsSuccessStatusCode)
                    {
                        errors.Add("Failed to fetch media file from the provided URL.");
                        return errors;
                    }
                    var contentType = res.Content.Headers.ContentType?.MediaType;
                    var fileSize = res.Content.Headers.ContentLength ?? 0;
                    bool isValidFile = false;

                    if (fileSize > 0)
                    {
                        if ((contentType == "image/jpeg" || contentType == "image/png") && fileSize <= 5242880) // 5MB for image
                            isValidFile = true;

                        else if ((contentType.StartsWith("text/") || contentType == "application/pdf" || contentType.StartsWith("application/vnd")) && fileSize <= 104857600) // 100MB for document
                            isValidFile = true;

                        else if (contentType.StartsWith("video/"))
                        {
                            var supportedVideoTypes = new[] { "video/mp4", "video/3gp", "video/3gpp" };
                            var maxVideoFileSize = 16 * 1024 * 1024; // 16MB

                            if (supportedVideoTypes.Contains(contentType) && fileSize <= maxVideoFileSize)
                                isValidFile = true;
                            else
                                errors.Add("Unsupported video file type or file size exceeded. WhatsApp supports only MP4 and 3GP formats up to 16MB with H.264 video codec and AAC audio codec.");
                        }
                        else
                            errors.Add("Unsupported file type.");
                    }
                    else
                        errors.Add("File size is zero.");

                    if (!isValidFile)
                        errors.Add("Invalid media file.");
                }
                catch (HttpRequestException ex)
                {
                    errors.Add("Error fetching media file: " + ex.Message);
                }
            }
            return errors;
        }
    }
}



