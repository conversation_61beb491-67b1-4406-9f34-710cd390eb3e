﻿using Engageto.Hubs;
using EngagetoBackGroundJobs.Implementation;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Text;

namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    public class WAAutoReplyMessageProcess : IWAAutoReplyMessageProcess
    {
        private readonly IGenericRepository _genericRepository;
        private readonly ApplicationDbContext _applicationDbContext;
        private readonly IAutomationWorkflowService _automationWorkflowService;
        private readonly IAutoReplyAutomationService _autoReplyAutomationService;
        private readonly IAutoReplyMessageService _autoReplyCustomMessageService;
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IMetaApiService _metaApiService;
        private readonly LeadProcessingService _leadProcessingService;
        private IHubContext<MessageHub, IMessageHubClient> _messageHub;
        public WAAutoReplyMessageProcess(IGenericRepository genericRepository,
                ApplicationDbContext applicationDbContext,
                IAutomationWorkflowService automationWorkflowService,
                IAutoReplyAutomationService autoReplyAutomationService,
                IAutoReplyMessageService autoReplyCustomMessageService,
                IWhatsAppBusinessClient whatsAppBusinessClient,
                ILogHistoryService logHistoryService,
                IMetaApiService metaApiService,
                LeadProcessingService leadProcessingService,
                IHubContext<MessageHub, IMessageHubClient> messageHub)
        {
            _genericRepository = genericRepository;
            _applicationDbContext = applicationDbContext;
            _automationWorkflowService = automationWorkflowService;
            _autoReplyAutomationService = autoReplyAutomationService;
            _autoReplyCustomMessageService = autoReplyCustomMessageService;
            _whatsAppBusinessClient = whatsAppBusinessClient;
            _logHistoryService = logHistoryService;
            _metaApiService = metaApiService;
            _leadProcessingService = leadProcessingService;
            _messageHub = messageHub;
        }

        public async Task<bool> SendAutoReplyMessageAsync(Conversations conversation, string companyId)
        {
            try
            {
                _ = Guid.TryParse(companyId, out Guid businessId);

                bool status = false;
                var contactNumber = conversation.From.Replace("+", "");
                var contactDetails = await _applicationDbContext.Contacts.FirstOrDefaultAsync(contact => string.Concat(contact.CountryCode, contact.Contact).Replace("+", "") == contactNumber 
                    && contact.BusinessId.ToString().ToLower() == companyId.ToLower());

                if (contactDetails != null && !string.IsNullOrEmpty(contactDetails?.WorkflowName) && contactDetails?.WorkflowStep != null)
                {
                    string workflowName = contactDetails?.WorkflowName ?? string.Empty;
                    int workflowStep = contactDetails?.WorkflowStep ?? -1;
                    if (workflowStep == 0 || workflowStep == 1)
                    {
                        contactDetails.WorkflowStartId = GetWorkflowStartedId();
                    }
                    var workflowData = await _automationWorkflowService.GetWorkflowAsync(businessId, null, null, workflowName);
                    if (workflowData?.Count > 0)
                    {
                        var nextStep = workflowData?[$"{workflowName}"]?.FirstOrDefault(m => m.Step == (workflowStep + 1));
                        var previousStep = workflowData?[$"{workflowName}"]?.FirstOrDefault(m => m.Step == (workflowStep));
                        if (string.IsNullOrEmpty(previousStep?.WebhookTriggerUrl) && workflowStep > 0)
                        {
                            WorkflowResponseHistoryEntity responseHistory = new WorkflowResponseHistoryEntity
                            {
                                Id = Guid.NewGuid(),
                                WorkflowName = previousStep?.WorkflowName ?? "",
                                Step = previousStep?.Step,
                                CreatedBy = contactDetails?.UserId,
                                CreatedAt = DateTime.UtcNow,
                                WorkflowId = previousStep.Id,
                                ContactId = contactDetails?.ContactId,
                                CompanyId = contactDetails.BusinessId,
                                UpdatedBy = contactDetails?.UserId,
                                WhatsappMessageId = conversation.WhatsAppMessageId,
                                Response = conversation.TextMessage ?? conversation.MediaCaption,
                                WorkflowStartId = contactDetails?.WorkflowStartId
                            };
                            await _genericRepository.SaveAsync(responseHistory);
                        }
                        if (nextStep != null)
                        {
                            var response = await WorkflowStepAsync(nextStep, contactDetails, workflowName, conversation);
                            status = response;
                        }
                        else
                        {
                            contactDetails.WorkflowName = null;
                            contactDetails.WorkflowStep = null;
                            contactDetails.WorkflowStartId = null;
                            _applicationDbContext.Update(contactDetails);
                            await _applicationDbContext.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        contactDetails.WorkflowName = null;
                        contactDetails.WorkflowStep = null;
                        contactDetails.WorkflowStartId = null;
                        _applicationDbContext.Update(contactDetails);
                        await _applicationDbContext.SaveChangesAsync();
                    }
                }
                if (contactDetails?.WorkflowStep == null && !string.IsNullOrEmpty(conversation?.TextMessage))
                {
                    var autoReplyAutomation = await _autoReplyAutomationService
                        .GetAutoReplyAutomationByInput(businessId, conversation.TextMessage ?? string.Empty);

                    if (autoReplyAutomation is null)
                        return false;

                    switch (autoReplyAutomation.AutoReplyType)
                    {
                        case ResponseType.CustomMessage:
                            var customMessages = (await _autoReplyCustomMessageService
                                .GetAutoReplyMessageAsync(businessId, autoReplyAutomation.Id))?.FirstOrDefault();

                            if (customMessages is not null)
                            {

                                string sendTextMessage = ReplacePlaceholders(customMessages.BodyMessage,
                                    (GetContactValues(contactDetails, customMessages.Veriables)));
                                var response = await SendAutoReplyMessageToContactAsync
                                    (conversation, sendTextMessage, customMessages.ButtonValue, businessId);
                                return response;
                            }
                            break;

                        case ResponseType.Workflow:
                            // Implement workflow handling if needed
                            var data = await _automationWorkflowService.GetWorkflowAsync(businessId, null, null, autoReplyAutomation.WorkflowName);

                            if (data != null)
                            {
                                var values = data[$"{autoReplyAutomation.WorkflowName}"]?.OrderBy(x => x.Step);
                                if (values?.Count() > 0)
                                {
                                    var response = await WorkflowStepAsync(values.First(), contactDetails, autoReplyAutomation?.WorkflowName, conversation);
                                    return response;
                                }
                            }
                            break;
                    }
                }
                return status;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("Error:SendAutoReplyMessageAsync", JsonConvert.SerializeObject(conversation), "", ex.Message, ex.StackTrace);
                return false;
            }
        }

        #region Helper method
        private long GetWorkflowStartedId()
        {
            DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            long unixTimeMilliseconds = (long)(DateTime.UtcNow - unixEpoch).TotalMilliseconds;
            return unixTimeMilliseconds;
        }
        #region WorkflowStep method
        private async Task<bool> WorkflowStepAsync(AutomationWorkflowResponseDto automationWorkflow, Contacts contact, string workflowName, Conversations conversation)
        {
            string sendTextMessage = ReplacePlaceholders(automationWorkflow.Title, (GetContactValues(contact, automationWorkflow.Veriables)));
            var buttonValues = automationWorkflow.AutoReplyWorkflowButtons?.Select((btn, index) => new ButtonValueDto
            {
                Key = $"{btn}_{index + 1}",
                Value = btn
            }).ToList();
            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(contact.BusinessId.ToString(), contact, conversation, @events: new List<IntegrationEvent> { IntegrationEvent.AutoReply }), CancellationToken.None);
            // update contact step
            contact.WorkflowName = workflowName;
            contact.WorkflowStep = (automationWorkflow?.Step);
            _applicationDbContext.Contacts.Update(contact);
            await _applicationDbContext.SaveChangesAsync();
            return await SendAutoReplyMessageToContactAsync(conversation, sendTextMessage, buttonValues, contact.BusinessId, automationWorkflow.AutoReplyWorkflowList, automationWorkflow, contact);
        }
        #endregion

        #region Send auto reply automation message to contact
        private async Task<bool> SendAutoReplyMessageToContactAsync(Conversations conversation,
            string replyMessage,
            List<ButtonValueDto> buttons,
            Guid businessId,
            WorkflowListDto? workflowListDto = null,
            AutomationWorkflowResponseDto? value = null,
            Contacts? contact = null)
        {
            bool isSuccess = false;
            try
            {
                string contactNumber = conversation.From;
                if (!string.IsNullOrEmpty(value?.WebhookTriggerUrl))
                {
                    await ProcessWebhookTriggerUrlAsync(businessId, contact, value, conversation);
                }
                if (workflowListDto != null)
                {
                    if (workflowListDto?.Inputs != null && workflowListDto.Inputs.Any() && !string.IsNullOrEmpty(replyMessage))
                    {
                        isSuccess = await ProcessAutoReplyListMessageAsync(workflowListDto, replyMessage, contactNumber, businessId);
                    }
                }
                else if (buttons != null && buttons.Count > 0)
                {
                    if (buttons.Count > 0 && !string.IsNullOrEmpty(replyMessage))
                    {
                        isSuccess = await ProcessAutoReplyButtonsAsync(buttons, replyMessage, contactNumber, businessId);
                    }
                }
                else if (!string.IsNullOrEmpty(replyMessage))
                {
                    TextMessageRequest textMessageRequest = new TextMessageRequest();
                    textMessageRequest.To = conversation.From;
                    textMessageRequest.Text = new WhatsAppText();
                    textMessageRequest.Text.Body = replyMessage;
                    textMessageRequest.Text.PreviewUrl = false;
                    await _whatsAppBusinessClient.SendMessageAsync(textMessageRequest, businessId, null, MessageType.AutoReply);
                    isSuccess = true;
                }
                else
                {
                    var data = await _automationWorkflowService.GetWorkflowAsync(contact.BusinessId, null, null, value.WorkflowName);

                    value = data[$"{value.WorkflowName}"]?.FirstOrDefault(m => m.Step == (value.Step + 1));
                    if (value != null)
                    {
                        await WorkflowStepAsync(value, contact, value.WorkflowName, conversation);
                    }
                    isSuccess = true;
                }
                return isSuccess;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("Error:SendAutoReplyMessageToContactAsync", JsonConvert.SerializeObject(value), "", ex.Message, ex.StackTrace);
                return false;
            }
        }
        #endregion

        #region Webhook Url Trigger Url helper method
        private async Task<bool> ProcessWebhookTriggerUrlAsync(Guid businessId,
            Contacts contact, AutomationWorkflowResponseDto automationWorkflow,
            Conversations conv)
        {
            try
            {
                string url = ReplacePlaceholders(automationWorkflow.WebhookTriggerUrl, GetContactValues(contact,
                    automationWorkflow?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerUrl)?.ToList()));

                string body = automationWorkflow?.WebhookTriggerBody ?? string.Empty;
                var workflowResponse = await _autoReplyAutomationService.GetWorkflowCustomerResponseAsync(contact.ContactId, automationWorkflow?.WorkflowName, businessId, contact.WorkflowStartId);
                if (workflowResponse?.Any() == true)
                {
                    var wResponse = automationWorkflow?.Veriables?.Join(
                                workflowResponse,
                                v => v.Value?.ToLowerInvariant().Trim(),
                                w => w.VeriableName?.ToLowerInvariant().Trim(),
                                (v, w) => new
                                {
                                    Variable = v.Veriable,
                                    Response = StringHelper.FormateEscapeSequences(w.Response ?? string.Empty),
                                    w.WorkflowName
                                }
                            ).GroupBy(x => x.Variable)
                            .Select(x => x.Last());
                    var keyAndValues = wResponse?.Where(x => x.WorkflowName == automationWorkflow?.WorkflowName)?.ToDictionary(dict => dict.Variable, dict => dict.Response);
                    body = ReplacePlaceholders(automationWorkflow?.WebhookTriggerBody ?? string.Empty, values: keyAndValues ?? new());
                    body = ReplacePlaceholders(body ?? "", GetContactValues(contact,
                        automationWorkflow?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerBody)?.ToList()));
                }
                else
                {
                    body = ReplacePlaceholders(body ?? "", GetContactValues(contact,
                    automationWorkflow?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerBody)?.ToList()));
                }
                string? headerJson = automationWorkflow?.WebhookTriggerHeader == null ? null : ReplacePlaceholders(JsonConvert.SerializeObject(automationWorkflow?.WebhookTriggerHeader),
                GetContactValues(contact, automationWorkflow?.Veriables?.Where(m => m.Type == VariableType.WebhookTriggerHeader)?.ToList()));
                List<WebhookHeader>? webhookHeader = new List<WebhookHeader>();
                if (!string.IsNullOrEmpty(headerJson))
                {
                    webhookHeader = JsonConvert.DeserializeObject<List<WebhookHeader>>(headerJson);
                }
                HttpMethod method = automationWorkflow?.WebhookTriggerHttpMethod?.ToUpper() switch
                {
                    "POST" => HttpMethod.Post,
                    "GET" => HttpMethod.Get,
                    "PUT" => HttpMethod.Put,
                    "DELETE" => HttpMethod.Delete,
                    "PATCH" => HttpMethod.Patch,
                    _ => HttpMethod.Get
                };
                if (method != null && !string.IsNullOrEmpty(body) && webhookHeader != null && !string.IsNullOrEmpty(url))
                {
                    await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessWebhookTriggerUrlAsync", JsonConvert.SerializeObject(automationWorkflow), body, url);
                    var response = await ExecuteWebhookAsync(url, body, webhookHeader, method);
                    if (!response && !string.IsNullOrEmpty(automationWorkflow?.DefaultErrorResponse))
                    {
                        var contactNumber = string.Concat(contact.CountryCode, contact.Contact).Replace("+", string.Empty);
                        var conversation = new Conversations()
                        {
                            Id = Guid.NewGuid(),
                            From = businessId.ToString(),
                            To = contactNumber,
                            CreatedAt = DateTime.UtcNow,
                            Status = EngagetoEntities.Enums.ConvStatus.sent,
                            MessageType = MessageType.AutoReply
                        };
                        var resultResponse = await _metaApiService.SendTextWithMediaMessageAsync(businessId.ToString(), contactNumber, automationWorkflow?.DefaultErrorResponse ?? string.Empty, "text", null, null);
                        if (resultResponse.IsSuccess)
                        {
                            var result = resultResponse.Result.ToObject<WhatsAppResponse>();
                            conversation.TextMessage = automationWorkflow?.DefaultErrorResponse;
                            conversation.Status = ConvStatus.sent;
                            conversation.WhatsAppMessageId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty;
                            _applicationDbContext.Conversations.Add(conversation);
                            await _applicationDbContext.SaveChangesAsync();
                            await SendConvMessageOnServer(businessId.ToString(), new() { conversation});
                        }
                    }
                    await _logHistoryService.SaveSuccessLogHistoryAsyn("ProcessWebhookTriggerUrlAsync", JsonConvert.SerializeObject(automationWorkflow), JsonConvert.SerializeObject(response), url);
                }
                return true;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("Error:ProcessWebhookTriggerUrlAsync", JsonConvert.SerializeObject(automationWorkflow), "", ex.Message, JsonConvert.SerializeObject(ex));
                return false;
            }
        }
        #endregion
        #region Process Auto reply list message
        public async Task<bool> ProcessAutoReplyListMessageAsync(WorkflowListDto workflowListDto, string replyMessage, string conversationFrom, Guid businessId)
        {
            try
            {
                // Create rows for the list message
                var rows = workflowListDto.Inputs
                    .Select((input, index) => new EngagetoEntities.Dtos.MetaDto.AutoReply.Row
                    {
                        id = index.ToString(),
                        title = input.Title,
                        description = input.Description
                    }).ToList();

                // Create sections with rows
                var sections = new List<EngagetoEntities.Dtos.MetaDto.AutoReply.Section>
                {
                    new EngagetoEntities.Dtos.MetaDto.AutoReply.Section
                    {
                        title = workflowListDto.ListName,
                        rows = rows
                    }
                };

                // Construct the AutoReply message
                var listMessage = new EngagetoEntities.Dtos.MetaDto.AutoReply.Rootobject
                {
                    to = conversationFrom,
                    interactive = new EngagetoEntities.Dtos.MetaDto.AutoReply.Interactive
                    {
                        type = "list",
                        body = new EngagetoEntities.Dtos.MetaDto.AutoReply.Body
                        {
                            text = replyMessage
                        },
                        action = new EngagetoEntities.Dtos.MetaDto.AutoReply.Action
                        {
                            button = workflowListDto.ButtonName,
                            sections = sections
                        }
                    }
                };

                // Send the message via WhatsApp Business API
                var result = await _whatsAppBusinessClient.WhatsAppBusinessPostAsync(listMessage, businessId, null, MessageType.AutoReply);
                await _logHistoryService.SaveSuccessLogHistoryAsyn("ProcessAutoReplyListMessageAsync", JsonConvert.SerializeObject(workflowListDto), JsonConvert.SerializeObject(listMessage), JsonConvert.SerializeObject(result));
                return true;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("Error:ProcessAutoReplyListMessageAsync", JsonConvert.SerializeObject(workflowListDto), "", ex.Message, ex.StackTrace);
                return false;
            }
        }
        #endregion
        #region Process Auto reply button message
        public async Task<bool> ProcessAutoReplyButtonsAsync(List<ButtonValueDto> buttons, string replyMessage, string conversationFrom, Guid businessId)
        {
            try
            {
                // Validate inputs
                if (buttons == null || !buttons.Any() || string.IsNullOrEmpty(replyMessage))
                {
                    return false;
                }
                // Extract button values
                var buttonValues = buttons.Select(m => m.Value).ToList();
                // Generate button objects
                var autoReplyButtons = buttonValues
                    .Select((value, index) => new EngagetoEntities.Dtos.MetaDto.AutoReply.Button
                    {
                        reply = new EngagetoEntities.Dtos.MetaDto.AutoReply.Reply
                        {
                            title = value,
                            id = index.ToString()
                        }
                    }).ToList();

                // Create the AutoReply message
                var textMessage = new EngagetoEntities.Dtos.MetaDto.AutoReply.Rootobject
                {
                    to = conversationFrom,
                    interactive = new EngagetoEntities.Dtos.MetaDto.AutoReply.Interactive
                    {
                        body = new EngagetoEntities.Dtos.MetaDto.AutoReply.Body
                        {
                            text = replyMessage ?? string.Empty
                        },
                        action = new EngagetoEntities.Dtos.MetaDto.AutoReply.Action
                        {
                            buttons = autoReplyButtons
                        }
                    }
                };
                var result = await _whatsAppBusinessClient.WhatsAppBusinessPostAsync(textMessage, businessId, null, MessageType.AutoReply);
                await _logHistoryService.SaveSuccessLogHistoryAsyn("ProcessAutoReplyListMessageAsync", JsonConvert.SerializeObject(buttonValues), JsonConvert.SerializeObject(textMessage), JsonConvert.SerializeObject(result));
                return true;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("Error:ProcessAutoReplyButtonsAsync", buttons, "", ex.Message, ex.StackTrace);
                return false;
            }
        }

        #endregion
        public async Task<bool> ExecuteWebhookAsync(string url, string body, List<WebhookHeader> webhookHeaders, HttpMethod method)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                var request = new HttpRequestMessage(method, url);
                if (method == HttpMethod.Post || method == HttpMethod.Put || method == HttpMethod.Patch)
                {
                    if (!string.IsNullOrEmpty(body))
                    {
                        request.Content = new StringContent(body, Encoding.UTF8, "application/json");
                    }
                }
                if (webhookHeaders != null && webhookHeaders.Count > 0)
                {
                    foreach (var header in webhookHeaders)
                    {
                        if (!string.IsNullOrEmpty(header.Key) && !string.IsNullOrEmpty(header.Value))
                        {
                            try
                            {
                                request.Headers.Add(header.Key, header.Value);
                            }
                            catch (InvalidOperationException)
                            {
                                if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase) && request.Content != null)
                                {
                                    request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(header.Value);
                                }
                            }
                        }
                    }
                }
                try
                {
                    HttpResponseMessage response = await httpClient.SendAsync(request);
                    string responseContent = await response.Content.ReadAsStringAsync();
                    await _logHistoryService.SaveErrorLogHistoryAsyn("ExecuteWebhookAsync3", responseContent, "Api response", responseContent, null);
                    return response.IsSuccessStatusCode;
                }
                catch (HttpRequestException ex)
                {

                    Console.WriteLine($"Request error: {ex.Message}");
                    await _logHistoryService.SaveErrorLogHistoryAsyn("ExecuteWebhookAsync", body, "Getting an error calling the API", ex.Message, ex.StackTrace);
                    return false; // Return false on failure
                }
                catch (Exception ex)
                {
                    await _logHistoryService.SaveErrorLogHistoryAsyn("ExecuteWebhookAsync1", body, "Getting an error calling the API", ex.Message, ex.StackTrace);
                    Console.WriteLine($"Unexpected error: {ex.Message}");
                    return false;
                }
            }
        }

        private Dictionary<string, string> GetContactValues(Contacts contact, List<VeriableDto>? variableDtos)
        {
            if (variableDtos is null || !variableDtos.Any())
            {
                return new Dictionary<string, string>();
            }

            if (contact is null)
            {
                return variableDtos.ToDictionary(
                    dict => dict.Veriable,
                    dict => dict.FallbackValue ?? string.Empty
                );
            }

            var contactDict = StringHelper.GetPropertyNamesAndValues(contact);

            return variableDtos.ToDictionary(
                dict => dict.Veriable,
                dict => contactDict.TryGetValue(dict.Value ?? string.Empty, out var value)
                        ? value?.ToString() ?? string.Empty
                        : dict.FallbackValue ?? string.Empty
            );
        }
        private string ReplacePlaceholders(string format, Dictionary<string, string> values)
        {
            StringBuilder sb = new StringBuilder(format);
            foreach (var kvp in values)
            {
                sb.Replace(kvp.Key, $"\"{kvp.Value}\"");
            }
            return sb.ToString();
        }
        private async Task SendConvMessageOnServer(string businessId, List<Conversations> conversations, bool? isRenderContact = true)
        {
            if (string.IsNullOrEmpty(businessId) || conversations == null || !conversations.Any())
                return;

            try
            {
                var convMessages = conversations.Adapt<List<ConversationDto>>();
                var replyIds = conversations.Where(c => !string.IsNullOrEmpty(c.ReplyId)).Select(c => c.ReplyId).ToList();

                if (replyIds.Any())
                {
                    var repliesConvs = (await _applicationDbContext.Conversations
                        .Where(c => replyIds.Contains(c.WhatsAppMessageId))
                        .ToListAsync()).Adapt<List<ConversationDto>>();

                    var replyDict = repliesConvs.ToDictionary(r => r.WhatsAppMessageId);
                    convMessages.ForEach(conv =>
                    {
                        if (!string.IsNullOrEmpty(conv.ReplyId) && replyDict.TryGetValue(conv.ReplyId, out var replyConv))
                        {
                            conv.Reply = replyConv;
                        }
                    });
                }
                var users = await _applicationDbContext.Users.Where(u => u.CompanyId == businessId).Select(u => new { u.Id }).ToListAsync();
                var groupPrefix = businessId.ToLower();
                var messageTasks = users.Select(user => _messageHub.Clients.Groups($"{groupPrefix}{user.Id.ToString().ToLower()}").ReceiveMessageFromServer(convMessages));
                var contactTasks = Enumerable.Empty<Task>();
                if (isRenderContact ?? false)
                {
                    contactTasks = users.Select(user =>
                        _messageHub.Clients
                            .Groups($"{groupPrefix}{user.Id.ToString().ToLower()}")
                            .RenderContacts()
                    );
                }
                // Run all tasks concurrently
                await Task.WhenAll(messageTasks.Concat(contactTasks));
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("SendConvMessageOnServer:Error", JsonConvert.SerializeObject(conversations), JsonConvert.SerializeObject(ex), "processing message hub");
                Console.WriteLine($"Error in SendConvMessageOnServer: {ex.Message}");
            }
        }
        #endregion
    }
}
