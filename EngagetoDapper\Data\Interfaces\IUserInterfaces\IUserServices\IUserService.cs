﻿using EngagetoEntities.Dtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.WalletDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.Identity.Client;

namespace EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices
{
    public interface IUserService
    {
        Task<Ahex_CRM_BusinessDetails?> GetBusinessDetailsAsync(string companyId);
        Task<Dictionary<string, List<Dictionary<string, List<string>>>>> GetMenuDetailsAsync(string companyId, string roleId);
        Task<Dictionary<string, List<MainMenu>>> GetPermissionMenuAsync(string companyId, string roleId);
        Task<DiscountEntity> SaveDiscountAsync(DiscountDto discountDto, Guid userId);
        Task<DiscountEntity> UpdateDiscount(DiscountEntity discount);
        Task<DiscountEntity> GetDiscountByDicountCode(string code);
        Task<DiscountEntity?> GetDiscountById(Guid id);
        Task<List<DiscountDto>> GetAllDiscountAsync();
        Task<List<TransactionHistoryEntity>?> GetCompanyWalletTransactionsHistoryAsync(string companyId);
        Task<BusinessDetailsMeta?> IsValidateMetaAccount(string companyId, Guid? usingId = null);
        Task<IEnumerable<InboxContactDto>> GetInboxContactsAsync(string companyId, Guid userId, string businessPhoneNumber, CancellationToken cancellationToken);
        Task<ExpectedWalletBalanceDto?> GetExpectedWalletBalanceAsync(string companyId);
        Task<decimal> GetCampaignCostAsync(string companyId);
        Task<IEnumerable<ExpiringOrExpiredSubscriptionsDto>> GetExpiringOrExpiredSubscriptionsAsync();
        Task<IEnumerable<UserDto>> GetUsersByBusinessIdAync(string? businessId,string? waPhoneNumberId);

    }
}
