﻿using EngagetoContracts.CampaignContracts;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace EngagetoRepository.CampaignRepository
{
    public class CampaignRepositories : ICampaign
    {
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        public IHttpClientFactory _httpClientFactory { get; set; }
        private ApplicationDbContext _compaignDbContext;
        private readonly HttpClient _client = new HttpClient();
        private IConfiguration _Configuration { get; set; }
        private static Dictionary<Guid, Timer> timers = new Dictionary<Guid, Timer>();
        private readonly JsonSerializer _serializer = new JsonSerializer();
        SendHandler Handler = new SendHandler();
        public CampaignRepositories(IWhatsAppBusinessClient whatsAppBusinessClient, ApplicationDbContext dbContext, IHttpClientFactory IhttpClientFactory, IConfiguration configuration)
        {
            _Configuration = configuration;
            _compaignDbContext = dbContext;
            _httpClientFactory = IhttpClientFactory;
            _whatsAppBusinessClient = whatsAppBusinessClient;

        }
        // saving method before going to end user
        public async Task<Campaign> CreateCampaignDtoAsync(Campaigns model, string jobId)
        {
            var user = await _compaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
            var campaignId = model.CampaignId ?? Guid.NewGuid();
            Campaign SendcampaignDto;
            var existingCampaign = await _compaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignId == campaignId);

            if (existingCampaign != null)
            {
                // Edit existingCampaign
                existingCampaign.Audiance = string.Join(",", model.Audiance) ?? throw new Exception("Please provide the Audiance");
                existingCampaign.BusinessId = model.BusinessId;
                existingCampaign.UserId = (Guid)model.UserId;
                existingCampaign.CampaignTitle = model.CampaignTitle ?? throw new Exception("Please provide the CampaignTitle");
                existingCampaign.SendTextType = !string.IsNullOrEmpty(model.SendTextType) ? model.SendTextType : string.Empty;
                existingCampaign.MediaUrl = !string.IsNullOrEmpty(model.MediaFile) ? model.MediaFile : string.Empty;
                existingCampaign.TemplateId = model.TemplateId ?? Guid.Empty;
                existingCampaign.HeaderValue = !string.IsNullOrEmpty(model.HeaderVariableValue?.value) ? model.HeaderVariableValue.value : string.Empty;
                existingCampaign.BodyValues = model.BodyVariableValues != null ? string.Join(",", model.BodyVariableValues.Select(m => m.value ?? string.Empty)) : string.Empty;
                existingCampaign.RedirectUrlsValue = model.RedirectUrlVariableValues != null ? string.Join(",", model.RedirectUrlVariableValues) : string.Empty;
                existingCampaign.DateSetLive = model.ScheduledDateTime;
                existingCampaign.Editedby = user.Name;
                existingCampaign.Createdby = existingCampaign.Createdby;
                existingCampaign.EditedDate = DateTime.UtcNow;
                _compaignDbContext.Campaigns.Update(existingCampaign);
                SendcampaignDto = existingCampaign;
            }
            else
            {
                SendcampaignDto = new EngagetoEntities.Entities.Campaign()
                {
                    // save new campaign
                    CampaignId = campaignId,
                    Audiance = model.Audiance != null ? string.Join(",", model.Audiance) : string.Empty,
                    BusinessId = model.BusinessId,
                    UserId = (Guid)model.UserId,
                    CampaignTitle = model.CampaignTitle ?? string.Empty,
                    SendTextType = model.SendTextType ?? string.Empty,
                    MediaUrl = model.MediaFile ?? string.Empty,
                    TemplateId = model.TemplateId ?? Guid.Empty,
                    HeaderValue = model.HeaderVariableValue?.value ?? string.Empty,
                    BodyValues = model.BodyVariableValues != null ? string.Join(",", model.BodyVariableValues.Select(m => m.value?.ToString() ?? string.Empty)) : string.Empty,
                    RedirectUrlsValue = model.RedirectUrlVariableValues != null ? string.Join(",", model.RedirectUrlVariableValues) : string.Empty,
                    DateSetLive = model.ScheduledDateTime ?? DateTime.MinValue,
                    Createdby = user.Name ?? "Unknown",
                    Editedby = user.Name,
                    State = EngagetoEntities.Enums.CampaignState.Scheduled,
                    ScheduleJobId = jobId,
                    CreatedDate = DateTime.UtcNow,
                    EditedDate = DateTime.UtcNow

                };
                await _compaignDbContext.Campaigns.AddAsync(SendcampaignDto);
            }
            await _compaignDbContext.SaveChangesAsync();
            return SendcampaignDto;
        }
        // saving method after going to end user
        public async Task<Campaign> CreateNewCampaignDtoAsync(Campaigns model)
        {
            var user = await _compaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
            Guid campaignId = model.CampaignId ?? Guid.NewGuid();
            Campaign entity;
            Campaign newCampaignDto = new Campaign
            {
                CampaignId = campaignId,
                Audiance = model.Audiance != null ? string.Join(",", model.Audiance) : string.Empty,
                BusinessId = model.BusinessId,
                UserId = (Guid)model.UserId,
                CampaignTitle = model.CampaignTitle ?? string.Empty,
                SendTextType = model.SendTextType ?? string.Empty,
                MediaUrl = model.MediaFile ?? string.Empty,
                TemplateId = model.TemplateId ?? Guid.Empty,
                HeaderValue = model.HeaderVariableValue?.value ?? string.Empty,
                BodyValues = model.BodyVariableValues != null ? string.Join(",", model.BodyVariableValues.Select(m => m.value?.ToString() ?? string.Empty)) : string.Empty,
                RedirectUrlsValue = model.RedirectUrlVariableValues != null ? string.Join(",", model.RedirectUrlVariableValues) : string.Empty,
                Createdby = user?.Name ?? "Unknown",
                State = EngagetoEntities.Enums.CampaignState.Completed,
                CreatedDate = DateTime.UtcNow,
                EditedDate = DateTime.UtcNow
            };
            if (campaignId == model.CampaignId)
            {
                entity = (_compaignDbContext.Campaigns.Update(newCampaignDto)).Entity;
                await _compaignDbContext.SaveChangesAsync();
            }
            else
            {
                entity = (_compaignDbContext.Campaigns.Add(newCampaignDto)).Entity;
                await _compaignDbContext.SaveChangesAsync();
            }
            return entity;
        }


        public static string GetPropertyValue(object obj, string propertyName)
        {
            if (string.IsNullOrEmpty(propertyName) || obj.GetType().GetProperty(propertyName) == null)
            {
                return null;
            }
            PropertyInfo propertyInfo = obj.GetType().GetProperty(propertyName);
            object value = propertyInfo.GetValue(obj, null);
            string valueAsString = value != null ? value.ToString() : null;
            return string.IsNullOrEmpty(valueAsString) ? null : valueAsString;
        }
        public bool HasVariables(string text)
        {

            return Regex.IsMatch(text, @"\{\{\d+\}\}");
        }
        public string ReplaceVariable(string text, string[]? values)
        {
            for (int i = 0; i < values.Length; i++)
            {
                text = text.Replace($"{{{{{i + 1}}}}}", values[i]);
            }
            return text;
        }
        // for sending Text Message in campaign
        public async Task<List<WhatsAppResponse>> SendTextOrEmojiMessages(Campaigns model, Guid BusinessId, string phoneNumberId)
        {
            var results = new List<WhatsAppResponse>();

            if (!string.IsNullOrEmpty(phoneNumberId) && !string.IsNullOrEmpty(model.SendTextType))
            {
                TextMessageRequest textMessageRequest = new TextMessageRequest
                {
                    To = phoneNumberId,
                    Text = new WhatsAppText
                    {
                        Body = model.SendTextType,
                        PreviewUrl = false
                    }
                };
                var result = await _whatsAppBusinessClient.SendMessageAsync(textMessageRequest, BusinessId);
                results.Add(result);
            }
            return results;
        }

        // for sending Media Message with text in campaign
        public async Task<List<WhatsAppResponse>> SendMediaMessage(Campaigns model, Guid BusinessId, string phoneNumberId)
        {
            var results = new List<WhatsAppResponse>();
            var result = new WhatsAppResponse();
            var regex = new Regex(@"^\+?[1-9]\d{1,14}$");

            if (!string.IsNullOrEmpty(phoneNumberId) && !string.IsNullOrEmpty(model.MediaFile))
            {
                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(model.MediaFile);
                if (response.IsSuccessStatusCode)
                {
                    var contentType = response.Content.Headers.ContentType?.MediaType;
                    Uri uri = new Uri(model.MediaFile);
                    Path.GetFileName(uri.LocalPath);
                    var fileName = Path.GetFileName(uri.LocalPath);

                    if (contentType != null)
                    {
                        // Check media type based on content type
                        if (contentType.StartsWith("image"))
                        {
                            ImageMessageRequest imageMessageRequest = new ImageMessageRequest();
                            imageMessageRequest.To = phoneNumberId;
                            imageMessageRequest.Image = new WhatsAppImage();
                            imageMessageRequest.Image.Link = model.MediaFile;
                            imageMessageRequest.Image.Caption = model.SendTextType ?? null;
                            result = await _whatsAppBusinessClient.SendMessageAsync(imageMessageRequest, BusinessId);

                            // Image file
                            // Handle image processing or save to local storage
                        }
                        else if (contentType.StartsWith("video"))
                        {
                            VideoMessageRequest videoMessageRequest = new VideoMessageRequest();
                            videoMessageRequest.To = phoneNumberId;
                            videoMessageRequest.Video = new WhatsAppVideo();
                            videoMessageRequest.Video.Link = model.MediaFile;
                            videoMessageRequest.Video.Caption = model.SendTextType ?? null;


                            result = await _whatsAppBusinessClient.SendMessageAsync(videoMessageRequest, BusinessId);

                            // Video file
                            // Handle video processing or save to local storage
                        }
                        else if (contentType.StartsWith("audio"))
                        {
                            AudioMessageRequest audioMessageRequest = new AudioMessageRequest();
                            audioMessageRequest.To = phoneNumberId;
                            audioMessageRequest.Audio = new WhatsAppAudio();
                            audioMessageRequest.Audio.Link = model.MediaFile;

                            result = await _whatsAppBusinessClient.SendMessageAsync(audioMessageRequest, BusinessId);

                            // Audio file
                            // Handle audio processing or save to local storage
                        }
                        else if (contentType.StartsWith("text/plain") ||
                            contentType.StartsWith("application/pdf") ||
                            contentType.StartsWith("application/vnd.ms-powerpoint") ||
                            contentType.StartsWith("application/msword") ||
                            contentType.StartsWith("application/vnd.ms-excel") ||
                            contentType.StartsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                            contentType.StartsWith("application/vnd.openxmlformats-officedocument.presentationml.presentation") ||
                            contentType.StartsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                        {
                            DocumentMessageRequest documentMessageRequest = new DocumentMessageRequest();
                            documentMessageRequest.To = phoneNumberId;
                            documentMessageRequest.Document = new WhatsAppDocument();
                            documentMessageRequest.Document.Link = model.MediaFile;
                            documentMessageRequest.Document.Caption = model.SendTextType ?? null;
                            documentMessageRequest.Document.FileName = fileName;
                            result = await _whatsAppBusinessClient.SendMessageAsync(documentMessageRequest, BusinessId);
                        }
                    }
                }
                results.Add(result);
            }
            return results;
        }

        public async Task<HttpResponseMessage> CreateCampaignAsync(Campaigns model)
        {
            var Business = await _compaignDbContext.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = await _compaignDbContext.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business.Token);
            HttpResponseMessage response = new HttpResponseMessage();
            List<string> phoneNumbers = new List<string>();
            List<string> WhatsAppMessageId = new List<string>();
            bool flag = true;
            List<Guid> conversationIds = new List<Guid>();
            foreach (var id in model.Audiance)
            {
                var contact = _compaignDbContext.Contacts.Where(m => m.ContactId.ToString() == id && (int)m.IsOptIn == 1).FirstOrDefault();
                if (contact != null)
                {
                    string Contacts = $"{contact.CountryCode}{contact.Contact}";
                    string phoneNumberId = Contacts.Replace("+", "");
                    phoneNumbers.Add(phoneNumberId);

                    if (!string.IsNullOrEmpty(Contacts))
                    {
                        // for sending text Campaign
                        if (phoneNumbers.Any() && !string.IsNullOrEmpty(model.SendTextType) && string.IsNullOrEmpty(model.MediaFile)
                                                                                            && (!model.TemplateId.HasValue || model.TemplateId == Guid.Empty))
                        {
                            var results = await SendTextOrEmojiMessages(model, Guid.Parse(model.BusinessId), phoneNumberId);


                            if (results != null && results.Count > 0)
                            {
                                foreach (var responseData in results)
                                {
                                    foreach (var message in responseData.Messages)
                                    {
                                        var retrievedEntity = await _compaignDbContext.Conversations.FirstOrDefaultAsync(c => c.WhatsAppMessageId == message.Id);
                                        if (retrievedEntity != null)
                                        {
                                            conversationIds.Add(retrievedEntity.Id);
                                            WhatsAppMessageId.Add(retrievedEntity.WhatsAppMessageId);
                                            var concatenatedMessageIds = string.Join(",", WhatsAppMessageId);


                                            if (model.ScheduledDateTime != null)
                                            {

                                                var SendcampaignDto = await _compaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignTitle == model.CampaignTitle);
                                                if (SendcampaignDto != null)
                                                {
                                                    SendcampaignDto.WhatsAppMessagesId = concatenatedMessageIds;
                                                    SendcampaignDto.DateSetLive = retrievedEntity.CreatedAt;
                                                    SendcampaignDto.State = EngagetoEntities.Enums.CampaignState.Completed;

                                                    try
                                                    {
                                                        _compaignDbContext.Campaigns.Update(SendcampaignDto);
                                                        await _compaignDbContext.SaveChangesAsync();
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        throw new Exception($"An error occurred: {ex.Message}", ex);
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                try
                                                {
                                                    var existingCampaign = await _compaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignTitle == model.CampaignTitle);
                                                    if (existingCampaign != null && !flag)
                                                    {
                                                        existingCampaign.WhatsAppMessagesId = concatenatedMessageIds;
                                                        _compaignDbContext.Campaigns.Update(existingCampaign);
                                                        await _compaignDbContext.SaveChangesAsync();
                                                    }
                                                    else if (flag)
                                                    {

                                                        var newCampaignDto = await CreateNewCampaignDtoAsync(model);
                                                        newCampaignDto.WhatsAppMessagesId = concatenatedMessageIds;
                                                        newCampaignDto.DateSetLive = retrievedEntity.CreatedAt;
                                                        _compaignDbContext.Campaigns.Update(newCampaignDto);
                                                        await _compaignDbContext.SaveChangesAsync();
                                                        flag = false;
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    throw new Exception($"An error occurred: {ex.Message}", ex);
                                                }
                                            }

                                            response.StatusCode = HttpStatusCode.OK;
                                        }

                                        else
                                        {
                                            Campaign SendcampaignDto = new Campaign
                                            {
                                                CampaignId = Guid.NewGuid(),
                                                CampaignTitle = model.CampaignTitle,
                                                BusinessId = model.BusinessId,
                                                UserId = (Guid)model.UserId,
                                                Createdby = user.Name,
                                                State = EngagetoEntities.Enums.CampaignState.Incompleted
                                            };

                                            try
                                            {
                                                await _compaignDbContext.Campaigns.AddAsync(SendcampaignDto);
                                                await _compaignDbContext.SaveChangesAsync();
                                            }
                                            catch (Exception ex)
                                            {
                                                throw new Exception($"An error occurred: {ex.Message}", ex);
                                            }

                                            response.StatusCode = HttpStatusCode.BadRequest;

                                        }
                                    }
                                }
                            }
                            // for sending Media with text in campaign
                            else if (phoneNumbers.Any() && !string.IsNullOrEmpty(model.MediaFile) && (!model.TemplateId.HasValue || model.TemplateId == Guid.Empty))
                            {
                                results = await SendMediaMessage(model, Guid.Parse(model.BusinessId), phoneNumberId);
                                if (results != null && results.Count > 0)
                                {
                                    foreach (var responseData in results)
                                    {
                                        foreach (var message in responseData.Messages)
                                        {
                                            var retrievedEntity = await _compaignDbContext.Conversations.FirstOrDefaultAsync(c => c.WhatsAppMessageId == message.Id);

                                            if (retrievedEntity != null)
                                            {
                                                conversationIds.Add(retrievedEntity.Id);
                                                WhatsAppMessageId.Add(retrievedEntity.WhatsAppMessageId);
                                            }
                                            var concatenatedMessageIds = string.Join(",", WhatsAppMessageId);

                                            if (retrievedEntity != null)
                                            {
                                                if (model.ScheduledDateTime != null)
                                                {
                                                    Campaign SendcampaignDto = await _compaignDbContext.Campaigns.FirstAsync(c => c.CampaignTitle == model.CampaignTitle);
                                                    SendcampaignDto.WhatsAppMessagesId = concatenatedMessageIds;
                                                    SendcampaignDto.DateSetLive = retrievedEntity.CreatedAt;
                                                    SendcampaignDto.State = EngagetoEntities.Enums.CampaignState.Completed;
                                                    try
                                                    {
                                                        _compaignDbContext.Campaigns.Update(SendcampaignDto);
                                                        await _compaignDbContext.SaveChangesAsync();
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        throw new Exception($"An error occurred: {ex.Message}", ex);
                                                    }

                                                }
                                                else
                                                {
                                                    // working in immidiate campaign 
                                                    try
                                                    {
                                                        var existingCampaign = await _compaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignTitle == model.CampaignTitle);
                                                        if (existingCampaign != null && flag == false)
                                                        {
                                                            existingCampaign.WhatsAppMessagesId = concatenatedMessageIds;
                                                            _compaignDbContext.Campaigns.Update(existingCampaign);
                                                            await _compaignDbContext.SaveChangesAsync();
                                                        }
                                                        if (flag)
                                                        {
                                                            var newCampaignDto = await CreateNewCampaignDtoAsync(model);
                                                            newCampaignDto.WhatsAppMessagesId = concatenatedMessageIds;
                                                            newCampaignDto.DateSetLive = retrievedEntity.CreatedAt;
                                                            _compaignDbContext.Campaigns.Update(newCampaignDto);
                                                            await _compaignDbContext.SaveChangesAsync();
                                                            flag = false;
                                                        }
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        throw new Exception($"An error occurred: {ex.Message}", ex);
                                                    }
                                                }
                                            }

                                        }
                                    }
                                    response.StatusCode = HttpStatusCode.OK;
                                }
                                else
                                {
                                    Campaign SendcampaignDto = new Campaign
                                    {
                                        CampaignId = Guid.NewGuid(),
                                        CampaignTitle = model.CampaignTitle,
                                        BusinessId = model.BusinessId,
                                        UserId = (Guid)model.UserId,
                                        Createdby = user.Name,
                                        State = EngagetoEntities.Enums.CampaignState.Incompleted
                                    };
                                    try
                                    {
                                        await _compaignDbContext.Campaigns.AddAsync(SendcampaignDto);
                                        await _compaignDbContext.SaveChangesAsync();
                                    }
                                    catch (Exception ex)
                                    {
                                        throw new Exception($"An error occurred: {ex.Message}", ex);
                                    }
                                }
                                response.StatusCode = HttpStatusCode.BadRequest;
                            }
                        }
                        // for sending Template campaign
                        else if (phoneNumbers.Any() && (model.TemplateId.HasValue || model.TemplateId != Guid.Empty) && string.IsNullOrEmpty(model.SendTextType))
                        {
                            var template = _compaignDbContext.Templates.FirstOrDefault(t => t.TemplateId == model.TemplateId);
                            var bodyMessage = StringHelper.ReplaceAndExtractVariables(template.Body).UpdatedMessage;
                            var headerMessage = StringHelper.ReplaceAndExtractVariables(template.Header ?? string.Empty).UpdatedMessage;
                            template.Buttons = _compaignDbContext.ButtonDetails.Where(m => m.TemplateId == template.TemplateId).ToList();                // Separate buttons by type
                            var urlButtons = template.Buttons.Where(b => b.ButtonType == "URL").ToList();
                            var phoneNumberButtons = template.Buttons.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
                            var quickReplyButtons = template.Buttons.Where(b => b.ButtonType == "QUICK_REPLY").ToList();
                            if (template == null)
                            {
                                return new HttpResponseMessage(HttpStatusCode.NotFound);
                            }
                            var MadiaUrls = template.MediaAwsUrl;
                            if (model.MediaUrl != null || model.MediaFile != null || Handler.S3Url != null)
                            {
                                MadiaUrls = model.MediaUrl != null ? model.MediaUrl : Handler.S3Url;
                            }

                            //for save into database
                            // Replace variables in header with provided values if they contain placeholders
                            string TempHeader = headerMessage;
                            string TemptBody = bodyMessage;
                            string TemptButtonQuickReply = String.Join(",", urlButtons.Select(m => m.ButtonName));
                            string TemptButtonRedirectUrl = string.Join(",", urlButtons.Select(b => b.ButtonValue));
                            string TemptButtonRedirectUrls = "";    // https://www.google.com/{{1}},https://www.google.com/{{1}}
                            int i = 1, j = 0;
                            foreach (var button in urlButtons)
                            {
                                if (i == 1)
                                {
                                    if (HasVariables(button.ButtonValue))
                                    {
                                        TemptButtonRedirectUrls = button.ButtonValue.Replace("{{1}}", model.RedirectUrlVariableValues[j].ToString());
                                        j++;
                                    }
                                    else
                                    {
                                        TemptButtonRedirectUrls = button.ButtonValue;
                                    }
                                    i++;
                                    continue;
                                }
                                if (i == 2)
                                {
                                    TemptButtonRedirectUrls = TemptButtonRedirectUrls + ",";

                                    if (HasVariables(button.ButtonValue))
                                    {
                                        TemptButtonRedirectUrls = TemptButtonRedirectUrls + button.ButtonValue.Replace("{{1}}", model.RedirectUrlVariableValues[j].ToString());
                                    }
                                    else
                                    {
                                        TemptButtonRedirectUrls = TemptButtonRedirectUrls + button.ButtonValue;
                                    }
                                }
                            }

                            if (template != null && TempHeader != null && HasVariables(headerMessage))
                            {
                                string HeaderVariableValue = GetPropertyValue(contact, model.HeaderVariableValue.value) ?? model.HeaderVariableValue.fallBackValue;
                                if (model.HeaderVariableValue != null)
                                {
                                    TempHeader = ReplaceVariable(headerMessage, new string[] { HeaderVariableValue });
                                }
                            }


                            if (template != null && TemptBody != null && HasVariables(bodyMessage))
                            {
                                var replacedBody = bodyMessage;
                                var matches = Regex.Matches(replacedBody, @"\{\{(\d+)\}\}");
                                int index = matches.Count();
                                int a = 0;
                                foreach (Match match in matches)
                                {
                                    string BodyVariableValues = GetPropertyValue(contact, model.BodyVariableValues[a].value) ?? model.BodyVariableValues[a].fallBackValue;
                                    if (index > 0 && index <= model.BodyVariableValues.Count)
                                    {
                                        var bodyValueParts = BodyVariableValues.ToString().Split(',');
                                        if (index > 0)
                                        {
                                            replacedBody = replacedBody.Replace(match.Value, bodyValueParts[0].Trim());
                                        }
                                        a++;
                                    }
                                    index--;
                                }

                                TemptBody = replacedBody;
                            }

                            // send to meta json data
                            string modifiedHeader = headerMessage;
                            string modifiedBody = bodyMessage;
                            string ButtonsRedirectUrlWithVariable = string.Join(",", urlButtons.Select(b => b.ButtonValue));
                            if (template != null && modifiedHeader != null && HasVariables(headerMessage))
                            {
                                string HeaderVariableValue1 = GetPropertyValue(contact, model.HeaderVariableValue.value) ?? model.HeaderVariableValue.fallBackValue;
                                var match = Regex.Match(headerMessage, @"\{\{\d+\}\}"); // Find the first match
                                if (match.Success)
                                {
                                    modifiedHeader = HeaderVariableValue1;
                                }
                            }
                            int i1 = 1, j1 = 0;
                            string modifiedButtonRedirectUrl1 = null;
                            string modifiedButtonRedirectUrl2 = null;
                            foreach (var button in urlButtons)
                            {
                                if (i1 == 1)
                                {
                                    if (HasVariables(button.ButtonValue))
                                    {
                                        modifiedButtonRedirectUrl1 = model.RedirectUrlVariableValues[j1].ToString();
                                        j1++;
                                    }
                                    i1++;
                                    continue;
                                }
                                if (i1 == 2)
                                {
                                    if (HasVariables(button.ButtonValue))
                                    {
                                        modifiedButtonRedirectUrl2 = model.RedirectUrlVariableValues[j1].ToString();
                                    }
                                }
                            }
                            if (modifiedButtonRedirectUrl1 == null)
                            {
                                modifiedButtonRedirectUrl1 = modifiedButtonRedirectUrl2;
                                modifiedButtonRedirectUrl2 = null;
                            }

                            if (template != null && modifiedBody != null && HasVariables(bodyMessage))
                            {
                                var parameters = new List<JObject>();
                                var matches = Regex.Matches(bodyMessage, @"\{\{\d+\}\}");
                                int index = matches.Count;
                                int a = 0;

                                foreach (Match match in matches)
                                {
                                    string BodyVariableValues = GetPropertyValue(contact, model?.BodyVariableValues[a]?.value) ?? model?.BodyVariableValues[a]?.fallBackValue;
                                    if (index > 0 && index <= model.BodyVariableValues.Count)
                                    {
                                        var value = BodyVariableValues;
                                        var parameter = new JObject
                                        {
                                            ["type"] = "text",
                                            ["text"] = value
                                        };
                                        parameters.Add(parameter);
                                        a++;
                                    }
                                    index--;
                                }
                                modifiedBody = JsonConvert.SerializeObject(parameters);
                            }

                            // var Mediatype = template.Mediatype.ToLower();
                            var Mediatype = template.MediaType.ToString().ToLower();

                            if (Mediatype == "none" || Mediatype == "text")
                            {
                                Mediatype = null;
                            }
                            var jsonData = $@"
                            {{
                               ""messaging_product"": ""whatsapp"",
                               ""recipient_type"": ""individual"",
                               ""to"": ""{phoneNumberId}"",
                               ""type"": ""template"",
                               ""template"": {{
                               ""name"": ""{template.TemplateName}"",
                               ""language"": {{
                               ""code"": ""{template.LanguageCode}""
                            }}";

                            // Check if MediaTypes is null (work in none && text template) where template,body are not null or empty, and they doesn't contain variables.
                            if (Mediatype == null && template != null && (!string.IsNullOrEmpty(bodyMessage) ||
                                                                          !string.IsNullOrEmpty(headerMessage) ||
                                                                          !string.IsNullOrEmpty(ButtonsRedirectUrlWithVariable)))
                            {
                                bool bodyContainsVariable = HasVariables(bodyMessage);
                                bool headerContainsVariable = headerMessage != null ? HasVariables(headerMessage) : false;
                                bool RedirectUrlsContainsVariable = ButtonsRedirectUrlWithVariable != null ? HasVariables(ButtonsRedirectUrlWithVariable) : false;
                                if (!bodyContainsVariable && !RedirectUrlsContainsVariable && !headerContainsVariable)
                                {
                                    jsonData += $@"

                                        }}
                                    }}";
                                }
                            }
                            // Check if MediaTypes is not null (work in media template) where template,body are not null or empty, and they doesn't contain variables.
                            if (Mediatype != null && template != null && (!string.IsNullOrEmpty(bodyMessage) ||
                                                                          !string.IsNullOrEmpty(ButtonsRedirectUrlWithVariable)))
                            {
                                bool bodyContainsVariable = HasVariables(bodyMessage);
                                bool RedirectUrlsContainsVariable = ButtonsRedirectUrlWithVariable != null ? HasVariables(ButtonsRedirectUrlWithVariable) : false;
                                if (!bodyContainsVariable && !RedirectUrlsContainsVariable)
                                {

                                    jsonData += $@",
                                     ""components"": [
                                                      {{
                                                          ""type"": ""header"",
                                                                    ""parameters"": [
                                                                                     {{
                                                                                       ""type"": ""{Mediatype.ToLower()}"",
                                                                                       ""{Mediatype.ToLower()}"": {{
                                                                                                                    ""link"": ""{MadiaUrls}""
                                                                                                                   }}
                                                                                     }}
                                                                                    ]
                                                                                  }}
                                                      ]
                                                   }} 
                                              }}";
                                }
                            }

                            // Check if MediaTypes is null (work in none template) where template,body are not null or empty, and they contain variables.

                            if (Mediatype == null && template != null && ((!string.IsNullOrEmpty(bodyMessage) && HasVariables(bodyMessage)) ||
                                                                          (!string.IsNullOrEmpty(ButtonsRedirectUrlWithVariable) && HasVariables(ButtonsRedirectUrlWithVariable))) &&
                                                                            string.IsNullOrEmpty(headerMessage))

                            {
                                bool bodyContainsVariable = HasVariables(bodyMessage);
                                bool RedirectUrlsContainsVariable = ButtonsRedirectUrlWithVariable != null ? HasVariables(ButtonsRedirectUrlWithVariable) : false;

                                jsonData += $@",
                                 ""components"": [";

                                bool firstComponent = true;

                                if (bodyContainsVariable)
                                {
                                    jsonData += $@"
                                              {{
                                                ""type"": ""body"",
                                                ""parameters"": {modifiedBody}
                                              }}";

                                    firstComponent = false;
                                }

                                int index = 0;
                                if (phoneNumberButtons != null)
                                {
                                    index = 1;
                                }
                                if (RedirectUrlsContainsVariable)
                                {
                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                    {
                                        if (!firstComponent)
                                        {
                                            jsonData += ",";
                                        }

                                        jsonData += $@"    
                                                     {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl1}""
                                                           }}
                                                         ]
                                                      }}";
                                        index++;

                                        if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                        {
                                            jsonData += $@",     
                                                      {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl2}""
                                                           }}
                                                         ]
                                                      }}";
                                        }
                                    }
                                }
                                jsonData += $@"
                                ]
                               }}
                             }}";
                            }
                            // Check if MediaTypes is null (work in text template) and template, body, and header are not null or empty, and they contain variables.
                            if (Mediatype == null && template != null && (((!string.IsNullOrEmpty(bodyMessage) && HasVariables(bodyMessage)) ||
                                                                           (!string.IsNullOrEmpty(ButtonsRedirectUrlWithVariable) && HasVariables(ButtonsRedirectUrlWithVariable)) ||
                                                                           (!string.IsNullOrEmpty(headerMessage) && HasVariables(headerMessage)))) && !string.IsNullOrEmpty(headerMessage))
                            {
                                bool bodyContainsVariable = HasVariables(bodyMessage);
                                bool headerContainsVariable = headerMessage != null ? HasVariables(headerMessage) : false;
                                bool RedirectUrlsContainsVariable = ButtonsRedirectUrlWithVariable != null ? HasVariables(ButtonsRedirectUrlWithVariable) : false;

                                jsonData += $@",
                                 ""components"": [";
                                bool firstComponent = true;

                                if (headerContainsVariable)
                                {

                                    if (!string.IsNullOrEmpty(modifiedHeader) && modifiedHeader.Trim() != "")
                                    {

                                        jsonData += $@"
                                                     {{
                                                       ""type"": ""header"",
                                                       ""parameters"":
                                                                      [
                                                                       {{
                                                                         ""type"": ""text"",
                                                                         ""text"": ""{modifiedHeader}""
                                                                        }}
                                                                       ]
                                                      }}";

                                        firstComponent = false;
                                    }
                                }
                                if (bodyContainsVariable)
                                {
                                    if (!firstComponent)
                                    {
                                        jsonData += ",";
                                    }

                                    jsonData += $@"
                                                   {{
                                                     ""type"": ""body"",
                                                     ""parameters"": {modifiedBody}
                                                   }}";

                                    firstComponent = false;

                                }
                                int index = 0;
                                if (phoneNumberButtons != null)
                                {
                                    index = 1;
                                }
                                if (RedirectUrlsContainsVariable)
                                {
                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                    {
                                        if (!firstComponent)
                                        {
                                            jsonData += ",";
                                        }

                                        jsonData += $@"    
                                                     {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl1}""
                                                           }}
                                                         ]
                                                      }}";
                                        index++;

                                        if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                        {
                                            jsonData += $@",     
                                                      {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl2}""
                                                           }}
                                                         ]
                                                      }}";
                                        }
                                    }
                                }
                                jsonData += $@"
                                 ]
                               }}
                            }}";

                            }
                            // Check if MediaTypes is not null (work in Media template) and template, body, and header are not null or empty, and they contain variables.
                            if (Mediatype != null && template != null && (!string.IsNullOrEmpty(bodyMessage) && HasVariables(bodyMessage) ||
                                                                          !string.IsNullOrEmpty(ButtonsRedirectUrlWithVariable) && HasVariables(ButtonsRedirectUrlWithVariable)))
                            {
                                bool bodyContainsVariable1 = HasVariables(bodyMessage);
                                bool RedirectUrlsContainsVariable = ButtonsRedirectUrlWithVariable != null ? HasVariables(ButtonsRedirectUrlWithVariable) : false;
                                bool firstComponent = true;

                                jsonData += $@",
                                    ""components"": [
                                                    {{
                                                      ""type"": ""header"",
                                                                ""parameters"": [
                                                                                {{
                                                                                 ""type"": ""{Mediatype.ToLower()}"",
                                                                                 ""{Mediatype.ToLower()}"": {{
                                                                                                              ""link"": ""{MadiaUrls}""
                                                                                                             }}
                                                                                 }}
                                                                                ]
                                                                            }}";
                                firstComponent = false;

                                if (bodyContainsVariable1)
                                {
                                    if (!firstComponent)
                                    {
                                        jsonData += ",";
                                    }

                                    jsonData += $@"
                                         {{
                                            ""type"": ""body"",
                                            ""parameters"": {modifiedBody}
                                         }}";
                                    firstComponent = false;


                                }
                                int index = 0;
                                if (phoneNumberButtons != null)
                                {
                                    index = 1;
                                }
                                if (RedirectUrlsContainsVariable)
                                {
                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                    {
                                        if (!firstComponent)
                                        {
                                            jsonData += ",";
                                        }

                                        jsonData += $@"    
                                                     {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl1}""
                                                           }}
                                                         ]
                                                      }}";
                                        index++;

                                        if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                        {
                                            jsonData += $@",     
                                                      {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl2}""
                                                           }}
                                                         ]
                                                      }}";
                                        }
                                    }
                                }
                                jsonData += $@"
                              ]
                            }}
                         }}";

                            }

                            JObject jsonObject = JObject.Parse(jsonData);
                            var request = new HttpRequestMessage(new HttpMethod("POST"), $"https://graph.facebook.com/v21.0/{Business.PhoneNumberID}/messages");
                            request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                            response = await _client.SendAsync(request);


                            if (response.IsSuccessStatusCode)
                            {
                                await using var stream = await response.Content.ReadAsStreamAsync();
                                using var reader = new StreamReader(stream);
                                using var jsonReader = new JsonTextReader(reader);
                                var result = _serializer.Deserialize<SendMessageResponseDto>(jsonReader);

                                SendStatus status;
                                switch (result.Messages[0].Status)
                                {
                                    case SendStatus.Accepted:
                                        status = SendStatus.Accepted;
                                        break;
                                    case SendStatus.Rejected:
                                        status = SendStatus.Rejected;
                                        break;
                                    default:
                                        // Log or debug the unexpected status value
                                        Console.WriteLine($"Unexpected status value: {result.Messages[0].Status}");
                                        status = SendStatus.Accepted; // Assign a default status
                                        break;
                                }

                                MediaType mediaTypeEnum;
                                int? mediaTypeInt = null;
                                if (Enum.TryParse(template.MediaType.ToString(), out mediaTypeEnum))
                                {
                                    mediaTypeInt = (int)mediaTypeEnum; // Get the integer value
                                }
                                // Save data to Templates table
                                EngagetoEntities.Entities.Conversations sendTemplateEntity = new EngagetoEntities.Entities.Conversations
                                {
                                    Id = Guid.NewGuid(),
                                    TemplateMediaType = (MediaType)mediaTypeInt,
                                    TemplateMediaUrl = template.MediaAwsUrl,
                                    CreatedAt = DateTime.UtcNow,
                                    TemplateBody = TemptBody.Replace("\\\"", "\"").Replace("\\n", "\n"),
                                    TemplateHeader = TempHeader?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                                    TemplateFooter = template.Footer?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                                    CallButtonName = phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonName,
                                    PhoneNumber = phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonValue,
                                    UrlButtonNames = String.Join(",", urlButtons.Select(m => m.ButtonName)),
                                    RedirectUrls = TemptButtonRedirectUrls,
                                    QuickReplies = String.Join(",", quickReplyButtons.Select(m => m.ButtonValue))

                                };
                                if (result.Contacts != null && result.Contacts.Count > 0 && result.Messages != null && result.Messages.Count > 0)
                                {
                                    sendTemplateEntity.To = phoneNumberId;
                                    sendTemplateEntity.From = model.BusinessId;
                                    sendTemplateEntity.MessageType = MessageType.Campaigns;
                                    sendTemplateEntity.UserId = model.UserId.ToString();
                                    sendTemplateEntity.WhatsAppMessageId = result.Messages[0].WhatsAppMessageId;
                                    sendTemplateEntity.Status = ConvStatus.sent;

                                }
                                await _compaignDbContext.Conversations.AddAsync(sendTemplateEntity);
                                await _compaignDbContext.SaveChangesAsync();

                                foreach (var message in result.Messages)
                                {
                                    var retrievedEntity = await _compaignDbContext.Conversations.FirstOrDefaultAsync(c => c.WhatsAppMessageId == message.WhatsAppMessageId);
                                    if (retrievedEntity != null)
                                    {
                                        WhatsAppMessageId.Add(retrievedEntity.WhatsAppMessageId);
                                        conversationIds.Add(retrievedEntity.Id);
                                    }
                                    var concatenatedMessageIds = string.Join(",", WhatsAppMessageId);

                                    if (retrievedEntity != null)
                                    {

                                        //work in schedule campaign 
                                        if (model.ScheduledDateTime != null)
                                        {
                                            Campaign SendcampaignDto = await _compaignDbContext.Campaigns.FirstAsync(c => c.CampaignTitle == model.CampaignTitle);
                                            SendcampaignDto.WhatsAppMessagesId = concatenatedMessageIds;
                                            SendcampaignDto.DateSetLive = retrievedEntity.CreatedAt;
                                            SendcampaignDto.State = CampaignState.Completed;
                                            try
                                            {
                                                _compaignDbContext.Campaigns.Update(SendcampaignDto);
                                                await _compaignDbContext.SaveChangesAsync();

                                            }
                                            catch (Exception ex)
                                            {
                                                throw new Exception($"Error occurred: {ex.Message}");
                                            }
                                        }
                                        else
                                        {
                                            // working in immidiate campaign 
                                            try
                                            {
                                                var existingCampaign = await _compaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.CampaignTitle == model.CampaignTitle);
                                                if (existingCampaign != null && flag == false)
                                                {
                                                    existingCampaign.WhatsAppMessagesId = concatenatedMessageIds;
                                                    _compaignDbContext.Campaigns.Update(existingCampaign);
                                                    await _compaignDbContext.SaveChangesAsync();
                                                }
                                                if (flag)
                                                {
                                                    var newCampaignDto = await CreateNewCampaignDtoAsync(model);
                                                    newCampaignDto.WhatsAppMessagesId = concatenatedMessageIds;
                                                    newCampaignDto.DateSetLive = retrievedEntity.CreatedAt;
                                                    _compaignDbContext.Campaigns.Update(newCampaignDto);
                                                    await _compaignDbContext.SaveChangesAsync();
                                                    flag = false;
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                throw new Exception($"An error occurred: {ex.Message}", ex);
                                            }
                                        }
                                    }

                                }
                            }
                            else
                            {
                                Campaign SendcampaignDto = new Campaign
                                {
                                    CampaignId = Guid.NewGuid(),
                                    CampaignTitle = model.CampaignTitle,
                                    BusinessId = model.BusinessId,
                                    UserId = (Guid)model.UserId,
                                    Createdby = user.Name,
                                    DateSetLive = DateTime.UtcNow,
                                    CreatedDate = DateTime.UtcNow,
                                    EditedDate = DateTime.UtcNow,
                                    State = CampaignState.Incompleted
                                };
                                try
                                {
                                    await _compaignDbContext.Campaigns.AddAsync(SendcampaignDto);
                                    await _compaignDbContext.SaveChangesAsync();
                                }
                                catch (Exception ex)
                                {
                                    throw new Exception($"An error occurred: {ex.Message}", ex);
                                }
                            }

                        }
                    }
                }

            }
            // Updating referenceId in conversation table
            await UpdateConversationReferenceIdsAsync(conversationIds, model.CampaignTitle);
            return response;

        }

        public async Task<bool> UpdateConversationReferenceIdsAsync(List<Guid> conversationIds, string title)
        {
            if (conversationIds.Count > 0)
            {
                var conversations = await _compaignDbContext.Conversations
                    .Where(x => conversationIds
                    .Contains(x.Id)).ToListAsync();
                var campaign = await _compaignDbContext.Campaigns.FirstOrDefaultAsync(x => x.CampaignTitle == title);
                if (campaign == null)
                    return false;

                conversations.ForEach(x => { x.ReferenceId = campaign.CampaignId.ToString(); x.MessageType = MessageType.Campaigns; });
                _compaignDbContext.Conversations.UpdateRange(conversations);
                await _compaignDbContext.SaveChangesAsync();
                return true;
            }
            return false;
        }
    }
}
























































