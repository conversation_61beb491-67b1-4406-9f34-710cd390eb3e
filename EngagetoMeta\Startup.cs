using EngagetoMeta.DatabaseContext;
using EngagetoMeta.Repositories;
using EngagetoMeta.Settings;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace EngagetoMeta
{
    public static class Startup
    {
        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // Register IHttpClientFactory
            services.AddHttpClient();
            // Bind configurations using options pattern
            services.Configure<MetaConfig>(configuration.GetSection("MetaConfig"));
            services.Configure<WebhookConfig>(configuration.GetSection("WebhookConfig"));
            services.Configure<WebhookConfig>(configuration.GetSection("ConnectionStrings"));

            services.AddSingleton<MetaConfig>(sp =>
                sp.GetRequiredService<IOptions<MetaConfig>>().Value);
            services.AddSingleton<WebhookConfig>(sp =>
                sp.GetRequiredService<IOptions<WebhookConfig>>().Value);
            services.AddSingleton<ConnectionStrings>(sp =>
                sp.GetRequiredService<IOptions<ConnectionStrings>>().Value);

            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(
                    configuration.GetConnectionString("Connection"),
                    sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure(
                            maxRetryCount: 5,

                            maxRetryDelay: TimeSpan.FromSeconds(10),
                            errorNumbersToAdd: null);
                    }
                ));

            services.AddScoped<ILogHistoryRepository, LogHistoryRepository>();

            // Add health checks
            services.AddHealthChecks();

            // Register other services
            services.AddControllers();
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen();
        }

        public static void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // Configure exception handling for production
            if (!env.IsDevelopment())
            {
                app.UseExceptionHandler("/error");
            }

            app.UseSwagger();
            app.UseSwaggerUI();

            // In Azure App Service, we should use HTTP
            if (!env.IsProduction())
            {
                app.UseHttpsRedirection();
            }

            app.UseRouting();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                // Map health check endpoint
                endpoints.MapHealthChecks("/health");
            });
        }
    }
}