﻿using Amazon.Runtime.Internal.Transform;
using Amazon.S3;
using Amazon.S3.Model;
using EngagetoContracts.Services;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using OfficeOpenXml;
using static System.Net.WebRequestMethods;

namespace EngagetoRepository.Services
{
    public class BlobStorageService : IBlobStorageService
    {
        private readonly string _bucketName;
        private readonly string _accessKey;
        private readonly string _secretKey;
        private readonly string _region;
        private readonly IAmazonS3 _s3Client;
        private readonly IUserIdentityService _userIdentityService;
        private readonly IGenericRepository _genericRepository;
        public readonly ApplicationDbContext _context;
        public BlobStorageService(ApplicationDbContext context, IUserIdentityService userIdentityService,
            IGenericRepository genericRepository,
            IConfiguration configuration)
        {
            _context = context;
            _userIdentityService = userIdentityService;
            _genericRepository = genericRepository;
            var awsSettings = configuration.GetSection("AWS");
            _bucketName = awsSettings.GetValue<string>("BucketName");
            _accessKey = awsSettings.GetValue<string>("AccessKey");
            _secretKey = awsSettings.GetValue<string>("SecretKey");
            _region = awsSettings.GetValue<string>("Region");

            _s3Client = new AmazonS3Client(_accessKey, _secretKey, Amazon.RegionEndpoint.GetBySystemName(_region));
        }

        public async Task<FileContentResult> DownloadAsync(string fileName)
        {
            var getRequest = new GetObjectRequest
            {
                BucketName = _bucketName,
                Key = fileName
            };

            try
            {
                using (var response = await _s3Client.GetObjectAsync(getRequest))
                using (var memoryStream = new MemoryStream())
                {
                    await response.ResponseStream.CopyToAsync(memoryStream);
                    var fileBytes = memoryStream.ToArray();

                    // Return the file with its content type and name
                    return new FileContentResult(fileBytes, response.Headers["Content-Type"])
                    {
                        FileDownloadName = fileName
                    };
                }
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                throw new FileNotFoundException($"File '{fileName}' not found in S3 bucket.", ex);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while downloading the file.", ex);
            }
        }


        public async Task<string> GetUrlAsync(string fileName, bool isExipre = false)
        {
            var request = new GetPreSignedUrlRequest
            {
                BucketName = _bucketName,
                Key = fileName,
            };
            if (isExipre)
            {
                request.Expires = DateTime.UtcNow.AddMinutes(30);
            }

            var url = await _s3Client.GetPreSignedURLAsync(request);
            return url;
        }

        public async Task<ViewUploadFileDto> UploadAsync(UploadFileDto dto, bool shouldUseOtherFileName = true)
        {
            if (dto.File == null || dto.File.Length == 0)
                throw new InvalidOperationException("File is not provided.");
            var file = dto.File;

            if (dto.ModuleType?.ToLower() == "campaigns" || dto.ModuleType?.ToLower() == "contacts")
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(file.OpenReadStream()))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    bool isValid = ExcelMapping.ValidateHeaders(worksheet);
                    if (!isValid)
                    {
                        throw new InvalidOperationException("Invalid Excel file header format.");
                    }
                }
            }


            (string name, string extension) = CommonHelper.GetExtensionOfFile(file.FileName);
            var key = file.FileName;

            if (shouldUseOtherFileName)
            {
                key = $"{name}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.{extension}";
            }

            using (var stream = file.OpenReadStream())
            {
                var uploadRequest = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key,
                    InputStream = stream,
                    ContentType = file.ContentType,
                    AutoCloseStream = true
                };
                var result = await _s3Client.PutObjectAsync(uploadRequest);

                var upload = new UploadedFile()
                {
                    FileName = file.FileName,
                    UploadedFileName = key,
                    BucketName = _bucketName,
                    ContentType = file.ContentType,
                    FilePath = $"https://{_bucketName}.s3.{_region}.amazonaws.com/{key}",
                    IsPublic = true,
                    BusinessId = _userIdentityService?.BusinessId,
                    UserId = _userIdentityService?.UserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = _userIdentityService?.UserId ?? Guid.Empty,
                    UpdatedBy = _userIdentityService?.UserId ?? Guid.Empty
                };
                await _context.AddAsync(upload);
                await _context.SaveChangesAsync();
                return new ViewUploadFileDto()
                {
                    Name = file.FileName,
                    Id = upload.Id,
                    Url = $"https://{_bucketName}.s3.{_region}.amazonaws.com/{key}",
                    UploadedFileName = key
                };
            }
        }

        public async Task<bool> UpdateFileAsync(string fileName, IFormFile file)
        {
            try
            {
                var contentType = CommonHelper.GetMimeFileType(file.FileName);
                using (var stream = file.OpenReadStream())
                {
                    // Define the PutObject request
                    var putRequest = new PutObjectRequest
                    {
                        BucketName = _bucketName,
                        Key = fileName,
                        InputStream = stream,
                        ContentType = contentType,
                        AutoCloseStream = true
                    };
                    var response = await _s3Client.PutObjectAsync(putRequest);
                    var uploadFile = (await _genericRepository.GetByObjectAsync<UploadedFile>(new Dictionary<string, object>() { { "UploadedFileName", fileName } }))?.LastOrDefault();
                    if (uploadFile != null)
                    {
                        uploadFile.FileName = file.FileName;
                        uploadFile.ContentType = contentType;
                        uploadFile.UpdatedAt = DateTime.UtcNow;
                        uploadFile.UpdatedBy = _userIdentityService?.UserId;
                    }
                    var result = await _genericRepository.FindAndUpdateAsync(
                    "UploadedFiles",
                    new Dictionary<string, object> { { "FileName", fileName }, { "UpdatedBy", _userIdentityService?.UserId ?? Guid.Empty }, { "UpdatedAt", DateTime.UtcNow } },
                    new() { { "Id", uploadFile.Id } });
                    return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
                }
            }
            catch (AmazonS3Exception ex)
            {
                // Handle S3-specific exceptions
                Console.WriteLine($"S3 Error: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                Console.WriteLine($"Error: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateFileAsync(string fileName, MemoryStream memoryStream, string contentType)
        {
            try
            {
                memoryStream.Position = 0;
                var putRequest = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileName,
                    InputStream = memoryStream,
                    ContentType = contentType,
                    AutoCloseStream = true
                };

                var response = await _s3Client.PutObjectAsync(putRequest);
                var uploadFile = (await _genericRepository.GetByObjectAsync<UploadedFile>(
                    new Dictionary<string, object> { { "UploadedFileName", fileName } }))
                    ?.LastOrDefault();

                if (uploadFile != null)
                {
                    // Preserve the original filename in the database
                    // We're only updating the content, not changing the filename
                    var result = await _genericRepository.FindAndUpdateAsync(
                        "UploadedFiles",
                        new Dictionary<string, object> {
                            {"UpdatedBy", _userIdentityService?.UserId ?? Guid.Empty },
                            {"UpdatedAt", DateTime.UtcNow }
                        },
                        new() { { "Id", uploadFile.Id } });
                }

                return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
            }
            catch (AmazonS3Exception ex)
            {
                Console.WriteLine($"S3 Error: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return false;
            }
        }

        public async Task<ViewUploadedFileDto> GetUploadedFile(int id)
        {
            try
            {
                var result = await _context.UploadedFiles.FirstOrDefaultAsync(i => i.Id == id);
                if (result == null)
                {
                    return null;
                }

                return new ViewUploadedFileDto()
                {
                    Id = result.Id,
                    FileName = result.FileName,
                    FilePath = result.FilePath,
                    UploadedFileName = result.UploadedFileName
                };
            }
            catch (Exception ex)
            {
                throw;
            }

        }
    }
}
