using EngagetoMeta;

var builder = WebApplication.CreateBuilder(args);

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();
builder.Logging.AddAzureWebAppDiagnostics();

// Configure services using Startup class
Startup.ConfigureServices(builder.Services, builder.Configuration);

var app = builder.Build();

// Log application startup
app.Logger.LogInformation("Starting Engageto.Meta application");

// Configure the application using Startup class
Startup.Configure(app, app.Environment);

app.Run();
