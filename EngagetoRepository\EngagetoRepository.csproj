﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Core" Version="3.7.304.15" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.309.1" />
    <PackageReference Include="BCrypt.Net" Version="0.1.0" />
    <PackageReference Include="cashfree_pg" Version="4.3.3" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="LibGit2Sharp" Version="0.30.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Formatters.Json" Version="2.1.18" />
    <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.1.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.10.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quartz" Version="3.9.0" />
    <PackageReference Include="Razorpay" Version="3.1.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Net.Http.Formatting.Extension" Version="5.2.3" />
    <PackageReference Include="System.Threading.Timer" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EngagetoBackGroundJobs\EngagetoBackGroundJobs.csproj" />
    <ProjectReference Include="..\EngagetoContracts\EngagetoContracts.csproj" />
    <ProjectReference Include="..\EngagetoDapper\EngagetoDapper.csproj" />
    <ProjectReference Include="..\EngagetoDatabase\EngagetoDatabase.csproj" />
    <ProjectReference Include="..\EngagetoEntities\EngagetoEntities.csproj" />
  </ItemGroup>

</Project>
