﻿using EngagetoContracts.MetaContracts;
using EngagetoContracts.OptinContracts;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.OptInManagementDto;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Text.Json;

namespace EngagetoRepository.OptInServices
{
    public class OptInRepository : IOptin
    {
        private readonly ApplicationDbContext _otpinDbContext;
        private readonly IConfiguration _configuration;
        private readonly IMetaApiService _metaApiService;

        public OptInRepository(ApplicationDbContext context, IConfiguration configuration, IMetaApiService metaApiService)
        {
            _otpinDbContext = context;
            _configuration = configuration;
            _metaApiService = metaApiService;
        }
        public async Task<string> AddKeyword(AddKeywordRequestDto model, string currentUserId, string currentUserNameClaim)
        {
            try
            {
                var optOutKeywords = model.OptOutKeyword?.Distinct().ToList() ?? new List<string>();
                if (optOutKeywords.Count > 5)
                {
                    throw new ArgumentException("Optout keyword list contain 5 items only.");
                }
                if (optOutKeywords.Any(keyword => keyword.Length > 25))
                {
                    throw new ArgumentException("Each optOut keyword must be 25 characters.");
                }
                var optInKeywords = model.OptInKeyword?.Distinct().ToList() ?? new List<string>();
                if (optInKeywords.Count > 5)
                {
                    throw new ArgumentException("Optin keyword list contain 5 items only.");
                }
                if (optInKeywords.Any(keyword => keyword.Length > 25))
                {
                    throw new ArgumentException("Each optIn keyword must be 25 characters.");
                }
                var guidId = model.Id != null && model.Id != Guid.Empty ? model.Id : Guid.NewGuid();
                string keywordsOutString = JsonSerializer.Serialize(optOutKeywords);
                string keywordsInString = JsonSerializer.Serialize(optInKeywords);

                var entity = _otpinDbContext.OptInManagement.Where(o => o.UserId.ToString() == currentUserId.ToString());

                if (entity != null && entity.Count() > 0)
                {
                    var entityOptInKeyword = entity.FirstOrDefault()?.OptInKeyword;
                    if (entityOptInKeyword != null)
                    {
                        var optInDeserialize = JsonSerializer.Deserialize<List<string>>(entityOptInKeyword);

                        // Check if any OptOutKeywords in the incoming model are already in the stored OptInKeywords
                        var conflictingOptOutKeywords = optOutKeywords.Intersect(optInDeserialize, StringComparer.OrdinalIgnoreCase).ToList();
                        if (conflictingOptOutKeywords.Any())
                        {
                            throw new InvalidOperationException($"Some OptOutKeywords {keywordsOutString} already exist in OptInKeywords {entityOptInKeyword}.");
                        }
                    }
                    var entityOptOutKeyword = entity.FirstOrDefault()?.OptOutKeyword;
                    if (entityOptOutKeyword != null)
                    {
                        var optOutDeserialize = JsonSerializer.Deserialize<List<string>>(entityOptOutKeyword);

                        // Check if any OptInKeywords in the incoming model are already in the stored OptOutKeywords
                        var conflictingOptInKeywords = optInKeywords.Intersect(optOutDeserialize, StringComparer.OrdinalIgnoreCase).ToList();
                        if (conflictingOptInKeywords.Any())
                        {
                            throw new InvalidOperationException($"Some OptInKeywords {keywordsInString} already exist in OptOutKeywords {entityOptOutKeyword}.");
                        }
                    }
                }
                string resultMessage = string.Empty;
                // work in OptOutKeywords
                if (model.OptOutKeyword != null && model.OptOutKeyword.Any(keyword => !string.IsNullOrWhiteSpace(keyword)))
                {
                    if (model.Id != null && model.Id != Guid.Empty)
                    {
                        var optKeywordEntity = await _otpinDbContext.OptInManagement.FindAsync(model.Id);
                        if (optKeywordEntity == null)
                        {
                            throw new Exception("Entity not found.");
                        }
                        optKeywordEntity.BusinessId = model.BusinessId;
                        optKeywordEntity.UserId = Guid.Parse(currentUserId);
                        optKeywordEntity.OptOutKeyword = keywordsOutString;
                        optKeywordEntity.OptOutMessageToggle = model.OptOutMessageToggle;
                        optKeywordEntity.TextOptOut = model.TextOptOut;
                        optKeywordEntity.UrlOptOut = model.UrlOptOut;
                        optKeywordEntity.CreatedBy = currentUserNameClaim;
                        optKeywordEntity.CreatedDate = DateTime.UtcNow;
                        resultMessage = "OptOut Keywords Updated successfully";
                    }
                    else
                    {
                        var optOutKeywordEntity = new OptInManagement
                        {
                            Id = (Guid)guidId,
                            BusinessId = model.BusinessId,
                            UserId = Guid.Parse(currentUserId),
                            OptOutKeyword = keywordsOutString,
                            OptOutMessageToggle = model.OptOutMessageToggle,
                            TextOptOut = model.TextOptOut,
                            UrlOptOut = model.UrlOptOut,
                            CreatedBy = currentUserNameClaim,
                            CreatedDate = DateTime.UtcNow
                        };
                        _otpinDbContext.OptInManagement.Add(optOutKeywordEntity);
                        resultMessage = "OptOut Keywords Added successfully";
                    }
                    await _otpinDbContext.SaveChangesAsync();
                }
                // work in OptInKeywords
                if (model.OptInKeyword != null && model.OptInKeyword.Any(keyword => !string.IsNullOrWhiteSpace(keyword)))
                {
                    if (model.Id != null && model.Id != Guid.Empty)
                    {
                        var optKeywordEntity = await _otpinDbContext.OptInManagement.FindAsync(model.Id);
                        if (optKeywordEntity == null)
                        {
                            throw new Exception("Entity not found.");
                        }
                        optKeywordEntity.BusinessId = model.BusinessId;
                        optKeywordEntity.UserId = Guid.Parse(currentUserId);
                        optKeywordEntity.OptInKeyword = keywordsInString;
                        optKeywordEntity.OptInMessageToggle = model.OptInMessageToggle;
                        optKeywordEntity.TextOptIn = model.TextOptIn;
                        optKeywordEntity.UrlOptIn = model.UrlOptIn;
                        optKeywordEntity.CreatedBy = currentUserNameClaim;
                        optKeywordEntity.CreatedDate = DateTime.UtcNow;
                        resultMessage = "OptIn Keywords Updated successfully";
                    }
                    else
                    {
                        var optOutKeywordEntity = new OptInManagement
                        {
                            Id = (Guid)guidId,
                            BusinessId = model.BusinessId,
                            UserId = Guid.Parse(currentUserId),
                            OptInKeyword = keywordsInString,
                            OptInMessageToggle = model.OptInMessageToggle,
                            TextOptIn = model.TextOptIn,
                            UrlOptIn = model.UrlOptIn,
                            CreatedBy = currentUserNameClaim,
                            CreatedDate = DateTime.UtcNow
                        };
                        _otpinDbContext.OptInManagement.Add(optOutKeywordEntity);
                        resultMessage = "OptIn Keywords Added successfully";
                    }
                    await _otpinDbContext.SaveChangesAsync();
                }

                return resultMessage;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        //get by Id in OptOut
        public async Task<AddKeywordRequestDto> GetOptOutKeywordsByIdAsync(string BusinessId, string currentUserId)
        {
            try
            {
                var entity = await _otpinDbContext.OptInManagement.Where(o => o.BusinessId == BusinessId).FirstOrDefaultAsync();
                var optOutKeywords = entity?.OptOutKeyword == null ? null : JsonSerializer.Deserialize<List<string>>(entity.OptOutKeyword);
                return new AddKeywordRequestDto
                {
                    Id = entity?.Id,
                    BusinessId = BusinessId,
                    UserId = Guid.Parse(currentUserId),
                    OptOutKeyword = optOutKeywords ?? new List<string>(),
                    OptOutMessageToggle = entity?.OptOutMessageToggle,
                    TextOptOut = entity?.TextOptOut,
                    UrlOptOut = entity?.UrlOptOut
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        //get by Id in OptIn
        public async Task<AddKeywordRequestDto> GetOptInKeywordsByIdAsync(String BusinessId, String currentUserId)
        {
            try
            {
                var entity = await _otpinDbContext.OptInManagement.Where(o => o.BusinessId == BusinessId).FirstOrDefaultAsync();
                var optInKeywords = entity?.OptInKeyword == null ? null : JsonSerializer.Deserialize<List<string>>(entity.OptInKeyword);
                return new AddKeywordRequestDto
                {
                    Id = entity?.Id,
                    BusinessId = BusinessId,
                    UserId = Guid.Parse(currentUserId),
                    OptInKeyword = optInKeywords ?? new List<string>(),
                    OptInMessageToggle = entity?.OptInMessageToggle,
                    TextOptIn = entity?.TextOptIn,
                    UrlOptIn = entity?.UrlOptIn
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        // for Delete by id
        public async Task<bool> RemoveKeywordAsync(Guid id, string businessId, string keywordToRemove)
        {
            var entity = await _otpinDbContext.OptInManagement.Where(o => o.Id == id).FirstOrDefaultAsync();
            if (entity == null)
            {
                return false;
            }
            bool isUpdated = false;

            // Process Opt-out Keywords
            if (entity.OptOutKeyword != null)
            {
                var optOutKeywords = JsonSerializer.Deserialize<List<string>>(entity.OptOutKeyword) ?? new List<string>();
                if (optOutKeywords.Remove(keywordToRemove))
                {
                    entity.OptOutKeyword = optOutKeywords.Any() ? JsonSerializer.Serialize(optOutKeywords) : null;
                    isUpdated = true;
                }
            }
            // Process Opt-in Keywords
            if (entity.OptInKeyword != null)
            {
                var optInKeywords = JsonSerializer.Deserialize<List<string>>(entity.OptInKeyword) ?? new List<string>();
                if (optInKeywords.Remove(keywordToRemove))
                {
                    entity.OptInKeyword = optInKeywords.Any() ? JsonSerializer.Serialize(optInKeywords) : null;
                    isUpdated = true;

                }
            }
            if (entity?.OptInKeyword == null && entity?.OptOutKeyword == null)
            {
                _otpinDbContext.OptInManagement.Remove(entity);
                await _otpinDbContext.SaveChangesAsync();
                return true;
            }
            if (isUpdated)
            {
                _otpinDbContext.OptInManagement.Update(entity);
                await _otpinDbContext.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<bool> OptInAndOutKeywordProcessAsync(WAWebhookDto wAWebhook, string businessId)
        {
            try
            {
                var messages = wAWebhook?.Entry?[0]?.Changes?[0]?.Value?.Messages?.FirstOrDefault();
                var textMessage = messages?.Type == "text" ? messages?.Text?.Body?.ToLower() : messages?.Button?.Text?.ToLower();
                var optKeyWords = await _otpinDbContext.OptInManagement
                    .Where(x => x.BusinessId == businessId
                        && ((x.OptInMessageToggle ?? false) == true
                            || (x.OptOutMessageToggle ?? false) == true))
                    .ToListAsync();
                //contact details
                var contact = await _otpinDbContext.Contacts.FirstOrDefaultAsync(m => (m.CountryCode + m.Contact) == ("+" + messages.From) && m.BusinessId.ToString().ToLower() == businessId.ToLower() && m.IsActive);
                
                if (optKeyWords?.Any() == true && contact != null)
                {
                    var optOutKeyWords = JsonSerializer.Deserialize<List<string>>(optKeyWords.FirstOrDefault()?.OptOutKeyword ?? "[]")
                        ?.Select(keyword => keyword.ToLower()).ToList();
                    
                    var optInKeywords = JsonSerializer.Deserialize<List<string>>(optKeyWords.FirstOrDefault()?.OptInKeyword ?? "[]")?.Select(keyword => keyword.ToLower()).ToList();
                    
                    var OptOutMessageToggles = optKeyWords?.FirstOrDefault()?.OptOutMessageToggle == true;
                    var OptInMessageToggles = optKeyWords?.FirstOrDefault()?.OptOutMessageToggle == true;

                    var conversation = new Conversations()
                    {
                        Id = Guid.NewGuid(),
                        From = businessId,
                        To = messages.From,
                        CreatedAt = DateTime.UtcNow,
                        Status = EngagetoEntities.Enums.ConvStatus.sent,
                        MessageType = MessageType.AutoReply
                    };
                    // OptOut processing
                    if (optOutKeyWords?.Any() == true && OptOutMessageToggles && optOutKeyWords.Contains(textMessage ?? string.Empty))
                    {
                        var optOutTextMessage = optKeyWords?.FirstOrDefault(x => (x.OptOutMessageToggle ?? false) == true)?.TextOptOut;
                        var url = optKeyWords?.FirstOrDefault(x => (x.OptOutMessageToggle ?? false) == true)?.UrlOptOut;
                        var mediaType = !string.IsNullOrEmpty(url) ? "image" : "text";
                        var response = await _metaApiService.SendTextWithMediaMessageAsync(businessId, messages.From, optOutTextMessage, mediaType, url, null);
                        if (response.IsSuccess) 
                        {
                            var result = response.Result.ToObject<WhatsAppResponse>();
                            conversation.WhatsAppMessageId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty;
                            conversation.TextMessage = optOutTextMessage;
                            conversation.MediaUrl = url;
                            await _otpinDbContext.Conversations.AddAsync(conversation);
                            contact.IsOptIn = Is_OptIn.optout;
                            _otpinDbContext.Contacts.Update(contact);
                            await _otpinDbContext.SaveChangesAsync();
                            return true;
                        }
                    }
                    // OptIn processing
                    if(optInKeywords?.Any() == true && OptInMessageToggles && optInKeywords.Contains(textMessage ?? string.Empty))
                    {
                        var optInTextMessage = optKeyWords?.FirstOrDefault(x => (x.OptInMessageToggle ?? false) == true)?.TextOptOut;
                        var url = optKeyWords?.FirstOrDefault(x => (x.OptOutMessageToggle ?? false) == true)?.UrlOptOut;
                        var mediaType = !string.IsNullOrEmpty(url) ? "image" : "text";
                        var response = await _metaApiService.SendTextWithMediaMessageAsync(businessId, messages.From, optInTextMessage, mediaType, url, null);
                        if (response.IsSuccess)
                        {
                            var result = response.Result.ToObject<WhatsAppResponse>();
                            conversation.WhatsAppMessageId = result?.Messages?.FirstOrDefault()?.Id ?? string.Empty;
                            conversation.TextMessage = optInTextMessage;
                            conversation.MediaUrl = url;
                            await _otpinDbContext.Conversations.AddAsync(conversation);
                            contact.IsOptIn = Is_OptIn.optin;
                            _otpinDbContext.Contacts.Update(contact);
                            await _otpinDbContext.SaveChangesAsync();
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
