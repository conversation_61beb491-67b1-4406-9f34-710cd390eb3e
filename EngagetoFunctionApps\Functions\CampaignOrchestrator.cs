﻿using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;

namespace EngagetoFunctionApps.Functions
{
    public class CampaignOrchestrator
    {
        private readonly ILogger<CampaignScheduler> _logger;
        private readonly ICampaignScheduler _campaignScheduler;
        private readonly EngagetoDapper.Data.Interfaces.GenericInterfaces.IGenericRepository _genericRepository;

        public CampaignOrchestrator(
            ILogger<CampaignScheduler> logger,
            ICampaignScheduler campaignScheduler,
            EngagetoDapper.Data.Interfaces.GenericInterfaces.IGenericRepository genericRepository)
        {
            _logger = logger;
            _campaignScheduler = campaignScheduler;
            _genericRepository = genericRepository;
        }

        [Function(nameof(RunCampaignOrchestrator))]
        public async Task RunCampaignOrchestrator(
        [OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<InputPayload>();
            try
            {
                var campaignData = input.CampaignJsonData;
                var campaign = campaignData.Adapt<Campaign>();
                campaign.CampaignId = input.CampaignId;
                var audience = input.Audiances;
                bool isDevelopment = input.IsDevelopment;

                InputPayload activityInput = new(
                    campaign.CampaignId,
                    campaignData,
                    DateTime.UtcNow,
                    audience,
                    isDevelopment
                );

                await context.CallActivityAsync(nameof(PartitionCampaignActivity), activityInput);
            }
            catch (Exception ex)
            {
                context.SetCustomStatus($"Error: {ex.Message}");
                throw;
            }
        }


        [Function(nameof(PartitionCampaignActivity))]
        public async Task PartitionCampaignActivity(
         [ActivityTrigger] InputPayload input)
        {
            input.CampaignJsonData.CampaignId = input.CampaignId;
            input.CampaignJsonData.DateSetLive = input.ScheduledTime;
            await _campaignScheduler.PartitionCampaignBatchesAsync(input.CampaignJsonData, input.Audiances, input.IsDevelopment);
        }


        [Function(nameof(ProcessCampaignSubBatches))]
        public async Task ProcessCampaignSubBatches([OrchestrationTrigger] TaskOrchestrationContext context)
        {
            var input = context.GetInput<InputPayload>();
            try
            {
                if (input.ScheduledTime > context.CurrentUtcDateTime)
                {
                    _logger.LogInformation($"Waiting until scheduled time: {input.ScheduledTime}");
                    await context.CreateTimer(input.ScheduledTime, CancellationToken.None);
                }
                await context.CallActivityAsync(nameof(StartCampaignProcessing), input);
                await context.CallActivityAsync(nameof(CompleteCampaign), input.CampaignId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing campaign {input.CampaignId}");
                await context.CallActivityAsync(nameof(FailCampaign), input.CampaignId);
                throw;
            }
        }


        [Function(nameof(StartCampaignProcessing))]
        public async Task StartCampaignProcessing([ActivityTrigger] InputPayload input)
        {
            try
            {
                var campaignData = input.CampaignJsonData;
                var campaign = campaignData.Adapt<Campaign>();
                campaign.CampaignId = input.CampaignId;
                var audiance = input.Audiances;
                await _campaignScheduler.ProcessCampaignAsync(campaign, audiance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing campaign {input.CampaignId}. JsonData: {input.CampaignJsonData}");
                throw;
            }
        }


        [Function(nameof(CompleteCampaign))]
        public async Task CompleteCampaign([ActivityTrigger] Guid campaignId)
        {
            var campaign = await GetCampaignData(campaignId);
            if (campaign != null)
            {
                campaign.State = CampaignState.Completed;
                await _genericRepository.UpdateRecordAsync(
                    "Campaigns",
                    new List<string> { "State" },
                    campaign,
                    new Dictionary<string, object> { { "CampaignId", campaignId } });
            }
        }

        [Function(nameof(FailCampaign))]
        public async Task FailCampaign([ActivityTrigger] Guid campaignId)
        {
            var campaign = await GetCampaignData(campaignId);
            if (campaign != null)
            {
                campaign.State = CampaignState.Incompleted;
                await _genericRepository.UpdateRecordAsync(
                    "Campaigns",
                    new List<string> { "State" },
                    campaign,
                    new Dictionary<string, object> { { "CampaignId", campaignId } });
            }
        }
        private async Task<Campaign> GetCampaignData(Guid campaignId)
        {
            var campaign = await _genericRepository.GetByObjectAsync<Campaign>(
                new Dictionary<string, object> { { "CampaignId", campaignId } },
                "Campaigns");

            return campaign.FirstOrDefault() ?? new();
        }
    }

}
