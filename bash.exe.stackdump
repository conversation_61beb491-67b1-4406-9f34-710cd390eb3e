Stack trace:
Frame         Function      Args
0007FFFFABC0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9AC0) msys-2.0.dll+0x1FEBA
0007FFFFABC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE98) msys-2.0.dll+0x67F9
0007FFFFABC0  000210046832 (000210285FF9, 0007FFFFAA78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABC0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFABC0  0002100690B4 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAEA0  00021006A49D (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE62E50000 ntdll.dll
7FFE62890000 KERNEL32.DLL
7FFE5FF40000 KERNELBASE.dll
7FFE62540000 USER32.dll
7FFE60A10000 win32u.dll
7FFE62C40000 GDI32.dll
7FFE60720000 gdi32full.dll
7FFE60A40000 msvcp_win.dll
7FFE60320000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE60F60000 advapi32.dll
7FFE62A10000 msvcrt.dll
7FFE62960000 sechost.dll
7FFE605B0000 bcrypt.dll
7FFE60E40000 RPCRT4.dll
7FFE5F650000 CRYPTBASE.DLL
7FFE60850000 bcryptPrimitives.dll
7FFE60AE0000 IMM32.DLL
