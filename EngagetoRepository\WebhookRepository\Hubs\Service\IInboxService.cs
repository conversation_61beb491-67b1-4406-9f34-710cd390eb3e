﻿using EngagetoEntities;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.WebhookDtos;
namespace EngagetoRepository.WebhookRepository.Hubs.Service
{
    public interface IInboxService
    {
        Task<Object> InboxContacts(Guid businessId, Guid userId, FilterDto? operations, int? Page = 1);
        Task<object> InboxContactsConversations(Guid BusinessId, string Contact, int? Page = 1);
        Task SendMediaMessage(MediaMessageDto data, Guid BusinessId, string? SentBy = null);
        Task SendTextOrEmojiMessage(TextMessageDto data, Guid BusinessId, string? SentBy = null);
        Task SendTemplateByUsingContact(SendTemplate model, string? SentBy = null);
        Task<Object> LatestInboxContacts(Guid businessId, string userId,FilterDto Operations, int page);
        Task<List<InboxContactDto>> GetInboxContactsAsync(Guid businessId, Guid userId, FilterDto? oprations);
        Task<Object> GetInboxConversations(Guid businessId, string contact);
        Task<bool> BookDemoAsync(string name, string email, DateTime scheduledTime);
    }
}
