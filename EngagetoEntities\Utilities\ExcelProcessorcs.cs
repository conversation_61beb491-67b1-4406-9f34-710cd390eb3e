﻿using OfficeOpenXml;
using Quartz.Util;

namespace EngagetoEntities.Utilities
{
    public class ExcelProcessorcs
    {
        public Dictionary<string, List<string>> ColumnMapping { get; set; }

        public ExcelProcessorcs(Dictionary<string, List<string>> columnMapping)
        {
            this.ColumnMapping = columnMapping;
        }
        // Example Column Mapping
        public async Task<List<T>> ProcessExcel<T>(string filePath) where T : new()
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                var result = new List<T>();
                using (var httpClient = new HttpClient())
                using (var response = await httpClient.GetAsync(filePath))
                {
                    response.EnsureSuccessStatusCode();

                    using (var stream = await response.Content.ReadAsStreamAsync())
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[0];
                        var columnMapping = MapExcelColumnsToModel(worksheet);
                        var properties = typeof(T).GetProperties();

                        for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                        {
                            var instance = new T();
                            bool shouldSkipRow = false; // Flag to track if we should skip this row

                            foreach (var columnDetail in columnMapping)
                            {
                                var property = properties.FirstOrDefault(x => x.Name == columnDetail.Key);
                                if (property == null) continue;

                                if (columnMapping.TryGetValue(property.Name, out int colIndex))
                                {
                                    var cellValue = GetCellValue(worksheet, row, colIndex);

                                    // If 'Name' or 'Contact' column is empty, mark row to be skipped
                                    if ((property.Name == "Contact") && string.IsNullOrWhiteSpace(cellValue))
                                    {
                                        shouldSkipRow = true;
                                        break; // Stop processing this row immediately
                                    }
                                    if (!string.IsNullOrEmpty(cellValue))
                                    {
                                        var convertedValue = ConvertToPropertyType(cellValue, property.PropertyType);
                                        if (convertedValue != null && property.CanWrite)
                                        {
                                            property.SetValue(instance, convertedValue);
                                        }
                                    }
                                }
                            }
                            // Only add to result if row is valid
                            if (!shouldSkipRow)
                            {
                                result.Add(instance);
                            }
                        }
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        private Dictionary<string, int> MapExcelColumnsToModel(ExcelWorksheet worksheet)
        {
            var columnMapping = new Dictionary<string, int>();

            for (int col = 1; col <= worksheet.Dimension.End.Column; col++)
            {
                var columnName = worksheet.Cells[1, col].Text; // Header row value

                foreach (var map in ColumnMapping)
                {
                    if (map.Value.Any(alias => alias.Equals(columnName, StringComparison.OrdinalIgnoreCase)))
                    {
                        columnMapping[map.Key] = col; // Map property name to Excel column index
                        break;
                    }
                }
            }
            return columnMapping;
        }

        private string? GetCellValue(ExcelWorksheet worksheet, int row, int columnIndex)
        {
            return worksheet.Cells[row, columnIndex].Text?.Trim();
        }

        private object? ConvertToPropertyType(string cellValue, Type propertyType)
        {
            if (string.IsNullOrWhiteSpace(cellValue))
            {
                return null;  // Return null for empty values
            }

            try
            {
                // If it's a basic type, convert accordingly
                if (propertyType == typeof(int))
                {
                    return int.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(decimal))
                {
                    return decimal.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(DateTime))
                {
                    return DateTime.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(bool))
                {
                    return bool.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(string))
                {
                    return cellValue;  // Return the value as is for string
                }
                else if (propertyType.IsEnum)  // If the property is an enum, try to parse it
                {
                    return Enum.TryParse(propertyType, cellValue, true, out var enumValue) ? enumValue : null;
                }
                else if (propertyType == typeof(Guid))
                {
                    return Guid.TryParse(cellValue, out var result) ? result : default;
                }
                else
                {
                    // For custom types or other cases, fallback to Convert.ChangeType
                    return Convert.ChangeType(cellValue, propertyType);
                }
            }
            catch (Exception)
            {
                // Handle conversion failure gracefully
                return null;
            }
        }
    }
}
