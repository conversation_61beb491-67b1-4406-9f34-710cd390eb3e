﻿using Microsoft.AspNetCore.Http;

namespace EngagetoEntities.Dtos.CommonDtos
{
    public class UploadFileDto
    {
        public IFormFile File { get; set; }
        public string? ModuleType{ get; set; }
    }
    public class ViewUploadFileDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
        public string UploadedFileName { get; set; }
    }
    public class ViewUploadedFileDto
    {
        public int Id { get; set;}
        public string FileName { get; set;}
        public string  FilePath { get; set;}
        public  string UploadedFileName { get; set;}

    }


}
