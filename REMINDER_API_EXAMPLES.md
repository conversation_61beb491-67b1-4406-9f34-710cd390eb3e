# 🔧 Reminder System API Examples

## Setup Reminder Configurations

### 1. Bulk Setup (Recommended for Initial Setup)
```http
POST /api/reminder/configurations/bulk
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "businessId": "12345678-1234-1234-1234-123456789012",
  "fifteenMinuteTemplateId": "template-guid-15min",
  "thirtyMinuteTemplateId": "template-guid-30min",
  "oneDayTemplateId": "template-guid-1day",
  "enableFifteenMinute": true,
  "enableThirtyMinute": true,
  "enableOneDay": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully configured 3 reminder types",
  "data": [
    {
      "id": "config-guid-1",
      "businessId": "12345678-1234-1234-1234-123456789012",
      "reminderType": "15_MINUTES",
      "templateId": "template-guid-15min",
      "templateName": "appointment_reminder_15min",
      "minutesBeforeSchedule": 15,
      "isEnabled": true,
      "priority": 3,
      "createdAt": "2025-01-27T10:00:00Z"
    }
    // ... other configurations
  ]
}
```

### 2. Individual Configuration Setup
```http
POST /api/reminder/configurations
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "businessId": "12345678-1234-1234-1234-123456789012",
  "reminderType": "2_HOUR",
  "templateId": "template-guid-2hour",
  "minutesBeforeSchedule": 120,
  "isEnabled": true,
  "priority": 2
}
```

### 3. Custom Reminder Types
```http
POST /api/reminder/configurations
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "businessId": "12345678-1234-1234-1234-123456789012",
  "reminderType": "WEEKLY_REMINDER",
  "templateId": "template-guid-weekly",
  "minutesBeforeSchedule": 10080,
  "isEnabled": true,
  "priority": 1
}
```

## View and Manage Configurations

### 1. Get All Configurations for Business
```http
GET /api/reminder/configurations/12345678-1234-1234-1234-123456789012
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "success": true,
  "message": "Reminder configurations retrieved successfully",
  "data": [
    {
      "id": "config-guid-1",
      "businessId": "12345678-1234-1234-1234-123456789012",
      "reminderType": "1_DAY",
      "templateId": "template-guid-1day",
      "templateName": "appointment_reminder_1day",
      "minutesBeforeSchedule": 1440,
      "isEnabled": true,
      "priority": 1,
      "createdAt": "2025-01-27T10:00:00Z",
      "updatedAt": null
    }
  ]
}
```

### 2. Get Reminder History for Contact
```http
GET /api/reminder/history/contact-guid-12345
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "success": true,
  "message": "Reminder history retrieved successfully",
  "data": [
    {
      "id": "reminder-guid-1",
      "contactId": "contact-guid-12345",
      "contactName": "John Doe",
      "contactPhone": "+************",
      "scheduledDate": "2025-06-12T10:00:00Z",
      "reminderType": "1_DAY",
      "templateName": "appointment_reminder_1day",
      "sentAt": "2025-06-11T10:00:00Z",
      "status": "SENT",
      "whatsAppMessageId": "wamid.12345",
      "errorMessage": null
    }
  ]
}
```

## Testing and Debugging

### 1. Manual Trigger (for Testing)
```http
POST /api/reminder/process/12345678-1234-1234-1234-123456789012
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "success": true,
  "message": "Processed 3 reminders for business 12345678-1234-1234-1234-123456789012",
  "data": {
    "processedCount": 3,
    "businessId": "12345678-1234-1234-1234-123456789012"
  }
}
```

### 2. Check Pending Reminders
```http
GET /api/reminder/pending/12345678-1234-1234-1234-123456789012/1_DAY
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "success": true,
  "message": "Found 2 contacts needing 1_DAY reminders",
  "data": {
    "contacts": [
      {
        "contactId": "contact-guid-1",
        "name": "John Doe",
        "phone": "+************",
        "scheduledAt": "2025-06-12T10:00:00Z",
        "minutesUntilScheduled": 1440
      }
    ],
    "count": 2
  }
}
```

## Common Configuration Examples

### Medical Clinic Setup
```http
POST /api/reminder/configurations/bulk
{
  "businessId": "clinic-business-id",
  "oneDayTemplateId": "clinic_reminder_1day",     // "Appointment tomorrow at {{2}}"
  "thirtyMinuteTemplateId": "clinic_reminder_30min", // "Appointment in 30 minutes"
  "enableOneDay": true,
  "enableThirtyMinute": true,
  "enableFifteenMinute": false
}
```

### Salon/Spa Setup
```http
POST /api/reminder/configurations
{
  "businessId": "salon-business-id",
  "reminderType": "2_HOUR",
  "templateId": "salon_prep_reminder",
  "minutesBeforeSchedule": 120,
  "isEnabled": true
}
```

### Consultation/Meeting Setup
```http
POST /api/reminder/configurations
{
  "businessId": "consulting-business-id",
  "reminderType": "1_WEEK",
  "templateId": "meeting_prep_template",
  "minutesBeforeSchedule": 10080,
  "isEnabled": true
}
```

## Error Responses

### Invalid Business ID
```json
{
  "success": false,
  "message": "Error creating reminder configuration",
  "errors": "Business not found"
}
```

### Invalid Template ID
```json
{
  "success": false,
  "message": "Error creating reminder configuration", 
  "errors": "Template not found or not approved"
}
```

### Missing Configuration
```json
{
  "success": false,
  "message": "No reminder configurations found",
  "errors": "Please configure reminder templates first"
}
```

## Integration with Workflow

### Contact with Scheduled Date
```csharp
// When creating/updating contact
var contact = new Contacts
{
    ContactId = Guid.NewGuid(),
    Name = "John Doe",
    Contact = "9876543210",
    CountryCode = "+91",
    ScheduledAt = DateTime.Parse("2025-06-12 10:00:00"), // This triggers reminders
    BusinessId = Guid.Parse("12345678-1234-1234-1234-123456789012")
};
```

### Workflow Template Node Processing
```
1. Template node processes contact
2. System detects ScheduledAt date
3. Looks up reminder configurations for business
4. Sets up reminders for background processing
5. Background service sends reminders at configured times
```

## Best Practices

### 1. Template Naming Convention
```
{business_type}_reminder_{interval}
Examples:
- clinic_reminder_1day
- salon_reminder_2hour
- meeting_reminder_30min
```

### 2. Progressive Reminder Strategy
```
1 Week Before  → Confirmation + Preparation
1 Day Before   → Final confirmation + Details  
2 Hours Before → Directions + Contact info
30 Min Before  → Arrival reminder
15 Min Before  → Last check-in
```

### 3. Testing Workflow
```
1. Create test templates
2. Configure reminders with short intervals (5-10 minutes)
3. Create test contact with near-future ScheduledAt
4. Process through workflow
5. Monitor logs and verify delivery
6. Update to production intervals
```

This API provides complete control over when and how reminders are sent, allowing you to customize the experience for your specific business needs.
