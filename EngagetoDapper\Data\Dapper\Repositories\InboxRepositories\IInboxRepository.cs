﻿using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;

namespace EngagetoDapper.Data.Dapper.Repositories.InboxRepositories
{
    public interface IInboxRepository
    {
        Task<IEnumerable<Conversations>> GetConversationAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<IEnumerable<EngagetoEntities.Entities.Contacts>> GetContactsAsync(string companyId, CancellationToken cancellationToken);
        Task<IEnumerable<ChatStatusEntityDto>> GetChatStatusAsync(Guid businessId);
        Task<int> UpdateOpenChatStatusAsync(Guid businessId);
        Task<IEnumerable<RespondedCountDto>> GetRespondedCountAsync(string companyId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<IEnumerable<ChatStatusEntityDto>> GetAllChatStatusAsync(string companyId, CancellationToken cancellationToken, bool? IsAgent = null);
        Task<IEnumerable<InboxAnalyticsTotalMessagsDto>> GetInboxTotalMessageCountAsync(List<EngagetoEntities.Entities.Contacts> contacts, string companyId,
            DateTime? fromDate,
            DateTime? toDate,
            CancellationToken cancellationToken);
        Task<IEnumerable<TopUserPerformanceDto>> GetUserPerformanceAsync(List<EngagetoEntities.Entities.Contacts> contacts, string companyId,
            DateTime? fromDate,
            DateTime? toDate,
            CancellationToken cancellationToken);
        Task<IEnumerable<ContactInfoDto>> GetContactInfoAsync(Guid businessId,
             DateTime? fromDate,
             DateTime? toDate,
             CancellationToken cancellationToken);
        Task<IEnumerable<AgentAvgResponseTimeDto>> GetAgentAvgResponseTime(string businessId, DateTime date, CancellationToken cancellationToken);
        Task<IEnumerable<T>> GetContactAssignmentsAsync<T>(Guid? companyId, Guid? contactId);
        Task<IEnumerable<T>> GetContactAssignmentHistoriesAsync<T>(Guid? companyId, Guid? contactId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<bool> CheckConversationStatus(string companyId, string phoneNumber, CancellationToken cancellationToken);
        Task<IEnumerable<ContactInfoDto>> GetContactDetailsByIds(string businessId, List<Guid> contactIds, CancellationToken cancellationToken);
        Task<Dictionary<Guid, IEnumerable<CampaignAnalytsicDto>>> GetCampaignAnalyticsCountAsync(string businessId, Dictionary<Guid, List<string>> values, CancellationToken cancellationToken);
        Task<bool> UpdateContactForWorkflowAsync(string businessId, List<Guid> contactIds, string workflowName, int step, CancellationToken cancellationToken);
        Task<TenantCCostAnalyticsDto?> GetCConversationCostAnalyticsAsync(string businessId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<TemplateAnalyticsDto?> GetTemplateAnalyticsAsync(string businessId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<int> SendTemplateCountAsync(string businessId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken);
        Task<WATemplateCategory> GetTemplateCategoryAsync(string referenceId);
        Task<bool> SaveConversationCostAsync(ConversationAnalyticsEntity conversation);

        Task<IEnumerable<Conversations>> GetLatestConversationsAsync(Guid businessId, CancellationToken cancellationToken);
        Task<IEnumerable<Contacts>> GetAllContactsAsync(Guid businessId, string userId, CancellationToken cancellationToken);
        Task<List<EngagetoEntities.Entities.Conversations>> GetLatestConversationsForContactsAsync(List<Contacts> contacts, CancellationToken cancellationToken);
        Task<IEnumerable<EngagetoEntities.Entities.Contacts>> GetPaginatedContactsAsync(int page, int pageSize, Guid businessId, CancellationToken cancellationToken);
        Task<CampaignAnalytsicDto> GetCampaignAnalyticsCountAsyncById(string businessId, Guid CampaignId, CancellationToken cancellationToken);
        Task<List<CampaignReportsDto>> GetCampaignReportByIdAsync(string businessId, Guid CampaignId, CancellationToken cancellationToken);


    }
}


