using EngagetoContracts.Workflow;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Engageto.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WorkflowJobCleanupController : ControllerBase
    {
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;
        private readonly ILogger<WorkflowJobCleanupController> _logger;

        public WorkflowJobCleanupController(
            INodeWorkflowEngineService nodeWorkflowEngineService,
            ILogger<WorkflowJobCleanupController> logger)
        {
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
            _logger = logger;
        }

        /// <summary>
        /// Clean up scheduled jobs for a specific workflow node
        /// Call this when a node is edited, time changed, or deleted
        /// </summary>
        /// <param name="nodeId">The workflow node ID</param>
        /// <returns>Number of jobs cleaned up</returns>
        [HttpPost("cleanup-node/{nodeId}")]
        public async Task<IActionResult> CleanupNodeJobs(Guid nodeId)
        {
            try
            {
                _logger.LogInformation("API request to cleanup jobs for node {NodeId}", nodeId);

                var cleanedCount = await _nodeWorkflowEngineService.CleanupScheduledJobsForNode(nodeId);

                return Ok(new
                {
                    success = true,
                    message = $"Successfully cleaned up {cleanedCount} scheduled jobs for node {nodeId}",
                    cleanedJobsCount = cleanedCount,
                    nodeId = nodeId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up jobs for node {NodeId}", nodeId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "Error cleaning up scheduled jobs",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Clean up scheduled jobs for an entire workflow
        /// Call this when a workflow is deleted
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <returns>Number of jobs cleaned up</returns>
        [HttpPost("cleanup-workflow/{workflowId}")]
        public async Task<IActionResult> CleanupWorkflowJobs(Guid workflowId)
        {
            try
            {
                _logger.LogInformation("API request to cleanup jobs for workflow {WorkflowId}", workflowId);

                var cleanedCount = await _nodeWorkflowEngineService.CleanupScheduledJobsForWorkflow(workflowId);

                return Ok(new
                {
                    success = true,
                    message = $"Successfully cleaned up {cleanedCount} scheduled jobs for workflow {workflowId}",
                    cleanedJobsCount = cleanedCount,
                    workflowId = workflowId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up jobs for workflow {WorkflowId}", workflowId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "Error cleaning up scheduled jobs",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Clean up scheduled jobs for multiple nodes at once
        /// Useful when updating multiple nodes in a workflow
        /// </summary>
        /// <param name="request">List of node IDs to clean up</param>
        /// <returns>Summary of cleanup results</returns>
        [HttpPost("cleanup-multiple-nodes")]
        public async Task<IActionResult> CleanupMultipleNodeJobs([FromBody] CleanupMultipleNodesRequest request)
        {
            try
            {
                _logger.LogInformation("API request to cleanup jobs for {Count} nodes", request.NodeIds?.Count ?? 0);

                if (request.NodeIds == null || !request.NodeIds.Any())
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "No node IDs provided"
                    });
                }

                var results = new List<object>();
                int totalCleaned = 0;

                foreach (var nodeId in request.NodeIds)
                {
                    try
                    {
                        var cleanedCount = await _nodeWorkflowEngineService.CleanupScheduledJobsForNode(nodeId);
                        totalCleaned += cleanedCount;
                        
                        results.Add(new
                        {
                            nodeId = nodeId,
                            cleanedJobsCount = cleanedCount,
                            success = true
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error cleaning up jobs for node {NodeId}", nodeId);
                        results.Add(new
                        {
                            nodeId = nodeId,
                            cleanedJobsCount = 0,
                            success = false,
                            error = ex.Message
                        });
                    }
                }

                return Ok(new
                {
                    success = true,
                    message = $"Processed {request.NodeIds.Count} nodes, cleaned up {totalCleaned} total jobs",
                    totalCleanedJobs = totalCleaned,
                    results = results
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in cleanup multiple nodes operation");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Error cleaning up scheduled jobs",
                    error = ex.Message
                });
            }
        }
    }

    /// <summary>
    /// Request model for cleaning up multiple nodes
    /// </summary>
    public class CleanupMultipleNodesRequest
    {
        public List<Guid> NodeIds { get; set; } = new List<Guid>();
    }
}
