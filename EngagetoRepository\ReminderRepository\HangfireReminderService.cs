using EngagetoContracts.ReminderContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoEntities;
using EngagetoEntities.Constants;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.ReminderRepository
{
    /// <summary>
    /// Hangfire-based reminder service for precise scheduling and better reliability
    /// </summary>
    public class HangfireReminderService : IReminderService
    {
        private readonly ApplicationDbContext _context;
        private readonly ITemplate _templateService;
        private readonly ILogger<HangfireReminderService> _logger;
        private readonly IBackgroundJobClient _backgroundJobClient;

        public HangfireReminderService(
            ApplicationDbContext context,
            ITemplate templateService,
            ILogger<HangfireReminderService> logger,
            IBackgroundJobClient backgroundJobClient)
        {
            _context = context;
            _templateService = templateService;
            _logger = logger;
            _backgroundJobClient = backgroundJobClient;
        }

        /// <summary>
        /// Schedule a reminder using Hangfire for precise timing
        /// </summary>
        /// <param name="contact">Contact to send reminder to</param>
        /// <param name="templateId">Template ID to use</param>
        /// <param name="reminderTime">Exact time to send reminder</param>
        /// <param name="reminderType">Type identifier for tracking</param>
        /// <returns>Hangfire job ID</returns>
        public string ScheduleReminder(Contacts contact, Guid templateId, DateTime reminderTime, string reminderType)
        {
            try
            {
                _logger.LogInformation("Scheduling reminder for contact {ContactId} at {ReminderTime} using template {TemplateId}",
                    contact.ContactId, reminderTime, templateId);

                // Create reminder tracking record
                var reminderRecord = new ContactReminder
                {
                    Id = Guid.NewGuid(),
                    ContactId = contact.ContactId,
                    ScheduledDate = contact.ScheduledAt ?? DateTime.UtcNow,
                    ReminderType = reminderType,
                    TemplateId = templateId,
                    SentAt = reminderTime,
                    Status = ReminderStatus.Pending,
                    BusinessId = contact.BusinessId,
                    CreatedAt = DateTime.UtcNow
                };

                _context.ContactReminders.Add(reminderRecord);
                _context.SaveChanges();

                // Schedule Hangfire job
                var jobId = _backgroundJobClient.Schedule(
                    () => ExecuteReminderJob(contact.ContactId, templateId, reminderRecord.Id, reminderType),
                    reminderTime);

                // Update reminder record with job ID
                reminderRecord.WhatsAppMessageId = jobId; // Store job ID temporarily
                _context.SaveChanges();

                _logger.LogInformation("Successfully scheduled reminder job {JobId} for contact {ContactId}",
                    jobId, contact.ContactId);

                return jobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling reminder for contact {ContactId}", contact.ContactId);
                throw;
            }
        }

        /// <summary>
        /// Execute the actual reminder job (called by Hangfire)
        /// </summary>
        [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 300, 900 })]
        public async Task ExecuteReminderJob(Guid contactId, Guid templateId, Guid reminderRecordId, string reminderType)
        {
            try
            {
                _logger.LogInformation("Executing reminder job for contact {ContactId}, template {TemplateId}",
                    contactId, templateId);

                // Get contact and reminder record
                var contact = await _context.Contacts.FirstOrDefaultAsync(c => c.ContactId == contactId);
                var reminderRecord = await _context.ContactReminders.FirstOrDefaultAsync(r => r.Id == reminderRecordId);

                if (contact == null)
                {
                    _logger.LogWarning("Contact {ContactId} not found for reminder job", contactId);
                    return;
                }

                if (reminderRecord == null)
                {
                    _logger.LogWarning("Reminder record {ReminderRecordId} not found", reminderRecordId);
                    return;
                }

                // Get template details
                var template = await _context.Templates.FirstOrDefaultAsync(t => t.TemplateId == templateId);
                if (template == null)
                {
                    _logger.LogWarning("Template {TemplateId} not found for reminder", templateId);
                    reminderRecord.Status = ReminderStatus.Failed;
                    reminderRecord.ErrorMessage = "Template not found";
                    await _context.SaveChangesAsync();
                    return;
                }

                // Send reminder template
                var templateRequest = new TemplateRequestDto
                {
                    TemplateName = template.TemplateName,
                    CountryCode = contact.CountryCode,
                    Contact = contact.Contact,
                    Name = contact.Name
                };

                var result = await _templateService.GetSendTemplateAsync(
                    contact.BusinessId.ToString(),
                    contact.BusinessId,
                    templateRequest);

                if (result != null && !string.IsNullOrEmpty(result.WhatsAppMessageId))
                {
                    // Update reminder record with success
                    reminderRecord.Status = ReminderStatus.Sent;
                    reminderRecord.WhatsAppMessageId = result.WhatsAppMessageId;
                    reminderRecord.SentAt = DateTime.UtcNow;

                    _logger.LogInformation("Successfully sent reminder to contact {ContactId}. MessageId: {MessageId}",
                        contactId, result.WhatsAppMessageId);
                }
                else
                {
                    // Update reminder record with failure
                    reminderRecord.Status = ReminderStatus.Failed;
                    reminderRecord.ErrorMessage = "Template service returned null or empty message ID";

                    _logger.LogWarning("Failed to send reminder to contact {ContactId}: Template service returned null",
                        contactId);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing reminder job for contact {ContactId}", contactId);

                // Update reminder record with error
                try
                {
                    var reminderRecord = await _context.ContactReminders.FirstOrDefaultAsync(r => r.Id == reminderRecordId);
                    if (reminderRecord != null)
                    {
                        reminderRecord.Status = ReminderStatus.Failed;
                        reminderRecord.ErrorMessage = ex.Message;
                        await _context.SaveChangesAsync();
                    }
                }
                catch (Exception updateEx)
                {
                    _logger.LogError(updateEx, "Error updating reminder record after failure");
                }

                throw; // Re-throw for Hangfire retry mechanism
            }
        }

        /// <summary>
        /// Cancel a scheduled reminder
        /// </summary>
        /// <param name="jobId">Hangfire job ID</param>
        /// <returns>True if cancelled successfully</returns>
        public bool CancelReminder(string jobId)
        {
            try
            {
                var result = _backgroundJobClient.Delete(jobId);
                _logger.LogInformation("Cancelled reminder job {JobId}: {Result}", jobId, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling reminder job {JobId}", jobId);
                return false;
            }
        }

        /// <summary>
        /// Get reminder statistics
        /// </summary>
        /// <param name="businessId">Business ID</param>
        /// <returns>Reminder statistics</returns>
        public async Task<object> GetReminderStatsAsync(Guid businessId)
        {
            try
            {
                var stats = await _context.ContactReminders
                    .Where(r => r.BusinessId == businessId)
                    .GroupBy(r => r.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                return new
                {
                    BusinessId = businessId,
                    Statistics = stats,
                    GeneratedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder stats for business {BusinessId}", businessId);
                throw;
            }
        }

        #region IReminderService Implementation (Legacy Methods)

        public async Task<int> ProcessPendingRemindersAsync()
        {
            // This method is not needed with Hangfire approach
            // Hangfire handles scheduling automatically
            _logger.LogInformation("ProcessPendingRemindersAsync called - not needed with Hangfire approach");
            return 0;
        }

        public async Task<int> ProcessBusinessRemindersAsync(Guid businessId)
        {
            // This method is not needed with Hangfire approach
            _logger.LogInformation("ProcessBusinessRemindersAsync called for business {BusinessId} - not needed with Hangfire", businessId);
            return 0;
        }

        public async Task<List<Contacts>> GetContactsNeedingRemindersAsync(Guid businessId, string reminderType)
        {
            // This method is not needed with Hangfire approach
            return new List<Contacts>();
        }

        public async Task<(bool Success, string? MessageId, string? ErrorMessage)> SendReminderAsync(Contacts contact, ReminderConfiguration reminderConfig)
        {
            // This is handled by ExecuteReminderJob in Hangfire approach
            return (false, null, "Use ScheduleReminder method instead");
        }

        public async Task<bool> IsReminderAlreadySentAsync(Guid contactId, string reminderType, DateTime scheduledDate)
        {
            try
            {
                var exists = await _context.ContactReminders
                    .AnyAsync(cr => cr.ContactId == contactId
                        && cr.ReminderType == reminderType
                        && cr.Status == ReminderStatus.Sent
                        && Math.Abs((cr.ScheduledDate - scheduledDate).TotalHours) < 1);

                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if reminder already sent for contact {ContactId}", contactId);
                return false;
            }
        }

        public async Task<ReminderConfiguration> CreateOrUpdateReminderConfigAsync(Guid businessId, string reminderType, Guid templateId, int minutesBeforeSchedule, bool isEnabled = true)
        {
            // This method is still useful for configuration management
            try
            {
                var existingConfig = await _context.ReminderConfigurations
                    .FirstOrDefaultAsync(rc => rc.BusinessId == businessId && rc.ReminderType == reminderType);

                var template = await _context.Templates.FirstOrDefaultAsync(t => t.TemplateId == templateId);
                var templateName = template?.TemplateName ?? "Unknown Template";

                if (existingConfig != null)
                {
                    existingConfig.TemplateId = templateId;
                    existingConfig.TemplateName = templateName;
                    existingConfig.MinutesBeforeSchedule = minutesBeforeSchedule;
                    existingConfig.IsEnabled = isEnabled;
                    existingConfig.UpdatedAt = DateTime.UtcNow;

                    await _context.SaveChangesAsync();
                    return existingConfig;
                }
                else
                {
                    var newConfig = new ReminderConfiguration
                    {
                        Id = Guid.NewGuid(),
                        BusinessId = businessId,
                        ReminderType = reminderType,
                        TemplateId = templateId,
                        TemplateName = templateName,
                        MinutesBeforeSchedule = minutesBeforeSchedule,
                        IsEnabled = isEnabled,
                        Priority = 1,
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.ReminderConfigurations.Add(newConfig);
                    await _context.SaveChangesAsync();
                    return newConfig;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating reminder configuration for business {BusinessId}", businessId);
                throw;
            }
        }

        public async Task<List<ReminderConfiguration>> GetReminderConfigurationsAsync(Guid businessId)
        {
            try
            {
                return await _context.ReminderConfigurations
                    .Include(rc => rc.Template)
                    .Where(rc => rc.BusinessId == businessId)
                    .OrderBy(rc => rc.Priority)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder configurations for business {BusinessId}", businessId);
                return new List<ReminderConfiguration>();
            }
        }

        public async Task<List<ContactReminder>> GetContactReminderHistoryAsync(Guid contactId)
        {
            try
            {
                return await _context.ContactReminders
                    .Include(cr => cr.Template)
                    .Where(cr => cr.ContactId == contactId)
                    .OrderByDescending(cr => cr.SentAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder history for contact {ContactId}", contactId);
                return new List<ContactReminder>();
            }
        }

        #endregion
    }
}
