﻿using Concentus.Oggfile;
using Concentus.Structs;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Text.RegularExpressions;

namespace EngagetoEntities.Validations.TemplateValidation
{
    public class TemplateValidation
    {

        private readonly ApplicationDbContext _context;
        private readonly IHttpClientFactory _httpClientFactory;
        private IConfiguration _configuration { get; set; }
        public TemplateValidation(ApplicationDbContext templateDbContext, IHttpClientFactory httpClientFactory,
                                  IConfiguration _configurations)
        {
            _context = templateDbContext;
            _httpClientFactory = httpClientFactory;
            _configuration = _configurations;

        }

        public async Task<IActionResult> ValidateCreateTemplate(CreateTemplateDto model)
        {
            // var existingTemplate = _context.Templates.Where(t => t.TemplateName == model.TemplateName || t.Footer == model.Footer || t.Body == model.Body || t.Status == WATemplateStatus.DRAFT).ToList();
            var existingTemplate = _context.Templates.Where(t => t.BusinessId == model.BusinessId && t.TemplateName == model.TemplateName || t.BusinessId == model.BusinessId && t.Body == model.Body).ToList();
            if (string.IsNullOrEmpty(model.TemplateName))
            {
                return new BadRequestObjectResult(new { Message = "TemplateName is required" });
            }
            if (!StringHelper.IsValidTemplateName(model.TemplateName))
            {
                return new BadRequestObjectResult(new { Message = "Only lowercase letters, integers, and underscores are allowed in TemplateName." });
            }
            if (model.TemplateName.Length > 512)
            {
                return new BadRequestObjectResult(new { Message = "Maximum 512 characters allowed in TemplateName." });
            }
            if (existingTemplate.Any(t => t.TemplateName == model.TemplateName && t.Status != WATemplateStatus.DRAFT))
            {
                return new BadRequestObjectResult(new { Message = $"Template name '{model.TemplateName}' exists and it must be unique. " });
            }
            if (model.Language == null)
            {
                return new BadRequestObjectResult(new { Message = "Template language is required" });
            }
            if (model.Category == null)
            {
                return new BadRequestObjectResult(new { Message = "Template category is required" });
            }

            if (model.MediaType == null)
            {
                return new BadRequestObjectResult(new { Message = "Template MediaType is required" });
            }

            Regex regex1 = StringHelper.GetVariableRegexs();
            if (!string.IsNullOrEmpty(model.Header) && regex1.IsMatch(model.Header))
            {
                int count = regex1.Matches(model.Header).Count;
                if (count != 1)
                {
                    return new BadRequestObjectResult(new { Message = "Header is Invalid. Only one variable {{1}} allowed." });
                }
            }
            // body validation
            if (string.IsNullOrEmpty(model.Body))
            {
                return new BadRequestObjectResult(new { Message = "Template body is required" });
            }
            if (model.Body.Length < 1 || model.Body.Length > 1024)
            {
                return new BadRequestObjectResult(new { Message = "Body Length must be between 1 and 1024 characters." });
            }
            if (!string.IsNullOrEmpty(model.Body))
            {
                if (existingTemplate.Any(t => t.Body == model.Body && t.Status != WATemplateStatus.DRAFT))
                {
                    return new BadRequestObjectResult(new { Message = "Body must be unique. A template with this Body already exists." });
                }
            }
            Regex regex2 = StringHelper.GetVariableRegexs();
            Regex regex3 = StringHelper.GetPlaceholderWithWordsBetweenRegex();

            bool containsPlaceholders2 = regex2.IsMatch(model.Body);
            bool containsPlaceholders3 = regex3.IsMatch(model.Body);
            int placeholderCount = regex2.Matches(model.Body).Count;
            if (containsPlaceholders2)
            {
                var segments = regex2.Split(model.Body);
                int wordsBetweenVariablesCount = 0;
                foreach (var segment in segments)
                {
                    string trimmedSegment = segment.Trim();
                    if (!string.IsNullOrWhiteSpace(trimmedSegment))
                    {
                        string[] words = trimmedSegment.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                        wordsBetweenVariablesCount += words.Length;
                    }
                }
                int variableLength = placeholderCount;
                if (wordsBetweenVariablesCount <= 2 * variableLength)
                {
                    return new BadRequestObjectResult(new { Message = "This template contains too many variable parameters relative to the message length. You need to decrease the number of variable parameters or increase the message length." });
                }
            }
            if (model.Footer?.Length > 60)
            {
                return new BadRequestObjectResult(new { Message = "Footer cannot exceed 60 characters." });
            }

            if (!string.IsNullOrEmpty(model.CallButtonName) && model.CallButtonName.Length > 20)
            {
                return new BadRequestObjectResult(new { Message = "CallButtonName length must be between 0 and 20 characters." });
            }
            if (!string.IsNullOrEmpty(model.CountryCode))
            {
                if (model.CountryCode.Length > 4 || model.CountryCode.Length < 1)
                {
                    return new BadRequestObjectResult(new { Message = "CountryCode length must be between 1 to 4 characters having + Ex: +91." });
                }

                if (!_context.CountryDetails.Select(m => m.CountryCode).Contains(model.CountryCode))
                {
                    return new BadRequestObjectResult(new { Message = "Country code is not valid or not found." });
                }
            }
            bool isCallButtonNameProvided = !string.IsNullOrEmpty(model.CallButtonName);
            bool isPhoneNumberProvided = !string.IsNullOrEmpty(model.PhoneNumber);
            bool isCountryCodeProvided = !string.IsNullOrEmpty(model.CountryCode);
            if ((isCallButtonNameProvided || isPhoneNumberProvided || isCountryCodeProvided) && !(isCallButtonNameProvided && isPhoneNumberProvided && isCountryCodeProvided))
            {
                return new BadRequestObjectResult(new { Message = "CallButtonName, PhoneNumber, and CountryCode must all be provided together." });
            }
            if (!string.IsNullOrEmpty(model.PhoneNumber))
            {
                if (model.PhoneNumber.Length > 20 || model.PhoneNumber.Length < 0)
                {
                    return new BadRequestObjectResult(new { Message = "PhoneNumber length must be between 0 to 20 characters." });
                }
                try
                {
                    var (contact, isValid) = PhoneNumberValidator.ValidatePhoneNumber(model.CountryCode, model.PhoneNumber);
                    if (!isValid)
                    {
                        return new BadRequestObjectResult(new { Message = "This phone number format is not recognized. Please check the country code and phone number" });
                    }
                }
                catch (Exception ex)
                {
                    return new BadRequestObjectResult(new { ex.Message });
                }
            }
            // Url Buttons are optional //
            if (model.UrlButtonName?.Count > 2 || model.RedirectUrl?.Count > 2)
            {
                return new BadRequestObjectResult(new { Message = "Maximum 2 UrlButtonName and RedirectUrl allowed." });
            }

            if ((model.UrlButtonName?.Count ?? 0) > 0 || (model.RedirectUrl?.Count ?? 0) > 0)
            {
                if (model.UrlButtonName != null && model.RedirectUrl != null)
                {
                    if (model.UrlButtonName.Count != model.RedirectUrl.Count)
                    {
                        return new BadRequestObjectResult(new { Message = "Each URL button name must have a corresponding URLs and Each URL must have URL button name ." });
                    }

                    for (int i = 0; i < model.UrlButtonName.Count; i++)
                    {
                        if (string.IsNullOrEmpty(model.RedirectUrl[i]))
                        {
                            return new BadRequestObjectResult(new { Message = $"URL is required for button '{model.UrlButtonName[i]}'." });
                        }
                        if (model.UrlButtonName[i].Length > 25)
                        {
                            return new BadRequestObjectResult(new { Message = " UrlButton Name text can't have more than 25 characters." });
                        }
                        if (model.RedirectUrl[i].Length > 2000)
                        {
                            return new BadRequestObjectResult(new { Message = $"URL for button '{model.UrlButtonName[i]}' can't have more than 2000 characters." });
                        }
                    }
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Both URL button names and redirect URLs must be provided together." });
                }
            }

            if (model.RedirectUrl != null)
            {
                foreach (var url in model.RedirectUrl)
                {
                    if (!StringHelper.IsValidUrl(url))
                    {
                        return new BadRequestObjectResult(new { Message = "Invalid URL format." });
                    }
                }
            }
            // QuickReply Buttons are optional QuickReply.Count is 10 only //
            if (model.QuickReply != null)
            {
                // Check if the count of QuickReply is more than 10
                if (model.QuickReply.Count > 10)
                {
                    return new BadRequestObjectResult(new { Message = "Maximum 10 QuickReply allowed." });
                }

                // Check if each QuickReply text has 25 characters or fewer
                foreach (var reply in model.QuickReply)
                {
                    if (reply.Length > 25)
                    {
                        return new BadRequestObjectResult(new { Message = "QuickReply Button text can't have more than 25 characters." });
                    }
                }
            }
            return null; // Return null if all validations pass
        }

        public async Task<IActionResult> ValidateCreateMediaFile(CreateTemplateDto model)
        {
            var imageSize = int.Parse(_configuration["MetaMediaFileSize:Image"]);
            var documentSize = int.Parse(_configuration["MetaMediaFileSize:Document"]);
            var videoSize = int.Parse(_configuration["MetaMediaFileSize:Video"]);
            var client = _httpClientFactory.CreateClient();
            var res = await client.GetAsync(model.MediaFile);
            if (!res.IsSuccessStatusCode)
            {
                return new BadRequestObjectResult(new { Message = "Failed to fetch media file from the provided URL." });
            }

            var contentType = res.Content.Headers.ContentType?.MediaType;
            var fileSize = res.Content.Headers.ContentLength ?? 0;

            if (fileSize <= 0)
            {
                return new BadRequestObjectResult(new { Message = "Empty file." });
            }
            bool isValidFile = false;

            if (fileSize <= imageSize && (contentType == "image/jpg" ||
                                                contentType == "image/jpeg" ||
                                                contentType == "image/png")) // 5MB for image
            {
                isValidFile = true;
            }
            else if (fileSize <= documentSize && (contentType.StartsWith("text/") ||
                                                       contentType == "application/pdf" ||
                                                       contentType.StartsWith("application/vnd"))) // 100MB for document
            {
                isValidFile = true;
            }
            else if (contentType.StartsWith("video/"))
            {
                // Supported video types and size check
                var supportedVideoTypes = new[] { "video/mp4", "video/3gp", "video/3gpp" };
                var maxVideoFileSize = videoSize; // 16MB

                if (supportedVideoTypes.Contains(contentType) && fileSize <= maxVideoFileSize)
                {
                    isValidFile = true;
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Unsupported video file type or file size exceeded. WhatsApp supports only MP4 and 3GP formats up to 16MB with H.264 video codec and AAC audio codec." });
                }
            }
            else
            {
                return new BadRequestObjectResult(new { Message = "Unsupported file type." });
            }

            if (!isValidFile)
            {
                return new BadRequestObjectResult(new { Message = "Invalid file." });
            }

            return null; // Return null if all validations pass
        }

        public async Task<IActionResult> ValidateEditTemplate(EditTemplateDto model)
        {
            if (model.MediaType == null)
            {
                return new BadRequestObjectResult(new { Message = "Template MediaType is required." });
            }

            if (model.Header?.Length > 60)
            {
                return new BadRequestObjectResult(new { Message = "Header length must be between 0 and 60 characters and match the required pattern only one variable {{1}} allowed and with exactly one space before and after the variable." });
            }

            Regex regex1 = StringHelper.GetVariableRegexs();
            if (!string.IsNullOrEmpty(model.Header) && regex1.IsMatch(model.Header))
            {
                int count = regex1.Matches(model.Header).Count;
                if (count != 1)
                {
                    return new BadRequestObjectResult(new { Message = "Header is Invalid. Only one variable {{1}} allowed." });
                }
            }
            if (model.Footer?.Length > 60)
            {
                return new BadRequestObjectResult(new { Message = "Footer cannot exceed 60 characters." });
            }
            if (string.IsNullOrWhiteSpace(model.Body) || model.Body.Length > 1024)
            {
                return new BadRequestObjectResult(new { Message = "Body length must be between 1 and 1024 characters." });
            }
            Regex regex2 = StringHelper.GetVariableRegexs();
            Regex regex3 = StringHelper.GetPlaceholderWithWordsBetweenRegex();
            bool containsPlaceholders2 = regex2.IsMatch(model.Body);
            bool containsPlaceholders3 = regex3.IsMatch(model.Body);
            int placeholderCount = regex2.Matches(model.Body).Count;

            if (containsPlaceholders2)
            {

                var segments = regex2.Split(model.Body);
                int wordsBetweenVariablesCount = 0;

                foreach (var segment in segments)
                {
                    string trimmedSegment = segment.Trim();
                    if (!string.IsNullOrWhiteSpace(trimmedSegment))
                    {
                        string[] words = trimmedSegment.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                        wordsBetweenVariablesCount += words.Length;
                    }
                }
                int variableLength = placeholderCount;
                if (wordsBetweenVariablesCount <= 2 * variableLength)
                {
                    return new BadRequestObjectResult(new { Message = "This template contains too many variable parameters relative to the message length. You need to decrease the number of variable parameters or increase the message length." });
                }
            }

            if (!string.IsNullOrEmpty(model.Footer) && model.Footer.Length > 60)
            {
                return new BadRequestObjectResult(new { Message = "Footer length must be between 0 and 60 characters." });
            }

            if (!string.IsNullOrEmpty(model.CallButtonName) && model.CallButtonName.Length > 60)
            {
                return new BadRequestObjectResult(new { Message = "CallButtonName length must be between 0 and 20 characters." });
            }
            if (!string.IsNullOrEmpty(model.CountryCode))
            {
                if (model.CountryCode.Length > 4 || model.CountryCode.Length < 1)
                {
                    return new BadRequestObjectResult(new { Message = "CountryCode length must be between 1 and 4 characters." });
                }
                if (!_context.CountryDetails.Select(m => m.CountryCode).Contains(model.CountryCode))
                {
                    return new BadRequestObjectResult(new { Message = "Country code is not valid or not found." });
                }
            }
            if (!string.IsNullOrEmpty(model.PhoneNumber))
            {
                if (model.PhoneNumber.Length > 20 || model.PhoneNumber.Length < 0)
                {
                    return new BadRequestObjectResult(new { Message = "PhoneNumber length must be between 0 and 20 characters." });
                }
                try
                {
                    var (contact, isValid) = PhoneNumberValidator.ValidatePhoneNumber(model.CountryCode, model.PhoneNumber);
                    if (!isValid)
                    {
                        return new BadRequestObjectResult(new { Message = "This phone number format is not recognized. Please check the country code and phone number" });
                    }
                }
                catch (Exception ex)
                {
                    return new BadRequestObjectResult(new { ex.Message });
                }
            }

            if (model.UrlButtonName?.Count > 2 || model.RedirectUrl?.Count > 2)
            {
                return new BadRequestObjectResult(new { Message = "Maximum 2 UrlButtonName and RedirectUrl allowed." });
            }

            bool isCallButtonNameProvided = !string.IsNullOrEmpty(model.CallButtonName);
            bool isPhoneNumberProvided = !string.IsNullOrEmpty(model.PhoneNumber);
            bool isCountryCodeProvided = !string.IsNullOrEmpty(model.CountryCode);

            if ((isCallButtonNameProvided || isPhoneNumberProvided || isCountryCodeProvided) &&
                  !(isCallButtonNameProvided && isPhoneNumberProvided && isCountryCodeProvided))
            {
                return new BadRequestObjectResult(new { Message = "CallButtonName, PhoneNumber, and CountryCode must all be provided together." });
            }

            if ((model.UrlButtonName?.Count ?? 0) > 0 || (model.RedirectUrl?.Count ?? 0) > 0)
            {
                if (model.UrlButtonName != null && model.RedirectUrl != null)
                {
                    if (model.UrlButtonName.Count != model.RedirectUrl.Count)
                    {
                        return new BadRequestObjectResult(new { Message = "Each URL button name must have a corresponding URL." });
                    }
                    for (int i = 0; i < model.UrlButtonName.Count; i++)
                    {
                        if (string.IsNullOrEmpty(model.RedirectUrl[i]))
                        {
                            return new BadRequestObjectResult(new { Message = $"URL is required for button '{model.UrlButtonName[i]}'." });
                        }
                    }
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Both URL button names and redirect URLs must be provided together." });
                }
            }

            if (model.RedirectUrl != null)
            {
                foreach (var url in model.RedirectUrl)
                {
                    if (!StringHelper.IsValidUrl(url))
                    {
                        return new BadRequestObjectResult(new { Message = "Invalid URL format." });
                    }
                }
            }

            // QuickReply Buttons are optional QuickReply.Count is 10 only //
            if (model.QuickReply != null)
            {
                // Check if the count of QuickReply is more than 10
                if (model.QuickReply.Count > 10)
                {
                    return new BadRequestObjectResult(new { Message = "Maximum 10 QuickReply allowed." });
                }

                // Check if each QuickReply text has 25 characters or fewer
                foreach (var reply in model.QuickReply)
                {
                    if (reply.Length > 25)
                    {
                        return new BadRequestObjectResult(new { Message = "QuickReply Button text can't have more than 25 characters." });
                    }
                }
            }
            return null; // Return null if all validations pass
        }

        public async Task<IActionResult> ValidateEditMediaTemplate(EditTemplateDto model)
        {
            var imageSize = int.Parse(_configuration["MetaMediaFileSize:Image"]);
            var documentSize = int.Parse(_configuration["MetaMediaFileSize:Document"]);
            var videoSize = int.Parse(_configuration["MetaMediaFileSize:Video"]);
            var client = _httpClientFactory.CreateClient();
            var res = await client.GetAsync(model.MediaFile);
            if (!res.IsSuccessStatusCode)
            {
                return new BadRequestObjectResult(new { Message = "Failed to fetch media file from the provided URL." });
            }

            var contentType = res.Content.Headers.ContentType?.MediaType;
            var fileSize = res.Content.Headers.ContentLength ?? 0;

            if (fileSize <= 0)
            {
                return new BadRequestObjectResult(new { Message = "Empty file." });
            }
            bool isValidFile = false;

            if (fileSize <= imageSize && (contentType == "image/jpg" ||
                                                contentType == "image/jpeg" ||
                                                contentType == "image/png")) // 5MB for image
            {
                isValidFile = true;
            }
            else if (fileSize <= documentSize && (contentType.StartsWith("text/") ||
                                                       contentType == "application/pdf" ||
                                                       contentType.StartsWith("application/vnd"))) // 100MB for document
            {
                isValidFile = true;
            }
            else if (contentType.StartsWith("video/"))
            {
                // Supported video types and size check
                var supportedVideoTypes = new[] { "video/mp4", "video/3gp", "video/3gpp" };
                var maxVideoFileSize = videoSize; // 16MB

                if (supportedVideoTypes.Contains(contentType) && fileSize <= maxVideoFileSize)
                {
                    isValidFile = true;
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Unsupported video file type or file size exceeded. WhatsApp supports only MP4 and 3GP formats up to 16MB with H.264 video codec and AAC audio codec." });
                }
            }
            else
            {
                return new BadRequestObjectResult(new { Message = "Unsupported file type." });
            }

            if (!isValidFile)
            {
                return new BadRequestObjectResult(new { Message = "Invalid file." });
            }

            return null; // Return null if all validations pass
        }



        public IActionResult ValidateFile(IFormFile file)
        {
            var imageSize = int.Parse(_configuration["MetaMediaFileSize:Image"]);
            var documentSize = int.Parse(_configuration["MetaMediaFileSize:Document"]);
            var videoSize = int.Parse(_configuration["MetaMediaFileSize:Video"]);
            var audioSize = int.Parse(_configuration["MetaMediaFileSize:Audio"]);

            var contentType = file.ContentType;
            var fileSize = file.Length;
            var fileType = contentType;

            if (fileSize <= 0)
            {
                return new BadRequestObjectResult(new { Message = "Invalid file size." });
            }

            bool isValidFile = false;

            if (fileType.StartsWith("image/"))
            {
                if (fileSize <= imageSize && (fileType == "image/jpg" || fileType == "image/jpeg" || fileType == "image/png"))
                {
                    isValidFile = true;
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Unsupported file type. Image must be less than or equal to 5MB." });
                }
            }
            else if (fileType.StartsWith("text/") || fileType.StartsWith("application/"))
            {
                if (fileSize <= documentSize && (fileType.StartsWith("text/") || fileType == "application/pdf" || fileType.StartsWith("application/vnd")))
                {
                    isValidFile = true;
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Unsupported file type. Document must be less than or equal to 100MB." });
                }
            }
            else if (fileType.StartsWith("video/"))
            {
                var supportedVideoTypes = new[] { "video/mp4", "video/3gp", "video/3gpp" };

                if (supportedVideoTypes.Contains(fileType) && fileSize <= videoSize)
                {
                    isValidFile = true;
                }
                else
                {
                    return new BadRequestObjectResult(new { Message = "Unsupported video file type or file size exceeded. WhatsApp supports only MP4 and 3GP formats up to 16MB with H.264 video codec and AAC audio codec." });
                }
            }
            else if (fileType.StartsWith("audio/"))
            {
                var supportedAudioTypes = new[] { "audio/aac", "audio/mp4", "audio/mpeg", "audio/amr", "audio/ogg" };

                if (supportedAudioTypes.Contains(fileType) && fileSize <= audioSize)
                {
                    if (fileType == "audio/ogg")
                    {
                        using (var stream = file.OpenReadStream())
                        {
                            if (!IsOpusEncoded(stream))
                            {
                                return new BadRequestObjectResult(new { Message = "Unsupported audio/ogg codec. Only Opus codec is supported." });
                            }
                        }
                    }

                    isValidFile = true;
                }
                else
                {
                    if (!supportedAudioTypes.Contains(fileType))
                    {
                        return new BadRequestObjectResult(new { Message = "Unsupported audio file type. Supported types are: aac, mp4, mpeg, amr, and ogg." });
                    }

                    if (fileSize > videoSize)
                    {
                        return new BadRequestObjectResult(new { Message = "Audio file size exceeded. The file size should be less than or equal to 16MB." });
                    }
                }
            }
            else
            {
                return new BadRequestObjectResult(new { Message = "Unsupported file type." });
            }

            if (!isValidFile)
            {
                return new BadRequestObjectResult(new { Message = "Invalid file." });
            }

            return null; // Validation passed
        }
        private bool IsOpusEncoded(Stream stream)
        {
            try
            {
                // Ensure the stream is positioned at the beginning
                if (stream.CanSeek)
                {
                    stream.Position = 0;
                }

                var decoder = new OpusDecoder(48000, 2); // Assuming 48kHz, stereo
                var oggIn = new OpusOggReadStream(decoder, stream);
                return oggIn.HasNextPacket;
            }
            catch
            {
                return false;
            }
        }
    }
}


