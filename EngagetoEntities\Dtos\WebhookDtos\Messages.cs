﻿using Newtonsoft.Json;

namespace EngagetoEntities.Dtos.WebhookDtos
{
    public class Messages
    {
        public ConversationDtos.ConversationDto Message(Entities.Conversations Obj, IEnumerable<Entities.Conversations>? Reply)
        {
            var settings = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            };

            ConversationDtos.ConversationDto obj = new ConversationDtos.ConversationDto
            {
                Id = Obj.Id,
                WhatsAppMessageId = Obj.WhatsAppMessageId,
                From = Obj.From,
                To = Obj.To,
                Reply = GetReplyItems(Reply, Obj.ReplyId),
                Status = Obj.Status.ToString(),
                CreatedAt = Obj.CreatedAt,
                TextMessage = Obj.TextMessage,
                MediaCaption = Obj.MediaCaption,
                MediaFileName = Obj.MediaFileName,
                MediaUrl = Obj.MediaUrl,
                MediaMimeType = Obj.MediaMimeType,
                TemplateMediaType = Obj.TemplateMediaType,
                TemplateBody = Obj.TemplateBody,
                TemplateHeader = Obj.TemplateHeader,
                TemplateFooter = Obj.TemplateFooter,
                TemplateMediaFile = Obj.TemplateMediaUrl,
                CallButtonName = Obj.CallButtonName,
                PhoneNumber = Obj.PhoneNumber,
                UrlButtonNames = ParseCommaSeparatedValues(Obj.UrlButtonNames),
                RedirectUrls = ParseCommaSeparatedValues(Obj.RedirectUrls),
                QuickReplies = ParseCommaSeparatedValues(Obj.QuickReplies),
                Action = !string.IsNullOrEmpty(Obj.Action) ? JsonConvert.DeserializeObject<EngagetoEntities.Dtos.MetaDto.AutoReply.Action>(Obj.Action, settings) : null,
                ErrorMessage = Obj.ErrorMessage,
                CarouselCards = Obj.CarouselCards
            };

            return obj;

        }
        public object? GetReplyItems(IEnumerable<Entities.Conversations>? query, string? replyId)
        {
            if (replyId != null)
            {
                return query?.Where(m => m.WhatsAppMessageId == replyId).Select(i => new
                {
                    i.Id,
                    i.WhatsAppMessageId,
                    i.From,
                    i.To,
                    Status = i.Status.ToString(),
                    i.CreatedAt,
                    i.TextMessage,
                    i.MediaFileName,
                    i.MediaMimeType,
                    i.MediaUrl,
                    i.MediaCaption,
                    i.TemplateMediaType,
                    TemplateMediaFile = i.TemplateMediaUrl,
                    i.TemplateHeader,
                    i.TemplateBody,
                    i.TemplateFooter,
                    i.CallButtonName,
                    i.PhoneNumber,
                    i.ErrorMessage,
                    UrlButtonNames = ParseCommaSeparatedValues(i.UrlButtonNames),
                    RedirectUrls = ParseCommaSeparatedValues(i.RedirectUrls),
                    QuickReplies = ParseCommaSeparatedValues(i.QuickReplies),
                    i.CarouselCards
                }).FirstOrDefault();
            }
            return null;
        }
        private string[]? ParseCommaSeparatedValues(string? input)
        {
            return (input ?? string.Empty)
                .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrEmpty(x))
                .ToArray();
        }
    }
}
