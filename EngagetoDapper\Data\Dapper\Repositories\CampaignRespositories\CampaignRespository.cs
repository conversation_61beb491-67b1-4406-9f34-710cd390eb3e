﻿using Dapper;
using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Enums;
using System.Globalization;


namespace EngagetoDapper.Data.Dapper.Repositories.CampaignRespositories
{
    public class CampaignRespository : ICampaignRespository
    {
        private readonly IUnitOfWork _unitOfWork;
        public CampaignRespository(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ConverstationDetailsDto>> GetConversationDetailsAsync(string companyId)
        {
            string sql = "SELECT * FROM GetCompanySubscriptionAndContDetails(@CompanyId)";
            var parameters = new { CompanyId = companyId };
            var result = await _unitOfWork.Connection.QueryAsync<ConverstationDetailsDto>(sql, parameters);
            return result;

        }
        public async Task<int> GetCampaignCountAsync(string companyId, CampaignState state, FilterCondition? condition,Search? search)
        {
            TextInfo textInfo = CultureInfo.CurrentCulture.TextInfo;
            
            string sql = @"
                SELECT COUNT(*) 
                FROM Campaigns 
                WHERE BusinessId = @BusinessId
                AND State = @State";

            var parameters = new DynamicParameters();
            parameters.Add("BusinessId", companyId);
            parameters.Add("State", state);

            if (condition != null && !string.IsNullOrEmpty(condition.Column) && !string.IsNullOrEmpty(condition.Operator))
            {
                if (DateTime.TryParse(condition.Value, out var date))
                {
                    sql += $" AND {textInfo.ToTitleCase(condition.Column)} {condition.Operator} @ConditionValue";
                    parameters.Add("ConditionValue", date);
                }
                else
                {
                    sql += $" AND {textInfo.ToTitleCase(condition.Column)} {condition.Operator} @ConditionValue";
                    parameters.Add("ConditionValue", condition.Value);
                }
            }
            if (search != null && !string.IsNullOrEmpty(search.Column) && !string.IsNullOrEmpty(search.Value))
            {
                sql += $" AND {textInfo.ToTitleCase(search.Column)} LIKE @SearchValue";
                parameters.Add("SearchValue", $"%{search.Value}%"); 
            }

            var result = await _unitOfWork.Connection.ExecuteScalarAsync<int>(sql, parameters);
            return result;
        }

    }
}
