# 📅 Time-Based Reminder System Setup Guide

## Overview
The time-based reminder system automatically sends WhatsApp template messages to contacts based on their scheduled appointment dates. When a workflow template node processes a contact with a `ScheduledAt` date, the system sets up reminders at user-configured intervals.

## 🚀 Quick Setup

### Step 1: Create Reminder Templates
First, create WhatsApp templates specifically for reminders in your Meta Business Manager:

```
Template Name: appointment_reminder_1day
Message: Hi {{1}}! Reminder: You have an appointment tomorrow at {{2}}. Please confirm your attendance.

Template Name: appointment_reminder_30min  
Message: Hi {{1}}! Your appointment is in 30 minutes at {{2}}. We're looking forward to seeing you!

Template Name: appointment_reminder_15min
Message: Hi {{1}}! Your appointment is in 15 minutes at {{2}}. Please arrive on time.
```

### Step 2: Configure Reminder Settings
Use the Reminder API to configure when and which templates to send:

#### Setup All Reminder Types (Bulk Configuration)
```http
POST /api/reminder/configurations/bulk
Content-Type: application/json

{
  "businessId": "your-business-guid",
  "fifteenMinuteTemplateId": "template-guid-for-15min",
  "thirtyMinuteTemplateId": "template-guid-for-30min", 
  "oneDayTemplateId": "template-guid-for-1day",
  "enableFifteenMinute": true,
  "enableThirtyMinute": true,
  "enableOneDay": true
}
```

#### Setup Individual Reminder Types
```http
POST /api/reminder/configurations
Content-Type: application/json

{
  "businessId": "your-business-guid",
  "reminderType": "CUSTOM_2HOUR",
  "templateId": "your-template-guid",
  "minutesBeforeSchedule": 120,
  "isEnabled": true,
  "priority": 1
}
```

### Step 3: Set Contact Scheduled Dates
Ensure your contacts have the `ScheduledAt` field populated:

```csharp
var contact = new Contacts
{
    ContactId = Guid.NewGuid(),
    Name = "John Doe",
    Contact = "9876543210",
    CountryCode = "+91",
    ScheduledAt = DateTime.Parse("2025-06-12 10:00:00"), // Important!
    BusinessId = businessGuid
};
```

## 🔧 Configuration Options

### Reminder Types
You can create any custom reminder type with any interval:

| Reminder Type | Minutes Before | Example Use Case |
|---------------|----------------|------------------|
| `1_DAY` | 1440 | Day-before confirmation |
| `2_HOUR` | 120 | Pre-appointment preparation |
| `30_MINUTES` | 30 | Final reminder |
| `15_MINUTES` | 15 | Arrival reminder |
| `CUSTOM_WEEK` | 10080 | Weekly advance notice |

### Template Variables
Your reminder templates can use these variables:
- `{{1}}` - Contact Name
- `{{2}}` - Scheduled Time
- Custom variables based on your template design

## 📊 How It Works

### Workflow Processing
```
1. Contact enters workflow with ScheduledAt = "2025-06-12 10:00 AM"
2. Template node processes normally
3. System checks for reminder configurations
4. Sets up reminders for future processing
5. Background service sends reminders at configured times
```

### Timeline Example
```
Contact: John Doe
ScheduledAt: 2025-06-12 10:00 AM

Configured Reminders:
├── 1_DAY (1440 min) → Sends at 2025-06-11 10:00 AM
├── 2_HOUR (120 min) → Sends at 2025-06-12 08:00 AM  
├── 30_MINUTES (30 min) → Sends at 2025-06-12 09:30 AM
└── 15_MINUTES (15 min) → Sends at 2025-06-12 09:45 AM
```

## 🛠️ Management APIs

### View Current Configurations
```http
GET /api/reminder/configurations/{businessId}
```

### View Reminder History
```http
GET /api/reminder/history/{contactId}
```

### Manual Testing
```http
POST /api/reminder/process/{businessId}
```

### Debug Pending Reminders
```http
GET /api/reminder/pending/{businessId}/{reminderType}
```

## ⚡ Background Processing

The system runs a background service every 5 minutes that:
1. Checks all businesses for pending reminders
2. Finds contacts whose reminder time has arrived
3. Sends WhatsApp templates using your configured templates
4. Tracks sent reminders to prevent duplicates

## 🔍 Monitoring

### Logs to Watch
```
[INFO] Setting up time-based reminders for contact {ContactId} with scheduled date {ScheduledDate}
[INFO] Reminder setup completed for contact {ContactId}: {ValidCount} valid reminders, {SkippedCount} skipped
[INFO] Successfully sent {ReminderType} reminder to contact {ContactId}. MessageId: {MessageId}
```

### Common Issues
1. **No reminders sent**: Check if reminder configurations exist for your business
2. **Templates not found**: Ensure template IDs in configurations are valid and approved
3. **Past appointments**: Reminders are only set up for future `ScheduledAt` dates

## 🎯 Best Practices

### Template Design
- Keep messages concise and clear
- Include appointment time and date
- Add contact information or location if needed
- Use friendly, professional tone

### Timing Strategy
- **1 Day Before**: Confirmation and preparation instructions
- **2-4 Hours Before**: Final details and directions  
- **30 Minutes Before**: Arrival reminder
- **15 Minutes Before**: Last-minute check-in

### Configuration Management
- Test with a small group first
- Monitor delivery rates and responses
- Adjust timing based on your business needs
- Keep templates updated and compliant

## 🚨 Important Notes

1. **User Configuration Required**: The system does NOT create default configurations. You must set up reminder templates and intervals for each business.

2. **Future Appointments Only**: Reminders are only set up for contacts with `ScheduledAt` dates in the future.

3. **Duplicate Prevention**: The system automatically prevents sending the same reminder type multiple times for the same appointment.

4. **Template Approval**: Ensure your WhatsApp templates are approved by Meta before configuring them for reminders.

5. **Background Processing**: Reminders are processed every 5 minutes by the background service. There may be a slight delay between the exact reminder time and when the message is sent.

## 📞 Support

If you need help setting up reminders:
1. Check the logs for configuration issues
2. Use the debug APIs to verify your setup
3. Test with manual processing first
4. Ensure your templates are approved and accessible

The reminder system is designed to be flexible and user-controlled. Configure it according to your business needs and appointment scheduling patterns.
