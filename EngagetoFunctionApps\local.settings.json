{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    //dev conn
   //"ConnStr": "Data Source=engageto.database.windows.net;Initial Catalog=qa-engageto;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,2;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;Max Pool Size=100;Min Pool Size=5;"
    //prd
    // production database
    "ConnStr": "Data Source=engageto-prd.database.windows.net;Initial Catalog=engageto-prd;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,1;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30; Max Pool Size=500;Min Pool Size=5;"

  },
  "FunctionSettings": {
    "Dev_ProcessCampaignSubBatchesUrl": "https://qa-engageto-background-app.azurewebsites.net/api/ProcessCampaignSubBatches?code=yze3m5xdrD4_BsrFZ04w-nD0Fnxfwz2yJSV1JNGdlnuQAzFuB-NacQ==",
    "Prod_ProcessCampaignSubBatchesUrl": "https://prd-engageto-background-app.azurewebsites.net/api/ProcessCampaignSubBatches?code=ouRvDLLIITyGMp_5-5OsmW_TK-ZpghAvJF8XLUJwoRM4AzFuaqmi8Q=="
  }
  }