using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoRepository.MetaServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Text;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoEntities.Dtos.AutomationDtos;
using Mapster;
using EngagetoEntities.Dtos.TemplateDtos;
using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Asn1.X509;
using Microsoft.IdentityModel.Tokens;
using System.Linq;
using EngagetoContracts.Services;
using EngagetoContracts.AttributeName;
using EngagetoContracts.WorkflowRepository;
using EngagetoEntities.Dtos.AttributeNameDtos;
using DocumentFormat.OpenXml.Bibliography;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Dtos.MetaDto;
using Microsoft.AspNetCore.Http;

namespace EngagetoRepository.Services
{
    public class NodeWorkflowEngineService : INodeWorkflowEngineService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<NodeWorkflowEngineService> _logger;
        private readonly IMetaApiService _metaApiService;
        private readonly IGenericRepository _genericRepository;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IWorkflowCustomResponseService _workflowCustomResponseService;
        private readonly IAttributeNameService _attributeNameService;
        private readonly ICustomerWorkflowTrackerRepository _customerWorkflowTrackerRepository;
        private readonly IUserIdentityService _userIdentityService;

        public NodeWorkflowEngineService(
            ApplicationDbContext dbContext,
            ILogger<NodeWorkflowEngineService> logger,
            IMetaApiService metaApiService,
            IGenericRepository genericRepository,
            IHttpClientFactory httpClientFactory,
            IServiceProvider serviceProvider,
            IWorkflowCustomResponseService workflowCustomResponseService,
            IAttributeNameService attributeNameService,
            ICustomerWorkflowTrackerRepository customerWorkflowTrackerRepository,
            IUserIdentityService userIdentityService
            )
        {
            _dbContext = dbContext;
            _logger = logger;
            _metaApiService = metaApiService;
            _genericRepository = genericRepository;
            _httpClientFactory = httpClientFactory;
            _serviceProvider = serviceProvider;
            _workflowCustomResponseService = workflowCustomResponseService;
            _attributeNameService = attributeNameService;
            _customerWorkflowTrackerRepository = customerWorkflowTrackerRepository;
            _userIdentityService = userIdentityService;
        }

        public async Task ProcessWorkflowAsync(Contacts contact, string? textMessage, bool? isNewCustomer = false, bool? isStatusChange = false, bool? isProjectChange = false)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                var nodeStartTime = DateTime.UtcNow;
                var currentNode = await GetStartingNodeAsync(contact, textMessage, isNewCustomer, isStatusChange, isProjectChange);
                if (currentNode == null)
                {
                    return;
                }

                var processStartTime = DateTime.UtcNow;
                var nextNodeId = await ProcessNodeAsync(currentNode, contact, textMessage);

                if (nextNodeId != null)
                {
                    contact.WorkFlowNodeId = nextNodeId;
                    contact.WorkflowId = currentNode.WorkflowId;
                    _dbContext.Contacts.Update(contact);
                    await _dbContext.SaveChangesAsync();

                    await ProcessWorkflowAsync(contact, null, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing workflow for customer {CustomerId}", contact.ContactId);
            }
            finally
            {
                _logger.LogInformation("Total workflow processing time: {Duration}ms",
                    (DateTime.UtcNow - startTime).TotalMilliseconds);
            }
        }

        private async Task<WorkflowNode?> GetStartingNodeAsync(Contacts contact, string? textMessage, bool? isNewCustomer, bool? isStatusChange = false, bool? isProjectChange = false)
        {
            try
            {
                if (contact.WorkFlowNodeId != null && contact.WorkFlowNodeId != Guid.Empty)
                {
                    var activeNode = await _dbContext.WorkflowNodes
                        .FirstOrDefaultAsync(n => n.Id == contact.WorkFlowNodeId);

                    if (activeNode != null)
                    {
                        return activeNode;
                    }
                    else
                    {
                        contact.WorkFlowNodeId = null;
                    }
                }

                if (isStatusChange == true)
                {
                    try
                    {
                        var statusWorkflows = await _dbContext.WorkflowNodes
                            .Include(n => n.Workflow)
                            .Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted && n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();


                        var matchingWorkflows = statusWorkflows
                            .Where(n =>
                                n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.Status &&
                                n.PayloadModel?.FlowStartModel?.LeadStatus != null &&
                                n.PayloadModel.FlowStartModel.LeadStatus.Any(ls =>
                                    ls.Status?.ToLower() == contact.LeadStatus?.ToString()?.ToLower() &&
                                    (
                                        ls.SubStatus == null || ls.SubStatus.Count == 0 || string.IsNullOrEmpty(contact.LeadSubStatus?.ToString()) ||
                                        (ls.SubStatus != null && ls.SubStatus.Contains(contact.LeadSubStatus.ToString()))
                                    ))).ToList();



                        if (matchingWorkflows.Any())
                        {
                            var selectedNode = matchingWorkflows
                                .OrderByDescending(n => n.CreatedAt)
                                .FirstOrDefault();

                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;

                                await _dbContext.SaveChangesAsync();
                                return selectedNode;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing status change workflow for contact {ContactId}", contact.ContactId);
                        throw;
                    }
                }

                if (isProjectChange == true)
                {
                    try
                    {
                        var projectWorkflows = await _dbContext.WorkflowNodes
                                             .Include(n => n.Workflow)
                                              .Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted && n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();


                        var matchingWorkflows = projectWorkflows
                                              .Where(n => n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.Project &&
                                                         n.PayloadModel?.FlowStartModel?.LeadProject != null &&
                                                         n.PayloadModel.FlowStartModel.LeadProject.Any(ls =>
                                                         ls?.ToLower() == contact.Project?.ToString()?.ToLower())).ToList();

                        if (matchingWorkflows.Any())
                        {
                            var selectedNode = matchingWorkflows
                                .OrderByDescending(n => n.CreatedAt)
                                .FirstOrDefault();

                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;

                                await _dbContext.SaveChangesAsync();
                                return selectedNode;
                            }
                        }


                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing project change workflow for contact {ContactId}", contact.ContactId);
                        throw;
                    }

                }

                if (isNewCustomer == true)
                {
                    var potentialNodes = await _dbContext.WorkflowNodes
                                       .Include(n => n.Workflow).Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted &&
                                        n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();

                    var sourceMatchingNodes = potentialNodes
                        .Where(n =>
                            n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead &&
                            n.PayloadModel?.FlowStartModel?.LeadSource != null &&
                            n.PayloadModel.FlowStartModel.LeadSource.Any(ls =>
                                ls.Source == contact.Source.ToString() &&
                                (
                                    string.IsNullOrEmpty(contact.SubSource?.ToString()) || ls.SubSource != null && ls.SubSource.Contains(contact.SubSource.ToString())
                                ))).ToList();



                    if (sourceMatchingNodes.Any())
                    {
                        var selectedNode = SelectPriorityWorkflow(sourceMatchingNodes);
                        contact.WorkflowId = selectedNode.WorkflowId;
                        return selectedNode;
                    }

                    var allDefaultNodes = await _dbContext.WorkflowNodes
                       .Include(n => n.Workflow)
                       .Where(n => n.Type == NodeType.FlowStart &&
                                  n.IsEntry &&
                                  n.Workflow.IsActive &&
                                  !n.Workflow.IsDeleted &&
                                  n.Workflow.CompanyId == contact.BusinessId.ToString())
                       .ToListAsync();

                    var defaultNodes = allDefaultNodes
                        .Where(n => n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead)
                        .ToList();

                    if (defaultNodes.Any())
                    {
                        var selectedNode = SelectPriorityWorkflow(defaultNodes);
                        contact.WorkflowId = selectedNode.WorkflowId;

                        return selectedNode;
                    }
                    return null;
                }

                // For existing customers with a message, check keywords in WorkflowKeywords table
                // First get active workflow IDs
                // Then find matching keywords for active workflows

                if (!string.IsNullOrEmpty(textMessage) || !string.IsNullOrWhiteSpace(textMessage))
                {

                    var matchingKeywords = await _dbContext.WorkflowKeywords
                       .Include(k => k.Workflow)
                       .Where(k => !k.IsDeleted &&
                            k.BusinessId == contact.BusinessId.ToString() &&
                            k.Keyword.ToLower() == textMessage.ToLower() &&
                            k.Workflow.IsActive)
                       .Select(k => new
                       {
                           k.WorkflowId,
                           WorkflowNodeId = Guid.Parse(k.WorkflowNodeId.ToString()),
                           k.Keyword,
                           k.Workflow
                       })
                       .ToListAsync();

                    if (matchingKeywords.Any())
                    {
                        // Get the matching nodes directly using WorkflowNodeId
                        var matchingNodes = await _dbContext.WorkflowNodes
                            .Where(n => matchingKeywords.Select(k => k.WorkflowNodeId).Contains(n.Id) && n.Type == NodeType.FlowStart && !n.IsDeleted).ToListAsync();

                        if (matchingNodes.Any())
                        {
                            var selectedNode = SelectPriorityWorkflow(matchingNodes);
                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;
                                return selectedNode;
                            }
                        }
                    }

                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error finding starting node for customer {CustomerId}",
                    contact.ContactId);
                return null;
            }
        }
        private WorkflowNode SelectPriorityWorkflow(List<WorkflowNode> nodes)
        {
            if (nodes == null || !nodes.Any())
                throw new ArgumentException("No nodes provided for selection");

            var byCreationDate = nodes.OrderByDescending(n => n.CreatedAt).FirstOrDefault();
            if (byCreationDate != null)
                return byCreationDate;

            return nodes.First();
        }

        private async Task<Guid?> ProcessNodeAsync(WorkflowNode node, Contacts contact, string? textMessage)
        {

            try
            {
                Guid? nextNode = null;

                switch (node.Type)
                {
                    case NodeType.FlowStart:
                        nextNode = await ProcessFlowStartNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.InteractiveMessage:
                        nextNode = await ProcessInteractiveMessageNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.HttpRequest:
                        nextNode = await ProcessHttpRequestNodeAsync(node, contact);
                        break;
                    case NodeType.Template:
                        nextNode = await ProcessTemplateNodeAsync(node, contact);
                        break;
                    case NodeType.Condition:
                        nextNode = await ProcessConditionNodeAsync(node, contact, textMessage);
                        break;
                    default:
                        _logger.LogWarning("Unknown node type: {NodeType}", node.Type);
                        break;
                }
                if (nextNode != null)
                {
                    return nextNode;
                }
                var hasOutgoingEdges = await _dbContext.WorkflowEdges.AnyAsync(e => e.SourceId == node.Id);

                if (!hasOutgoingEdges)
                {
                    await MarkWorkflowCompletedAsync(contact);
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }

        }
        private async Task MarkWorkflowCompletedAsync(Contacts contact)
        {
            try
            {
                var activeTrackers = await _dbContext.CustomerWorkflowTrackers
              .Where(t =>
                t.ContactId == contact.ContactId &&
                t.WorkflowId == contact.WorkflowId &&
               !t.CompletedAt.HasValue)
               .ToListAsync();
                if (activeTrackers.Any())
                {
                    foreach (var tracker in activeTrackers)
                    {
                        tracker.CompletedAt = DateTime.UtcNow;
                        _dbContext.CustomerWorkflowTrackers.Update(tracker);
                    }
                }
                contact.WorkflowId = null;
                contact.WorkFlowNodeId = null;
                _dbContext.Contacts.Update(contact);

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {

            }
        }
        private async Task<Guid?> ProcessFlowStartNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                if (edge == null || !edge.Targets.Any())
                {

                    return null;
                }
                var nextNodeId = edge.Targets.First().TargetNodeId;

                return nextNodeId;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task<Guid?> ProcessInteractiveMessageNodeAsync(WorkflowNode node, Contacts contact, string? textMessage)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var interactiveMessage = node.PayloadModel?.InteractiveMessage;
                if (interactiveMessage == null)
                {
                    _logger.LogWarning("Interactive message payload is null for node {NodeId}", node.Id);
                    return null;
                }
                var customerResponse = await _workflowCustomResponseService.GetWorkflowCustomResponseAsync(
                    node.Id,
                    node.WorkflowId,
                    contact.BusinessId
                );

                string? attributeName = null;
                if (customerResponse != null && customerResponse.AttributeId.HasValue)
                {
                    attributeName = await _attributeNameService.GetAttributeNameByIdAsync(customerResponse.AttributeId.Value);
                }

                if (!string.IsNullOrEmpty(textMessage))
                {

                    var existingTracker = await _dbContext.CustomerWorkflowTrackers
                     .FirstOrDefaultAsync(t =>
                         t.ContactId == contact.ContactId &&
                         t.WorkflowId == node.WorkflowId &&
                         t.NodeId == node.Id &&
                         t.CompletedAt == null);

                    if (existingTracker != null)
                    {
                        existingTracker.CustomerReponse = textMessage;
                        existingTracker.AttributeName = attributeName;
                        existingTracker.UpdatedAt = DateTime.UtcNow;

                        _dbContext.CustomerWorkflowTrackers.Update(existingTracker);
                    }
                    else
                    {
                        var tracker = new CustomerWorkflowTracker
                        {
                            Id = Guid.NewGuid(),
                            BusinessId = contact.BusinessId,
                            ContactId = contact.ContactId,
                            WorkflowId = node.WorkflowId,
                            NodeId = node.Id,
                            NodeType = node.Type,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CustomerReponse = textMessage,
                            AttributeName = attributeName
                        };
                        await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);
                    }
                    try
                    {
                        await _dbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                    }

                    var edges = await _dbContext.WorkflowEdges
                        .Include(e => e.Targets)
                        .Where(e => e.SourceId == node.Id)
                        .ToListAsync();

                    if (!edges.Any())
                    {
                        return null;
                    }

                    var targets = edges.SelectMany(e => e.Targets).ToList();

                    if (!targets.Any())
                    {
                        return null;
                    }

                    if (targets.Count == 1)
                    {
                        return targets[0].TargetNodeId;
                    }

                    if (targets.Count > 1)
                    {
                        foreach (var target in targets)
                        {
                            if (string.IsNullOrEmpty(target.Condition))
                                continue;

                            var conditionParts = target.Condition.Split(new[] { "==" }, StringSplitOptions.RemoveEmptyEntries);
                            if (conditionParts.Length != 2)
                                continue;

                            var expectedValue = conditionParts[1].Trim().Trim('\'', '"');

                            if (textMessage.Equals(expectedValue, StringComparison.OrdinalIgnoreCase))
                            {
                                var targetNode = await _dbContext.WorkflowNodes.FindAsync(target.TargetNodeId);
                                return target.TargetNodeId;
                            }
                        }
                        var defaultTarget = targets.FirstOrDefault(t => string.IsNullOrEmpty(t.Condition));
                        if (defaultTarget != null)
                        {
                            return defaultTarget.TargetNodeId;
                        }
                    }
                    return targets[0].TargetNodeId;
                }
                else
                {
                    var existingTracker = await _dbContext.CustomerWorkflowTrackers
                   .FirstOrDefaultAsync(t =>
                    t.ContactId == contact.ContactId &&
                    t.WorkflowId == node.WorkflowId &&
                    t.NodeId == node.Id &&
                    t.CompletedAt == null);

                    if (existingTracker != null)
                    {
                        existingTracker.CustomerReponse = textMessage;
                        existingTracker.AttributeName = attributeName;
                        existingTracker.UpdatedAt = DateTime.UtcNow;

                        _dbContext.CustomerWorkflowTrackers.Update(existingTracker);
                        await _dbContext.SaveChangesAsync();
                    }
                    else
                    {
                        var tracker = new CustomerWorkflowTracker
                        {
                            Id = Guid.NewGuid(),
                            BusinessId = contact.BusinessId,
                            ContactId = contact.ContactId,
                            WorkflowId = node.WorkflowId,
                            NodeId = node.Id,
                            NodeType = node.Type,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CustomerReponse = textMessage,
                            AttributeName = attributeName
                        };

                        await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);

                        try
                        {
                            await _dbContext.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                    var account = await _dbContext.BusinessDetailsMetas
                        .FirstOrDefaultAsync(x => x.BusinessId == contact.BusinessId.ToString());

                    if (account == null)
                    {
                        return null;
                    }

                    var phoneNumber = $"{contact.CountryCode}{contact.Contact}";

                    var bodyMessage = await ProcessInteractiveMessageVariablesAsync(interactiveMessage, contact);

                    try
                    {
                        if (interactiveMessage.Type == InteractiveType.Button && interactiveMessage.Buttons != null && interactiveMessage.Buttons.Any())
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, interactiveMessage.Header, interactiveMessage.Type, interactiveMessage.Buttons, null);

                            string actionJson = JsonConvert.SerializeObject(new
                            {
                                buttons = interactiveMessage.Buttons.Select(b => new
                                {
                                    type = "reply",
                                    reply = new
                                    {
                                        id = b.Id,
                                        title = b.Name
                                    }
                                }).ToList()
                            });

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                MessageType = MessageType.Interactive,
                                TextMessage = bodyMessage,
                                Action = actionJson,
                                QuickReplies = String.Join(",", interactiveMessage.Buttons),
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();
                        }
                        else if (interactiveMessage.Type == InteractiveType.List &&
                                interactiveMessage.List != null)
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, interactiveMessage.Header, interactiveMessage.Type, null, interactiveMessage.List);

                            string actionJson = JsonConvert.SerializeObject(new
                            {
                                type = "list",
                                button = interactiveMessage.List.ButtonText,
                                sections = interactiveMessage.List.Sections.Select(s => new
                                {
                                    title = s.Title,
                                    rows = s.Rows.Select(r => new
                                    {
                                        id = r.Id,
                                        title = r.Title,
                                        description = r.Description
                                    }).ToList()
                                }).ToList()
                            });

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                MessageType = MessageType.Interactive,
                                TextMessage = bodyMessage,
                                Action = actionJson,
                                QuickReplies = String.Join(",", interactiveMessage.List),
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();

                        }
                        else
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, null, interactiveMessage.Type, null, null);

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                MessageType = MessageType.Normal,
                                TextMessage = bodyMessage,
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };
                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private string ResolveVariableValue(string variable, string? userResponse, string? variableValue, string? fallbackValue, Contacts? contact = null)
        {
            // Priority 1: User Response
            if (!string.IsNullOrEmpty(userResponse))
            {
                return userResponse;
            }

            // Priority 2: Contact Property based on attribute name
            if (contact != null && !string.IsNullOrEmpty(variableValue))
            {
                // Get the property value from contact based on attribute name
                var propertyInfo = typeof(Contacts).GetProperty(variableValue);
                if (propertyInfo != null)
                {
                    var value = propertyInfo.GetValue(contact);
                    if (value != null)
                    {
                        // Special handling for phone number
                        if (variableValue.Equals("Contact", StringComparison.OrdinalIgnoreCase))
                        {
                            return $"{contact.CountryCode}{value}";
                        }
                        return value.ToString();
                    }
                }
            }

            // Priority 3: Variable Value
            if (!string.IsNullOrEmpty(variableValue))
            {
                return variableValue;
            }

            // Priority 4: Fallback Value
            return fallbackValue ?? "Unknown";
        }

        private async Task<string> ReplaceVariablesInJsonBody(string jsonBody, List<VariableModel> variableValues, Contacts contact)
        {
            try
            {
                if (jsonBody.StartsWith("\"") && jsonBody.EndsWith("\""))
                {
                    jsonBody = JsonConvert.DeserializeObject<string>(jsonBody);
                }

                foreach (var variable in variableValues)
                {
                    if (!string.IsNullOrEmpty(variable.Variable))
                    {
                        var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contact.ContactId, contact.WorkflowId);

                        var matchingTracker = previousResponses.FirstOrDefault(t => t.AttributeName != null && t.AttributeName.Equals(variable.Value, StringComparison.OrdinalIgnoreCase));

                        // Priority: 1. User Response, 2. Contact Property, 3. Variable Value, 4. Fallback
                        string replacementValue = ResolveVariableValue(variable.Variable, matchingTracker?.CustomerReponse, variable.Value, variable.FallbackValue, contact);
                        jsonBody = jsonBody.Replace(variable.Variable, replacementValue);
                    }
                }
                var parsedJson = JsonConvert.DeserializeObject(jsonBody);
                return JsonConvert.SerializeObject(parsedJson);
            }
            catch (Exception ex)
            {
                return jsonBody;
            }
        }

        private string ReplaceVariablesInBody(string body, List<VariableModel>? variables, Contacts contact)
        {
            if (string.IsNullOrEmpty(body) || variables == null || !variables.Any())
                return body;

            string processedBody = body;
            foreach (var variable in variables)
            {
                if (!string.IsNullOrEmpty(variable.Variable))
                {
                    string value = variable.Value;
                    if (contact != null)
                    {
                        // Try to get the value from contact properties
                        var property = contact.GetType().GetProperty(value);
                        if (property != null)
                        {
                            var propertyValue = property.GetValue(contact);
                            if (property.Name.Equals("ScheduledAt", StringComparison.OrdinalIgnoreCase))
                            {
                                value = TimeZoneInfo.ConvertTimeFromUtc((DateTime)propertyValue, TimeZoneInfo.Local).ToString("dd-MM-yyyy h:mm tt");

                            }
                            else
                            {
                                value = property.GetValue(contact)?.ToString() ?? variable.FallbackValue;
                            }
                        }
                    }
                    processedBody = processedBody.Replace(variable.Variable, value ?? variable.FallbackValue);
                }
            }
            return processedBody;
        }

        /// <summary>
        /// Process interactive message variables using EXACT same pattern as campaigns
        /// Similar to ProcessTemplateWithLeadratVariable in your campaign system
        /// </summary>
        private async Task<string> ProcessInteractiveMessageVariablesAsync(InteractiveMessageModel interactiveMessage, Contacts contact)
        {
            try
            {
                var messageBody = interactiveMessage.Body;
                var variables = interactiveMessage.Variables;

                if (variables == null || !variables.Any())
                {
                    return messageBody;
                }

                var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);

                if (!extractedVariables.Any())
                {
                    return messageBody;
                }

                // Check if any variables are Leadrat variables (same logic as campaigns)
                var hasLeadratVariables = variables.Any(v => StringHelper.IsLeadratVariable(v.Value));

                if (!hasLeadratVariables)
                {
                    // Process with local variables only (similar to ProcessTemplateWithLocalVariable)
                    return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                }
                else
                {
                    // Process with Leadrat variables (similar to ProcessTemplateWithLeadratVariable)
                    return await ProcessInteractiveMessageWithLeadratVariablesAsync(messageBody, variables, extractedVariables, contact);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing interactive message variables, using fallback");
                return ReplaceVariablesInBody(interactiveMessage.Body, interactiveMessage.Variables, contact);
            }
        }

        /// <summary>
        /// Process with local variables only (similar to ProcessTemplateWithLocalVariable)
        /// </summary>
        private string ProcessInteractiveMessageWithLocalVariables(string messageBody, List<VariableModel> variables, Contacts contact)
        {
            try
            {
                var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);

                if (!extractedVariables.Any())
                {
                    return messageBody;
                }
                var contactProperties = typeof(Contacts).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(contact)?.ToString() ?? string.Empty);
                var variableValuesList = new List<string>();

                foreach (var extractedVar in extractedVariables)
                {
                    var matchingVariable = variables.FirstOrDefault(v => v.Variable == extractedVar);
                    if (matchingVariable != null)
                    {
                        var value = contactProperties.ContainsKey(matchingVariable.Value) && !string.IsNullOrEmpty(contactProperties[matchingVariable.Value])
                            ? contactProperties[matchingVariable.Value]
                            : matchingVariable.FallbackValue;
                        variableValuesList.Add(value);
                    }
                    else
                    {
                        variableValuesList.Add(extractedVar); // Fallback to original variable
                    }
                }
                return StringHelper.ReplacePlaceholders(updatedMessage, variableValuesList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing local variables");
                return messageBody;
            }
        }

        /// <summary>
        /// Process with Leadrat variables (similar to ProcessTemplateWithLeadratVariable)
        /// </summary>
        private async Task<string> ProcessInteractiveMessageWithLeadratVariablesAsync(string messageBody, List<VariableModel> variables, List<string> extractedVariables, Contacts contact)
        {
            try
            {
                 var businessDetails =    await _dbContext.BusinessDetails.FirstOrDefaultAsync(i => i.Id == contact.BusinessId);

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Add("tenant", businessDetails.TenantId);
    
                var response = await httpClient.GetAsync(LeadratApiUrls.GetLeadApiUrl(contact.Contact,  contact.CountryCode));

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var jsonResponse = string.IsNullOrWhiteSpace(jsonString) ? new JObject() : JsonConvert.DeserializeObject<JObject>(jsonString);
                    var apiDataDict = ObjectHelper.ConvertJObjectToDictionary(jsonResponse);

                    var variableValuesList = new List<string>();
                    foreach (var extractedVar in extractedVariables)
                    {
                        var matchingVariable = variables.FirstOrDefault(v => v.Variable == extractedVar);
                        if (matchingVariable != null)
                        {
                            var value = GetValueFromApiData(apiDataDict, matchingVariable.Value) ?? matchingVariable.FallbackValue;
                            variableValuesList.Add(value);
                        }
                        else
                        {
                            variableValuesList.Add(extractedVar); 
                        }
                    }
                    return StringHelper.ReplacePlaceholders(messageBody, variableValuesList);
                }
                else
                {
                    return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                }
            }
            catch (Exception ex)
            {
                return ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
            }
        }

        private string GetValueFromApiData(Dictionary<string, object> apiData, string keyPath)
        {
            try
            {
                // Support nested keys like "customer.name" or simple keys like "name"
                var keys = keyPath.Split('.');
                object currentValue = apiData;

                foreach (var key in keys)
                {
                    if (currentValue is Dictionary<string, object> dict && dict.ContainsKey(key))
                    {
                        currentValue = dict[key];
                    }
                    else
                    {
                        return null;
                    }
                }

                return currentValue?.ToString();
            }
            catch
            {
                return null;
            }
        }

        private async Task<Guid?> ProcessHttpRequestNodeAsync(WorkflowNode node, Contacts contacts)
        {
            var httpRequest = node.PayloadModel.HttpRequest;
            if (httpRequest == null)
            {
                return null;
            }
            try
            {
                var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contacts.ContactId, contacts.WorkflowId);
                var variableValues = new List<VariableModel>();
                if (httpRequest.VariableValues != null)
                {
                    foreach (var variable in httpRequest.VariableValues)
                    {
                        var matchingTracker = previousResponses
                         .FirstOrDefault(t => t.AttributeName != null &&
                                  t.AttributeName.Equals(variable.Value, StringComparison.OrdinalIgnoreCase));

                        var variableModel = new VariableModel
                        {
                            Variable = variable.Variable,
                            Value = matchingTracker?.CustomerReponse ?? variable.Value,
                            FallbackValue = variable.FallbackValue
                        };
                        variableValues.Add(variableModel);
                    }
                }

                string processedJsonBody = httpRequest.JsonBody;
                if (!string.IsNullOrEmpty(processedJsonBody))
                {
                    processedJsonBody = await ReplaceVariablesInJsonBody(processedJsonBody, variableValues, contacts);
                }
                using var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromSeconds(30);

                var request = new HttpRequestMessage(
                    new HttpMethod(httpRequest.Method),
                    httpRequest.Url
                );

                if (httpRequest.Headers != null)
                {
                    foreach (var header in httpRequest.Headers)
                    {
                        if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                            continue;

                        request.Headers.Add(header.Key, header.Value.ToString());
                    }
                }
                if (httpRequest.QueryParameters != null)
                {
                    var queryString = string.Join("&",
                        httpRequest.QueryParameters.Select(kvp =>
                            $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value.ToString())}"));

                    if (!string.IsNullOrEmpty(queryString))
                    {
                        request.RequestUri = new Uri($"{httpRequest.Url}?{queryString}");
                    }
                }

                if (!string.IsNullOrEmpty(processedJsonBody))
                {
                    request.Content = new StringContent(
                        processedJsonBody,
                        Encoding.UTF8,
                        "application/json"
                    );
                }
                else if (httpRequest.FormData != null && httpRequest.FormData.Any())
                {
                    var formContent = new MultipartFormDataContent();
                    foreach (var item in httpRequest.FormData)
                    {
                        formContent.Add(new StringContent(item.Value.ToString()), item.Key);
                    }
                    request.Content = formContent;
                }
                var response = await client.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                if (!response.IsSuccessStatusCode)
                {
                    // return null;
                    return edge?.Targets.FirstOrDefault()?.TargetNodeId;
                }
                return edge?.Targets.FirstOrDefault()?.TargetNodeId;
            }
            catch (HttpRequestException ex)
            {
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task<Guid?> ProcessTemplateNodeAsync(WorkflowNode node, Contacts contacts)
        {
            var template = new TemplateModel();
            try
            {
                template = node.PayloadModel.Template;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            if (template == null)
            {
                return null;
            }

            try
            {
                var business = await _dbContext.BusinessDetailsMetas
                    .FirstOrDefaultAsync(b => b.BusinessId == contacts.BusinessId.ToString());

                if (business == null)
                {
                    return null;
                }

                var templateDetails = (await _genericRepository.GetByObjectAsync<Template>(new Dictionary<string, object> { { "BusinessId", business.BusinessId }, { "TemplateId", template.TemplateId ?? Guid.Empty } }, "Templates"))
                          .FirstOrDefault();
                var buttons = await _genericRepository.GetByObjectAsync<Button>(new Dictionary<string, object> { { "TemplateId", template.TemplateId ?? Guid.Empty } }, "ButtonDetails");

                if (templateDetails == null)
                {
                    return null;
                }
                var phoneNumber = $"{contacts.CountryCode}{contacts.Contact}";

                var bodyVariables = template.BodyVariableValues ?? new List<string>();
                var headerVariable = template.HeaderValue ?? string.Empty;

                var templateRequest = new TemplateRequestDto
                {
                    TemplateName = templateDetails.TemplateName,
                    CountryCode = contacts.CountryCode,
                    Contact = contacts.Contact,
                    Name = contacts.Name,
                    HeaderValue = headerVariable,
                    BodyVariableValues = bodyVariables
                };

                Guid companyId = Guid.Parse(business.BusinessId);
                var templateService = _serviceProvider.GetRequiredService<ITemplate>();

                var conv = await templateService.GetSendTemplateAsync(business.BusinessId, companyId, templateRequest);

                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                return edge?.Targets.FirstOrDefault()?.TargetNodeId;
            }
            catch (Exception ex)
            {

                return null;
            }
        }
        private async Task<Guid?> ProcessConditionNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var condition = node.PayloadModel?.Condition;
                if (condition == null)
                {
                    return null;
                }
                var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contacts.ContactId, contacts.WorkflowId);
                if (previousResponses == null || !previousResponses.Any())
                {
                    return null;
                }

                var matchingTracker = previousResponses
                                    .FirstOrDefault(t => t.AttributeName != null &&
                                     t.AttributeName.Equals(condition.Attribute, StringComparison.OrdinalIgnoreCase));

                if (matchingTracker == null)
                {

                    matchingTracker = previousResponses
                        .OrderByDescending(t => t.CreatedAt)
                        .FirstOrDefault(t => t.NodeType == NodeType.InteractiveMessage && !string.IsNullOrEmpty(t.CustomerReponse));
                }
                var edges = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .Where(e => e.SourceId == node.Id)
                    .ToListAsync();

                if (!edges.Any())
                {
                    return null;
                }

                var allTargets = edges.SelectMany(e => e.Targets).ToList();

                var conditionButtons = condition.Buttons;
                if (conditionButtons != null && conditionButtons.Any())
                {
                    var buttonTargetMap = new Dictionary<string, WorkflowEdgeTargetNode>();
                    foreach (var target in allTargets)
                    {
                        // Extract the button ID from the source handle
                        var sourceHandle = edges.FirstOrDefault(e => e.Targets.Contains(target))?.SourceHandle;
                        if (sourceHandle != null && sourceHandle.Contains("conditionButtonId-"))
                        {
                            var buttonId = sourceHandle.Split("conditionButtonId-").Last();
                            buttonTargetMap[buttonId] = target;
                        }
                    }

                    // Find the true path button
                    var trueButton = conditionButtons.FirstOrDefault(b => b.Name.Contains("True", StringComparison.OrdinalIgnoreCase));
                    var falseButton = conditionButtons.FirstOrDefault(b => b.Name.Contains("False", StringComparison.OrdinalIgnoreCase));

                    if (trueButton != null && falseButton != null &&
                        buttonTargetMap.ContainsKey(trueButton.Id) &&
                        buttonTargetMap.ContainsKey(falseButton.Id))
                    {
                        bool conditionResult = false;

                        if (matchingTracker != null)
                        {
                            switch (condition.Operator)
                            {
                                case ConditionOperator.Equals:
                                    conditionResult = string.Equals(matchingTracker.CustomerReponse, condition.Value, StringComparison.OrdinalIgnoreCase);
                                    break;
                                case ConditionOperator.NotEquals:
                                    conditionResult = !string.Equals(matchingTracker.CustomerReponse, condition.Value, StringComparison.OrdinalIgnoreCase);
                                    break;
                                case ConditionOperator.GreaterThan:
                                    conditionResult = double.TryParse(matchingTracker.CustomerReponse, out double inputNum) &&
                                                    double.TryParse(condition.Value, out double conditionNum) &&
                                                    inputNum > conditionNum;
                                    break;
                                case ConditionOperator.LessThan:
                                    conditionResult = double.TryParse(matchingTracker.CustomerReponse, out double inputNum2) &&
                                                    double.TryParse(condition.Value, out double conditionNum2) &&
                                                    inputNum2 < conditionNum2;
                                    break;
                                case ConditionOperator.Contains:
                                    conditionResult = matchingTracker.CustomerReponse?.Contains(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                case ConditionOperator.StartsWith:
                                    conditionResult = matchingTracker.CustomerReponse?.StartsWith(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                case ConditionOperator.EndsWith:
                                    conditionResult = matchingTracker.CustomerReponse?.EndsWith(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                default:
                                    conditionResult = false;
                                    break;
                            }
                        }

                        var targetButton = conditionResult ? trueButton : falseButton;
                        var targetNodeId = buttonTargetMap[targetButton.Id].TargetNodeId;
                        return targetNodeId;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

    }
}
