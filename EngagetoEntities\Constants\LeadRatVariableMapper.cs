﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EngagetoEntities.Constants
{
    public class LeadRatVariableMapper
    {
        public static Dictionary<string, string> LeadVariableMapping = new()
        {
            { "#leadName#", "Name" },
            { "#LeadName#", "Name" },
            { "#leadContact#", "ContactNo" },
            { "#LeadContact#", "ContactNo" },
            { "#LeadEmail#", "Email" },
            { "#leadEmail#", "Email" },
            { "#leadCreatedOn#", "CreatedOn" },
            { "#propertyName#", "Properties[0].Title" },
            { "#PropertyName#", "Properties[0].Title" },
            { "#note#", "Notes" },
            { "#Note#", "Notes" },
            { "#noOfBhk#", "Properties[0].NoOfBHK" },
            { "#NoOfBhk#", "Properties[0].NoOfBHK" },
            { "#bhkType#", "Properties[0].BHKType" },
            { "#BhkType#", "Properties[0].BHKType" },
            { "#BhkTypes#", "Properties[0].BHKType" },
            { "#AssignedTo#", "AssignTo" },
            { "#AlternateContactNo#", "AlternateContactNo" },
            { "#leadCreatedBy#", "CreatedBy" },
            { "#enquiredFor#", "Properties[0].EnquiredFor" },
            { "#AssignedBy#", "AssignedFrom" },
            { "#leadSource#", "Enquiries[0].LeadSource" },
            { "#enquiredLocation#", "Enquiries[0].Address" },
            { "#enquiredType#", "Enquiries[0].EnquiredFor" },
            { "#Project#", "Projects[0].Name" },
            { "#date#", "ScheduledDate"},
            { "#Schedule Date#","ScheduledDate"},
            { "#ScheduleTime#","ScheduledDate" },
            { "#projectName#", "Projects[0].Name"},
            { "#Projects#","Projects[0].Name" },
            { "#Properties#","Properties[0].Title" },
            { "#LowerBudget#", "Enquiry.LowerBudget"},
            { "#UpperBudget#", "Enquiry.UpperBudget"},
            { "#enquiryType#", "Enquiry.EnquiryTypes[0]"},
            { "#EnquiryTypes#","Enquiry.EnquiryTypes[0]"},
            { "#EnquiredFor#", "Properties[0].EnquiredFor"},
            { "#Lead Source#", "Enquiries[0].LeadSource"},
            { "#Sub Source#", "Enquiry.PropertyType.ChildType.DisplayName"},
            { "#Property Type#", "PropertyType.DisplayName"},
            { "#time#", "ScheduledDate" },
            { "#propertyType#","PropertyType.DisplayName"},
            { "#propertySubtype#","Enquiry.PropertyType.ChildType.DisplayName" },
            { "#carpetArea#","Enquiry.CarpetArea,Enquiry.CarpetAreaUnit" },
            { "#CarpetArea#","Enquiry.CarpetArea,Enquiry.CarpetAreaUnit" },
            { "#ChannelPartnerName#", "ChannelPartners[0].FirmName" },
            { "#channelPartnerName#", "ChannelPartners[0].FirmName" }
        };
        /// <summary>
        /// Enhanced User Details Mapping for Agent Variables
        /// Maps agent-related variables to User Details API response fields
        /// </summary>
        public static Dictionary<string, string> UserDetailsMapping = new()
        {
            // Agent Contact Information
            {"#agentName#", "data.userName"},
            {"#agentEmail#", "data.email"},
            {"#agentMobile#", "data.phoneNumber"},
            {"#userEmail#", "data.email"},
            {"#userMobile#", "data.phoneNumber"},

            // Agent Personal Details
            {"#firstName#", "data.firstName"},
            {"#lastName#", "data.lastName"},
            {"#userName#", "data.userName"},
            {"#UserName#", "data.userName"},

            // Agent Organization Details
            {"#department#", "data.department.name"},
            {"#role#", "data.designation.name"},
            {"#designation#", "data.designation.name"},

            // Manager Information
            {"#managerName#", "data.reportsTo.name"},
            {"#managerPhone#", "data.reportsTo.contactNo"},

            // Office Information
            {"#officeName#", "data.officeName"},
            {"#officeAddress#", "data.officeAddress"},

            // Legacy mappings (for backward compatibility)
            {"#userPhoneNumber#", "data.phoneNumber"},
            {"#UserPhoneNumber#", "data.phoneNumber"},
            {"#UserEmail#", "data.email"},
            {"#Assign To#", "data.firstName,data.lastName"},
            {"#Primary Owner#", "data.firstName,data.lastName"}
        };

        /// <summary>
        /// Check if a variable is an agent/user variable that should be processed via User Details API
        /// </summary>
        public static bool IsAgentVariable(string variable)
        {
            return UserDetailsMapping.ContainsKey(variable);
        }

        /// <summary>
        /// Get the field path for an agent variable
        /// </summary>
        public static string GetAgentVariableFieldPath(string variable)
        {
            return UserDetailsMapping.TryGetValue(variable, out var fieldPath) ? fieldPath : null;
        }

        /// <summary>
        /// Get all supported agent variables
        /// </summary>
        public static IEnumerable<string> GetSupportedAgentVariables()
        {
            return UserDetailsMapping.Keys;
        }

    }
}
