﻿namespace EngagetoEntities.Dtos.TemplateDtos
{
    public class SendTemplateDto
    {
        public string BusinessId { get; set; }
        public Guid UserId { get; set; }
        public Guid TemplateId { get; set; }
        public List<string> Contact { get; set; }
        //public string? HeaderVariableValue { get; set; }
        // public string[]? BodyVariableValues { get; set; }
        public string[]? RedirectUrlVariableValues { get; set; }
        public string? MadiaUrl { get; set; }
        public string? MediaFile { get; set; }
    }



}
