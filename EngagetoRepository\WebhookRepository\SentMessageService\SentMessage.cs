﻿using Engageto.Hubs;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoRepository.WebhookRepository.WhatsAppBusinessEndpointUrl;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;

namespace EngagetoRepository.WebhookRepository.SentMessageService
{
    public class SentMessage : ISentMessage
    {
        private readonly JsonSerializer _serializer = new JsonSerializer();
        private readonly HttpClient _httpClient = new HttpClient();
        private readonly ApplicationDbContext _appDbContext;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IHubContext<MessageHub, IMessageHubClient> messageHub;
        private readonly IConfiguration configuration;
        private IJobService _jobService { get; set; }

        public SentMessage(IJobService jobService, IConfiguration config, IHubContext<MessageHub, IMessageHubClient> _messageHub, ApplicationDbContext appDbContext, IHttpClientFactory httpClient)
        {
            this._appDbContext = appDbContext;
            _httpClientFactory = httpClient;
            messageHub = _messageHub;
            configuration = config;
            _jobService = jobService;
            //whatsAppReceiveNotification = data;
        }




        public async Task<object> SendMessageAsync(TextMessageRequest textMessage, Guid BusinessId)
        {
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).PhoneNumberID);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(textMessage, formattedWhatsAppEndpoint, BusinessId);
        }
        public async Task<object> SendMessageAsync(ImageMessageRequest Message, Guid BusinessId)
        {
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).PhoneNumberID);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId);
        }
        public async Task<object> SendMessageAsync(AudioMessageRequest Message, Guid BusinessId)
        {
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).PhoneNumberID);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId);
        }

        public async Task<object> SendMessageAsync(VideoMessageRequest Message, Guid BusinessId)
        {
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).PhoneNumberID);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId);
        }
        public async Task<object> SendMessageAsync(DocumentMessageRequest Message, Guid BusinessId)
        {
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).PhoneNumberID);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId);
        }
        private async Task<object> WhatsAppBusinessPostAsync<T>(TextMessageRequest whatsAppDto, string whatsAppBusinessEndpoint, Guid BusinessId)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).Token);
            WhatsAppResponse result = new();
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            var data = new object();
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                data = await InsertSentTextMessage(result, whatsAppDto.Text.Body, whatsAppDto.Context?.MessageId, BusinessId);
            }
            else
            {
                // Handle error response
                // Log or handle the error appropriately
            }
            return data;
        }
        private async Task<object> WhatsAppBusinessPostAsync<T>(ImageMessageRequest whatsAppDto, string whatsAppBusinessEndpoint, Guid BusinessId)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).Token);
            WhatsAppResponse result = new();
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            var data = new object();
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                data = await InsertSentMediaMessage(result, whatsAppDto.Image.Link, whatsAppDto.Image.Caption, whatsAppDto.Context?.MessageId, BusinessId);
            }
            else
            {
                throw new Exception("While sending message we are getting error.");
                // Handle error response
                // Log or handle the error appropriately
            }
            return data;
        }
        private async Task<object> WhatsAppBusinessPostAsync<T>(AudioMessageRequest whatsAppDto, string whatsAppBusinessEndpoint, Guid BusinessId)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).Token);
            WhatsAppResponse result = new();
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            var data = new object();
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                data = await InsertSentMediaMessage(result, whatsAppDto.Audio.Link, "", whatsAppDto.Context?.MessageId, BusinessId);
            }
            else
            {
                throw new Exception("While sending message we are getting error.");
                // Handle error response
                // Log or handle the error appropriately
            }
            return result;
        }
        private async Task<object> WhatsAppBusinessPostAsync<T>(VideoMessageRequest whatsAppDto, string whatsAppBusinessEndpoint, Guid BusinessId)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).Token);
            WhatsAppResponse result = new();
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            var data = new object();
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                data = await InsertSentMediaMessage(result, whatsAppDto.Video.Link, whatsAppDto.Video.Caption, whatsAppDto.Context?.MessageId, BusinessId);
            }
            else
            {
                throw new Exception("While sending message we are getting error.");
                // Handle error response
                // Log or handle the error appropriately
            }
            return data;
        }
        private async Task<object> WhatsAppBusinessPostAsync<T>(DocumentMessageRequest whatsAppDto, string whatsAppBusinessEndpoint, Guid BusinessId)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString()).Token);
            WhatsAppResponse result = new();
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            var data = new object();
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                data = await InsertSentMediaMessage(result, whatsAppDto.Document, whatsAppDto.Context?.MessageId, BusinessId);
            }
            else
            {
                throw new Exception("While sending message we are getting error.");
            }
            return data;
        }

        public object? GetReplyItems(IEnumerable<Conversations>? query, string? replyId)
        {
            if (replyId != null)
            {
                return query?.Where(m => m.WhatsAppMessageId == replyId)?.Select(i => new
                {
                    Id = i.Id,
                    WhatsAppMessageId = i.WhatsAppMessageId,
                    From = i.From,
                    To = i.To,
                    Status = i.Status.ToString(),
                    CreatedAt = i.CreatedAt,
                    TextMessage = i.TextMessage,
                    MediaFileName = i.MediaFileName,
                    MediaMimeType = i.MediaMimeType,
                    MediaUrl = i.MediaUrl,
                    MediaCaption = i.MediaCaption,
                    TemplateMediaType = i.TemplateMediaType,
                    TemplateMediaFile = i.TemplateMediaUrl,
                    TemplateHeader = i.TemplateHeader,
                    TemplateBody = i.TemplateBody,
                    TemplateFooter = i.TemplateFooter,
                    CallButtonName = i.CallButtonName,
                    PhoneNumber = i.PhoneNumber,
                    UrlButtonNames = (i.UrlButtonNames ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(name => name.Trim())
                    .Where(name => !string.IsNullOrEmpty(name))
                    .ToArray(),
                    RedirectUrls = (i.RedirectUrls ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(name => name.Trim())
                    .Where(name => !string.IsNullOrEmpty(name))
                    .ToArray(),
                    QuickReplies = (i.QuickReplies ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(name => name.Trim())
                    .Where(name => !string.IsNullOrEmpty(name))
                    .ToArray()
                }).FirstOrDefault();
            }
            return null;
        }
        private async Task<object> InsertSentTextMessage(WhatsAppResponse response, string TextBody, string? ReplyId, Guid BusinessId)
        {




            try
            {
                Conversations sentMessage = new Conversations();
                sentMessage.WhatsAppMessageId = response.Messages[0].Id;
                sentMessage.From = BusinessId.ToString();
                sentMessage.To = response.Contacts[0].WaId;
                sentMessage.Status = EngagetoEntities.Enums.ConvStatus.sent;
                sentMessage.TextMessage = TextBody;
                sentMessage.ReplyId = ReplyId;
                sentMessage.MessageType = EngagetoEntities.Enums.MessageType.Normal;

                sentMessage.CreatedAt = DateTime.UtcNow;
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;
                List<ConversationDto> conversations = new List<ConversationDto>();
              
                var Obj = _appDbContext.Conversations.Add(sentMessage);
                _appDbContext.SaveChanges();
                List<Conversations> query = new List<Conversations>();
                if (sentMessage.ReplyId != null)
                {
                    query = _appDbContext.Conversations?.Where(m => m.WhatsAppMessageId == sentMessage.ReplyId)?.ToList();
                }

                ConversationDto obj = new ConversationDto();
                obj.Id = Obj.Entity.Id;
                obj.WhatsAppMessageId = Obj.Entity.WhatsAppMessageId;
                obj.To = Obj.Entity.To;
                obj.From = Obj.Entity.From;
                obj.Reply = GetReplyItems(query, Obj.Entity.ReplyId); ;
                obj.Status = Obj.Entity.Status.ToString();
                obj.CreatedAt = Obj.Entity.CreatedAt;
                obj.TextMessage = Obj.Entity.TextMessage;
                obj.MediaCaption = Obj.Entity.MediaCaption;
                obj.MediaFileName = Obj.Entity.MediaFileName;
                obj.MediaUrl = Obj.Entity.MediaUrl;
                obj.MediaMimeType = Obj.Entity.MediaMimeType;
                obj.TemplateMediaType = Obj.Entity.TemplateMediaType;
                obj.TemplateBody = Obj.Entity.TemplateBody;
                obj.TemplateHeader = Obj.Entity.TemplateHeader;
                obj.TemplateFooter = Obj.Entity.TemplateFooter;
                obj.TemplateMediaFile = Obj.Entity.TemplateMediaUrl;
                obj.CallButtonName = Obj.Entity.CallButtonName;
                obj.PhoneNumber = Obj.Entity.PhoneNumber;
                obj.UrlButtonNames = (Obj.Entity.UrlButtonNames ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                obj.RedirectUrls = (Obj.Entity.RedirectUrls ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                obj.QuickReplies = (Obj.Entity.QuickReplies ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;


                var Contact = _appDbContext.Contacts.FirstOrDefault(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && ((m.CountryCode + m.Contact) == "+" + Obj.Entity.To));
                if (Contact != null)
                {
                    Response(Contact.ContactId);
                }

                conversations.Add(obj);

                // Convert DateTime to string without timezone specifier
                var data = _appDbContext.Users.Where(m => m.CompanyId == BusinessId.ToString());
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                    }

                }
                return Obj.Entity;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private async Task<object> InsertSentMediaMessage(WhatsAppResponse response, string Link, string Caption, string ReplyId, Guid BusinessId)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                var result = await client.GetAsync(Link);
                var contentType = result.Content.Headers.ContentType?.MediaType;
                Conversations sentMessage = new Conversations();
                sentMessage.WhatsAppMessageId = response.Messages[0].Id;
                sentMessage.From = BusinessId.ToString();
                sentMessage.To = response.Contacts[0].WaId;
                sentMessage.Status = EngagetoEntities.Enums.ConvStatus.sent;
                sentMessage.MediaMimeType = contentType;
                sentMessage.MediaUrl = Link;
                sentMessage.MediaCaption = Caption;
                sentMessage.CreatedAt = DateTime.UtcNow;
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");
                sentMessage.MessageType = EngagetoEntities.Enums.MessageType.Normal;

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;
                List<ConversationDto> conversations = new List<ConversationDto>();
                var Obj = _appDbContext.Conversations.Add(sentMessage);
                _appDbContext.SaveChanges();
                List<Conversations> query = new List<Conversations>();
                if (sentMessage.ReplyId != null)
                {
                    query = _appDbContext.Conversations?.Where(m => m.WhatsAppMessageId == sentMessage.ReplyId)?.ToList();
                }
                ConversationDto obj = new ConversationDto();
                obj.Id = Obj.Entity.Id;
                obj.WhatsAppMessageId = Obj.Entity.WhatsAppMessageId;
                obj.To = Obj.Entity.To;
                obj.From = Obj.Entity.From;
                obj.Reply = GetReplyItems(query, Obj.Entity.ReplyId); ;
                obj.Status = Obj.Entity.Status.ToString();
                obj.CreatedAt = Obj.Entity.CreatedAt;
                obj.TextMessage = Obj.Entity.TextMessage;
                obj.MediaCaption = Obj.Entity.MediaCaption;
                obj.MediaFileName = Obj.Entity.MediaFileName;
                obj.MediaUrl = Obj.Entity.MediaUrl;
                obj.MediaMimeType = Obj.Entity.MediaMimeType;
                obj.TemplateMediaType = Obj.Entity.TemplateMediaType;
                obj.TemplateBody = Obj.Entity.TemplateBody;
                obj.TemplateHeader = Obj.Entity.TemplateHeader;
                obj.TemplateFooter = Obj.Entity.TemplateFooter;
                obj.TemplateMediaFile = Obj.Entity.TemplateMediaUrl;
                obj.CallButtonName = Obj.Entity.CallButtonName;
                obj.PhoneNumber = Obj.Entity.PhoneNumber;
                obj.UrlButtonNames = (Obj.Entity.UrlButtonNames ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                obj.RedirectUrls = (Obj.Entity.RedirectUrls ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                obj.QuickReplies = (Obj.Entity.QuickReplies ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                conversations.Add(obj);
                var Contact = _appDbContext.Contacts.Where(m => m.IsActive).FirstOrDefault(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && ((m.CountryCode + m.Contact) == "+" + Obj.Entity.To));
                if (Contact != null)
                {
                    Response(Contact.ContactId);
                }

                var data = _appDbContext.Users.Where(m => m.CompanyId == BusinessId.ToString());
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                    }

                }
                return Obj.Entity;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        private async Task<object> InsertSentMediaMessage(WhatsAppResponse response, WhatsAppDocument document, string ReplyId, Guid BusinessId)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                var result = await client.GetAsync(document.Link);
                var contentType = result.Content.Headers.ContentType?.MediaType;
                Conversations sentMessage = new Conversations();
                sentMessage.WhatsAppMessageId = response.Messages[0].Id;
                sentMessage.From = BusinessId.ToString();
                sentMessage.To = response.Contacts[0].WaId;
                sentMessage.Status = EngagetoEntities.Enums.ConvStatus.sent;
                sentMessage.MediaMimeType = contentType;
                sentMessage.MediaUrl = document.Link;
                sentMessage.MediaCaption = document.Caption;
                sentMessage.ReplyId = ReplyId;
                sentMessage.MediaFileName = document.FileName;
                sentMessage.MessageType = MessageType.Normal;

                sentMessage.CreatedAt = DateTime.UtcNow;
                List<ConversationDto> conversations = new List<ConversationDto>();
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;
                var Obj = _appDbContext.Conversations.Add(sentMessage);
                _appDbContext.SaveChanges();
                List<Conversations> query = new List<Conversations>();
                if (sentMessage.ReplyId != null)
                {
                    query = _appDbContext.Conversations?.Where(m => m.WhatsAppMessageId == sentMessage.ReplyId)?.ToList();
                }
                ConversationDto obj = new ConversationDto();
                obj.Id = Obj.Entity.Id;
                obj.WhatsAppMessageId = Obj.Entity.WhatsAppMessageId;
                obj.To = Obj.Entity.To;
                obj.From = Obj.Entity.From;
                obj.Reply = GetReplyItems(query, Obj.Entity.ReplyId); ;
                obj.Status = Obj.Entity.Status.ToString();
                obj.CreatedAt = Obj.Entity.CreatedAt;
                obj.TextMessage = Obj.Entity.TextMessage;
                obj.MediaCaption = Obj.Entity.MediaCaption;
                obj.MediaFileName = Obj.Entity.MediaFileName;
                obj.MediaUrl = Obj.Entity.MediaUrl;
                obj.MediaMimeType = Obj.Entity.MediaMimeType;
                obj.TemplateMediaType = Obj.Entity.TemplateMediaType;
                obj.TemplateBody = Obj.Entity.TemplateBody;
                obj.TemplateHeader = Obj.Entity.TemplateHeader;
                obj.TemplateFooter = Obj.Entity.TemplateFooter;
                obj.TemplateMediaFile = Obj.Entity.TemplateMediaUrl;
                obj.CallButtonName = Obj.Entity.CallButtonName;
                obj.PhoneNumber = Obj.Entity.PhoneNumber;
                obj.UrlButtonNames = (Obj.Entity.UrlButtonNames ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                obj.RedirectUrls = (Obj.Entity.RedirectUrls ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                obj.QuickReplies = (Obj.Entity.QuickReplies ?? string.Empty).Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(name => name.Trim()) // Trim to remove any surrounding whitespace
                .Where(name => !string.IsNullOrEmpty(name)) // Ensure no empty strings are included
                .ToArray(); ;
                conversations.Add(obj);
                var Contact = _appDbContext.Contacts.Where(m => m.IsActive).FirstOrDefault(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && ((m.CountryCode + m.Contact) == "+" + Obj.Entity.To));
                if (Contact != null)
                {
                    Response(Contact.ContactId);
                }
                var data = _appDbContext.Users.Where(m => m.CompanyId == BusinessId.ToString());
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();

                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                    }

                }
                return Obj.Entity;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }
        #region Delay Response Delete Object If We Got Response From Client
        public void Response(Guid ContactId)
        {
            var Contact = _appDbContext.Contacts.Where(m => m.ContactId == ContactId).FirstOrDefault();

            if (Contact.DelayResponseJobID != null & _jobService.JobState(Contact.DelayResponseJobID) == "Scheduled")
            {
                _jobService.Delete(Contact.DelayResponseJobID);
                Contact.DelayResponseJobID = null;
                _appDbContext.Contacts.Update(Contact);
                _appDbContext.SaveChanges();
            }
        }
        #endregion
    }
}
