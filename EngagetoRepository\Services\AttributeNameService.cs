using EngagetoContracts.AttributeName;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AttributeNameDtos;
using EngagetoEntities.Entities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.Services
{
    public class AttributeNameService : IAttributeNameService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<AttributeNameService> _logger;
        private readonly IUserIdentityService _userIdentityService;

        public AttributeNameService(
            ApplicationDbContext dbContext,
            ILogger<AttributeNameService> logger,
            IUserIdentityService userIdentityService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _userIdentityService = userIdentityService;
        }

        public async Task<AttributeNameDtos> CreateAttributeNameAsync(CreateAttributeNameDtos attributeNameDto, Guid userId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                // Check if attribute name already exists
                var existingAttributeName = await _dbContext.AttributeNameEntity
                    .FirstOrDefaultAsync(a => a.Name == attributeNameDto.Name && 
                                            a.BusinessId == businessId && 
                                            !a.IsDeleted);
                if (existingAttributeName != null)
                {
                    throw new Exception("Attribute name already exists");
                }
                var newAttributeName = new AttributeNameEntity
                {
                    Id = Guid.NewGuid(),
                    Name = attributeNameDto.Name,
                    UserId = attributeNameDto.UserId,
                    IsExist = true,
                    BusinessId = businessId,
                    CreatedBy = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedBy = userId,
                    UpdatedAt = DateTime.UtcNow
                };

                _dbContext.AttributeNameEntity.Add(newAttributeName);
                await _dbContext.SaveChangesAsync();

                return newAttributeName.Adapt<AttributeNameDtos>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating attribute name");
                throw;
            }
        }

        public async Task<AttributeNameDtos> UpdateAttributeNameAsync(Guid id, UpdateAttributeNameDtos attributeNameDto, Guid userId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var attributeName = await _dbContext.AttributeNameEntity
                    .FirstOrDefaultAsync(a => a.Id == id && 
                                            a.BusinessId == businessId && 
                                            !a.IsDeleted);

                if (attributeName == null)
                {
                    throw new Exception("Attribute name not found");
                }

                // Check if the new name conflicts with existing attribute names
                if (attributeName.Name != attributeNameDto.Name)
                {
                    var nameExists = await _dbContext.AttributeNameEntity
                        .AnyAsync(a => a.Name == attributeNameDto.Name && 
                                     a.BusinessId == businessId && 
                                     !a.IsDeleted && 
                                     a.Id != id);

                    if (nameExists)
                    {
                        throw new Exception("Attribute name already exists");
                    }
                }

                attributeName.Name = attributeNameDto.Name;
                attributeName.UserId = attributeName.UserId;
                attributeName.IsExist = attributeNameDto.IsExist;
                attributeName.UpdatedBy = userId;
                attributeName.UpdatedAt = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();

                return attributeName.Adapt<AttributeNameDtos>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attribute name");
                throw;
            }
        }

        public async Task<bool> DeleteAttributeNameAsync(Guid id, Guid userId)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;

                var attributeName = await _dbContext.AttributeNameEntity
                    .FirstOrDefaultAsync(a => a.Id == id && 
                                            a.BusinessId == businessId && 
                                            !a.IsDeleted);

                if (attributeName == null)
                {
                    throw new Exception("Attribute name not found");
                }

                attributeName.IsDeleted = true;
                attributeName.UpdatedBy = userId;
                attributeName.UpdatedAt = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attribute name");
                throw;
            }
        }

        public async Task<List<AttributeNameDtos>> GetAllAttributeNamesAsync()
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var attributeNames = await _dbContext.AttributeNameEntity
                    .Where(a => (a.BusinessId == businessId || a.BusinessId == null) && !a.IsDeleted)
                    .OrderByDescending(a => a.UpdatedAt)
                    .ToListAsync();

                return attributeNames.Adapt<List<AttributeNameDtos>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all attribute names");
                throw;
            }
        }

        public async Task<AttributeNameDtos> GetAttributeNameByIdAsync(Guid id,  Guid? businessId =  null)
        {
            try
            {
                var attributeName =  _dbContext.AttributeNameEntity
                    .Where(a => (a.BusinessId == businessId.ToString() || a.BusinessId == null) && a.Id == id);
                
                if (attributeName == null)
                {
                    throw new Exception("Attribute name not found");
                }

                return attributeName.Adapt<AttributeNameDtos>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attribute name by ID");
                throw;
            }
        }

        public async Task<string?> GetAttributeNameByIdAsync(Guid id)
        {
            try
            {
                var attributeName = await _dbContext.AttributeNameEntity
                    .Where(a =>a.Id == id && !a.IsDeleted)
                    .Select(a => a.Name)
                    .FirstOrDefaultAsync();
                return attributeName ?? null ; 
            }
            catch(Exception ex)
            {
                throw;
            }
        }
    }
} 