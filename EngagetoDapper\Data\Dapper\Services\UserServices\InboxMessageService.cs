﻿using EngagetoContracts.ContactContracts;
using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoDapper.Data.Interfaces;
using EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices;
using EngagetoDapper.Data.Interfaces.CommonInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoDatabase.WhatsAppBusinessDatabase.Models;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Utilities;
using Mapster;
using Newtonsoft.Json;
using System.Text;
using System.Text.RegularExpressions;

namespace EngagetoDapper.Data
{
    public class InboxMessageService : IInboxMessageService
    {
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ISentMessage _sentMessage;
        private readonly DbAa80b1WhatsappbusinessContext _DbAa80b1WhatsappbusinessContext;
        private readonly ApplicationDbContext _dbContext;
        private readonly ICommonService _commonService;
        public IInboxService _inboxDapperService;
        private readonly IConversationsService _conversationsService;
        private readonly IConversationService _conversationService;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IWhatsAppBusinessNotificarion _whatsAppBusinessNotification;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;

        public InboxMessageService(IWhatsAppBusinessClient whatsAppBusinessClient,
            IHttpClientFactory httpClient,
            ISentMessage sentMessage,
            DbAa80b1WhatsappbusinessContext DbAa80b1WhatsappbusinessContext,
            ApplicationDbContext dbContext, ICommonService commonService,
            IInboxService inboxDapperService,
            IConversationsService conversationsService,
            IConversationService conversationService, IContactRepositoryBase contactRepository,
            IWhatsAppBusinessNotificarion whatsAppBusinessNotification, EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService)
        {
            _whatsAppBusinessClient = whatsAppBusinessClient;
            _httpClientFactory = httpClient;
            _sentMessage = sentMessage;
            _DbAa80b1WhatsappbusinessContext = DbAa80b1WhatsappbusinessContext;
            _dbContext = dbContext;
            _commonService = commonService;
            _inboxDapperService = inboxDapperService;
            _conversationsService = conversationsService;
            _conversationService = conversationService;
            _contactRepository = contactRepository;
            _whatsAppBusinessClient = whatsAppBusinessClient;
            _userService = userService;
            _whatsAppBusinessNotification = whatsAppBusinessNotification;
        }
        public async Task<List<Object>> TextOrEmojiMessageAsync(TextData data, Guid businessId)
        {
            var results = new List<object>();
            if (data.Contact == null || data.Contact.Count == 0 || data.Contact.Any(string.IsNullOrWhiteSpace))
            {
                throw new ArgumentException("Contacts are not provided or invalid.");
            }
            var regex = new Regex(@"^\+?[1-9]\d{1,14}$");
            foreach (var contact in data.Contact)
            {
                if (!regex.IsMatch(contact))
                {
                    throw new ArgumentException("Each contact must be a valid phone number.");
                }
            }
            var result = new object();
            foreach (var contact in data.Contact)
            {
                if (string.IsNullOrWhiteSpace(contact))
                {
                    throw new ArgumentException("One or more contacts are not provided.");
                }

                if (data.TextMessage != null && data.TextMessage != "")
                {
                    TextMessageRequest textMessageRequest = new TextMessageRequest();
                    textMessageRequest.To = contact;
                    textMessageRequest.Text = new WhatsAppText();
                    textMessageRequest.Text.Body = data.TextMessage;
                    textMessageRequest.Text.PreviewUrl = false;
                    result = await _whatsAppBusinessClient.SendMessageAsync(textMessageRequest, businessId);
                }

                results.Add(result);
            }

            return results;
        }

        public async Task<List<Object>> SendMediaMessageAsync(MediaData data, Guid businessId)
        {
            var results = new List<object>();

            var result = new object();

            if (data.Contact == null || data.Contact.Count == 0)
            {
                throw new Exception("Contact is not found.");
            }
            var regex = new Regex(@"^\+?[1-9]\d{1,14}$");
            foreach (var contact in data.Contact)
            {
                if (!regex.IsMatch(contact))
                {
                    throw new Exception("Each contact must be a valid phone number.");
                }
            }
            foreach (var Contact in data.Contact)
            {
                if (data.MediaUrl != null)
                {
                    var client = _httpClientFactory.CreateClient();
                    var response = await client.GetAsync(data.MediaUrl);

                    if (response.IsSuccessStatusCode)
                    {
                        var contentType = response.Content.Headers.ContentType?.MediaType;
                        Uri uri = new Uri(data.MediaUrl);
                        Path.GetFileName(uri.LocalPath);
                        var fileName = Path.GetFileName(uri.LocalPath);

                        if (contentType != null)
                        {
                            // Check media type based on content type
                            if (contentType.StartsWith("image"))
                            {
                                ImageMessageRequest imageMessageRequest = new ImageMessageRequest();
                                imageMessageRequest.To = Contact;
                                imageMessageRequest.Image = new WhatsAppImage();
                                imageMessageRequest.Image.Link = data.MediaUrl;
                                imageMessageRequest.Image.Caption = data.Caption;

                                result = await _whatsAppBusinessClient.SendMessageAsync(imageMessageRequest, businessId);
                                // Image file
                                // Handle image processing or save to local storage
                            }
                            else if (contentType.StartsWith("video"))
                            {
                                VideoMessageRequest videoMessageRequest = new VideoMessageRequest();
                                videoMessageRequest.To = Contact;
                                videoMessageRequest.Video = new WhatsAppVideo();
                                videoMessageRequest.Video.Link = data.MediaUrl;
                                videoMessageRequest.Video.Caption = data.Caption;


                                result = await _whatsAppBusinessClient.SendMessageAsync(videoMessageRequest, businessId);
                                // Video file
                                // Handle video processing or save to local storage
                            }
                            else if (contentType.StartsWith("audio"))
                            {
                                AudioMessageRequest audioMessageRequest = new AudioMessageRequest();
                                audioMessageRequest.To = Contact;
                                audioMessageRequest.Audio = new WhatsAppAudio();
                                audioMessageRequest.Audio.Link = data.MediaUrl;

                                result = await _whatsAppBusinessClient.SendMessageAsync(audioMessageRequest, businessId);
                                // Audio file
                                // Handle audio processing or save to local storage
                            }
                            else if (contentType.StartsWith("text/plain") ||
                                contentType.StartsWith("application/pdf") ||
                                contentType.StartsWith("application/vnd.ms-powerpoint") ||
                                contentType.StartsWith("application/msword") ||
                                contentType.StartsWith("application/vnd.ms-excel") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.presentationml.presentation") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                            {
                                DocumentMessageRequest documentMessageRequest = new DocumentMessageRequest();
                                documentMessageRequest.To = Contact;
                                documentMessageRequest.Document = new WhatsAppDocument();
                                documentMessageRequest.Document.Link = data.MediaUrl;
                                documentMessageRequest.Document.Caption = data.Caption;
                                documentMessageRequest.Document.FileName = fileName;

                                result = await _whatsAppBusinessClient.SendMessageAsync(documentMessageRequest, businessId);

                            }
                            else
                            {
                                throw new Exception("Unsupported media type.");
                            }
                        }
                        else
                        {
                            throw new Exception("Unable to find the content type.");
                        }
                    }
                    else
                    {
                        throw new Exception("Unable to find the content type.");
                    }
                }
                else
                {
                    throw new Exception("Unable to find the content type.");
                }
                results.Add(result);
            }
            return results;
        }

        public async Task<List<Object>> SendWhatsappMediaAsync(MediaData data, Object companyId)
        {
            var results = new List<object>();
            if (data.Contact == null || !data.Contact.Any())
            {
                throw new Exception("Contact is not found.");
            }
            var regex = new Regex(@"^\+?[1-9]\d{9,14}$");
            foreach (var contact in data.Contact)
            {
                if (!regex.IsMatch(contact))
                {
                    throw new Exception("Each contact must be a valid phone number.");
                }
            }

            var BusinessId = Guid.Parse(companyId.ToString() ?? string.Empty);
            var result = new object();
            foreach (var Contact in data.Contact)
            {
                if (data.MediaUrl != null)
                {
                    var client = _httpClientFactory.CreateClient();
                    var response = await client.GetAsync(data.MediaUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        var contentType = response.Content.Headers.ContentType?.MediaType;
                        Uri uri = new Uri(data.MediaUrl);
                        Path.GetFileName(uri.LocalPath);
                        var fileName = Path.GetFileName(uri.LocalPath);

                        if (contentType != null)
                        {
                            // Check media type based on content type
                            if (contentType.StartsWith("image"))
                            {
                                ImageMessageRequest imageMessageRequest = new ImageMessageRequest();
                                imageMessageRequest.To = Contact;
                                imageMessageRequest.Image = new WhatsAppImage();
                                imageMessageRequest.Image.Link = data.MediaUrl;
                                imageMessageRequest.Image.Caption = data.Caption ?? string.Empty;

                                result = await _sentMessage.SendMessageAsync(imageMessageRequest, BusinessId);
                                // Image file
                                // Handle image processing or save to local storage
                            }
                            else if (contentType.StartsWith("video"))
                            {
                                VideoMessageRequest videoMessageRequest = new VideoMessageRequest();
                                videoMessageRequest.To = Contact;
                                videoMessageRequest.Video = new WhatsAppVideo();
                                videoMessageRequest.Video.Link = data.MediaUrl;
                                videoMessageRequest.Video.Caption = data.Caption ?? string.Empty;


                                result = await _sentMessage.SendMessageAsync(videoMessageRequest, BusinessId);
                                // Video file
                                // Handle video processing or save to local storage
                            }
                            else if (contentType.StartsWith("audio"))
                            {
                                AudioMessageRequest audioMessageRequest = new AudioMessageRequest();
                                audioMessageRequest.To = Contact;
                                audioMessageRequest.Audio = new WhatsAppAudio();
                                audioMessageRequest.Audio.Link = data.MediaUrl;


                                result = await _sentMessage.SendMessageAsync(audioMessageRequest, BusinessId);
                                // Audio file
                                // Handle audio processing or save to local storage
                            }
                            else if (contentType.StartsWith("text/plain") ||
                                contentType.StartsWith("application/pdf") ||
                                contentType.StartsWith("application/vnd.ms-powerpoint") ||
                                contentType.StartsWith("application/msword") ||
                                contentType.StartsWith("application/vnd.ms-excel") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.presentationml.presentation") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                            {
                                DocumentMessageRequest documentMessageRequest = new DocumentMessageRequest();
                                documentMessageRequest.To = Contact;
                                documentMessageRequest.Document = new WhatsAppDocument();
                                documentMessageRequest.Document.Link = data.MediaUrl;
                                documentMessageRequest.Document.Caption = data.Caption;
                                documentMessageRequest.Document.FileName = fileName;


                                result = await _sentMessage.SendMessageAsync(documentMessageRequest, BusinessId);

                            }
                            else
                            {
                                throw new Exception("Unsupported media type.");
                            }
                        }
                        else
                        {
                            throw new Exception("Unsupported media type.");
                        }
                    }
                    else
                    {
                        throw new Exception("Unable to find the Media content type");
                    }
                }
                else
                {
                    throw new Exception("Unable to find the Media content type");
                }
                results.Add(result);
            }
            return results;
        }

        public async Task<List<Object>> SendWhatsappTextAsync(TextData data, Object companyId)
        {
            var results = new List<Object>();
            if (data.Contact == null || !data.Contact.Any())
                throw new Exception("Contact is not found.");

            var regex = new Regex(@"^\+?[1-9]\d{9,14}$");
            foreach (var contact in data.Contact)
            {
                if (!regex.IsMatch(contact))
                    throw new Exception("Each contact must be a valid phone number.");
            }
            var BusinessId = Guid.Parse(companyId.ToString() ?? string.Empty);
            var result = new object();
            foreach (var Contact in data.Contact)
            {
                if (Contact == null)
                    throw new Exception("Contact is not found.");

                if (data.TextMessage != null && data.TextMessage != "")
                {
                    TextMessageRequest textMessageRequest = new TextMessageRequest();
                    textMessageRequest.To = Contact;
                    textMessageRequest.Text = new WhatsAppText();
                    textMessageRequest.Text.Body = data.TextMessage;
                    textMessageRequest.Text.PreviewUrl = false;
                    result = await _sentMessage.SendMessageAsync(textMessageRequest, BusinessId);
                }
                results.Add(result);
            }
            return results;
        }

        public async Task<Object> GetUserDetailsAsync(Guid businessId, string contact)
        {
            var UserDetails = _DbAa80b1WhatsappbusinessContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact1).Replace("+", "") == contact) && (m.BusinessId == businessId) && (m.IsActive));
            var Tags = new List<string>();
            List<object> Note = new List<object>();
            if (UserDetails != null)
            {

                if (!string.IsNullOrEmpty(UserDetails.Note))
                {
                    var Data = UserDetails.Note.Split(',').ToArray();
                    Note = _DbAa80b1WhatsappbusinessContext.Notes
                            .Where(m => Data.Contains(m.Id.ToString()))
                            .Select(m => new { Note = m.Note1, Id = m.Id }) // Selecting Note1 and Id properties
                            .Cast<object>() // Casting the anonymous objects to objects
                            .ToList();
                }

                if (UserDetails.Tags != null)
                {
                    var Data = UserDetails.Tags.Split(',').ToList();
                    Tags = _DbAa80b1WhatsappbusinessContext.Tags
                            .Where(m => Data.Contains(m.Id.ToString()))
                            .Select(m => m.Tag1) // Assuming 'NoteText' is the property you want to populate 'Note' with
                            .ToList();

                }

            }

            var formattedBusinessId = businessId.ToString().ToLower();
            var Conversation = _dbContext.Conversations
             .Where(m => ((m.From == contact && m.To.ToLower() == formattedBusinessId) || (m.To == contact && m.From.ToLower() == formattedBusinessId)))
             .OrderByDescending(m => m.CreatedAt)
             .Select(m => new { m.MediaUrl, m.MediaFileName, m.TextMessage })
             .ToArray();
            var Text = Conversation.Where(m => m.TextMessage != null).Select(m => m.TextMessage != null ? m.TextMessage : string.Empty).ToList();
            InboxMethods inboxMethods = new InboxMethods();
            var Media = Conversation.Where(m => m.MediaUrl != null).Select(m => new { m.MediaUrl, m.MediaFileName });

            var Link = inboxMethods.ExtractUrls(Text);
            string name = "";
            string ContactId = "";
            bool ContactExist = false;
            string email = "";
            string CountryName = "";
            if (UserDetails != null)
            {
                name = UserDetails.Name;
                email = UserDetails.Email ?? string.Empty;
                ContactExist = true;
                CountryName = UserDetails.CountryName ?? string.Empty;
                ContactId = UserDetails.ContactId.ToString();
            }
            var WorkflowVariables = await _commonService.WorkflowVariableResponse(UserDetails.ContactId);
            var userDetailsResponse = new
            {
                Name = name == "" ? contact : name,
                PhoneNumber = contact,
                Email = email,
                CountryName,
                ShareMedia = Media,
                Links = Link,
                ContactExist,
                ContactId,
                UserDetails.IsSpam,
                Tags,
                Note,
                WorkflowVariables
            };
            return userDetailsResponse;
        }

        public async Task<bool> AssignChatAsync(Guid businessId, Guid? assignUserId, ContactNumbers contacts, Guid currentUserId)
        {
            foreach (var item in contacts.Contact)
            {
                var data = _DbAa80b1WhatsappbusinessContext.Contacts.FirstOrDefault(m => ((m.CountryCode + m.Contact1).Replace("+", "") == item) && (m.BusinessId == businessId) && (m.IsActive));
                if (data != null)
                {
                    if (data.UserId?.ToString().ToLower() == assignUserId?.ToString().ToLower())
                    {
                        data.UserId = null;
                    }
                    else
                    {
                        data.UserId = assignUserId;
                    }

                    _DbAa80b1WhatsappbusinessContext.Contacts.Update(data);
                    _DbAa80b1WhatsappbusinessContext.SaveChanges();
                    await _inboxDapperService.SaveContactAssignmentAsync(businessId.ToString(), data.ContactId, currentUserId, data.UserId ?? Guid.Empty);
                }
                else
                {
                    var Contact = new EngagetoDatabase.WhatsAppBusinessDatabase.Models.Contact();
                    Contact.Contact1 = item.Substring(item.Length - 10);
                    string remainingString = item.Remove(item.Length - 10);
                    Contact.Name = "";
                    // Assuming the country code is the remaining part of the string
                    Contact.CountryCode = "+" + remainingString;
                    Contact.UserId = assignUserId;
                    Contact.BusinessId = businessId;
                    Contact.ChatStatus = ChatStatus.open;
                    Contact.IsActive = true;
                    Contact.IsSpam = false;
                    Contact.ContactId = Guid.NewGuid();
                    //Contact.CreatedDate = DateTime.UtcNow;
                    Contact.CreatedDate = DateTime.UtcNow;

                    _DbAa80b1WhatsappbusinessContext.Contacts.Add(Contact);
                    _DbAa80b1WhatsappbusinessContext.SaveChanges();
                    await _inboxDapperService.SaveContactAssignmentAsync(businessId.ToString(), Contact.ContactId, currentUserId, assignUserId ?? Guid.Empty);
                }
            }
            return true;
        }

        public async Task<(List<EngagetoEntities.Entities.Conversations>?, WhatsAppErrorResponse?)> SendTextEmojiMediaMessageAsync(TextMediaMassages messages, Object companyId)
        {
            var results = new List<Conversations>();
            string mediaType = string.Empty;
            string mimeType = string.Empty;
            string? caption = string.Empty;

            var metaAccount = await _userService.IsValidateMetaAccount(companyId.ToString() ?? string.Empty);

            await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(companyId.ToString() ?? string.Empty, messages.ContactNumbers.Count());

            var regex = new Regex(@"^\+?[1-9]\d{1,14}$", RegexOptions.Compiled);

            var invalidContacts = messages.ContactNumbers
                .Select((contact, index) => new { Contact = contact, Index = index })
                .Where(item => !regex.IsMatch(item.Contact))
                .ToList();

            if (invalidContacts.Any())
            {
                var errorMessage = string.Join(", ", invalidContacts.Select(item => $"Contact at index {item.Index}"));
                throw new InvalidOperationException($"Invalid phone numbers found: {errorMessage}");
            }

            var BusinessId = Guid.Parse(companyId.ToString() ?? string.Empty);
            Conversations conversation = new();
            if (messages.ReplyId != null && messages.ReplyId != Guid.Empty)
            {
                conversation = await _conversationService.GetMessageByIdAsync(messages.ReplyId ?? Guid.Empty) ?? new();
            }

            if (string.IsNullOrWhiteSpace(messages.Textmessage) && string.IsNullOrWhiteSpace(messages.MediaUrl))
            {
                throw new InvalidOperationException("Either textmessage or mediaUrl must be provided.");
            }

            foreach (var contact in messages.ContactNumbers)
            {
                var phoneNumberWithCountryCode = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(contact);
                await _contactRepository.SaveContactNumber(phoneNumberWithCountryCode.CountryCode, phoneNumberWithCountryCode.NationalNumber, BusinessId.ToString(), EngagetoEntities.Enums.SourceType.LeadRat);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", metaAccount?.Token);
                var apiUrl = MetaApi.GetSendTemplateUrl(metaAccount?.PhoneNumberID ?? string.Empty);
                string waPayload = null;
                if (string.IsNullOrEmpty(messages.MediaUrl) || messages.MediaUrl == "default")
                {
                    var textMessage = StringHelper.FormateEscapeSequences(messages.Textmessage ?? string.Empty);
                    messages.MediaUrl = null;
                    if (messages.ReplyId == null || messages.ReplyId == Guid.Empty || conversation is null)
                    {
                        waPayload = WAMessageDto.GetWAMessage(contact, textMessage);
                    }
                    else
                    {
                        waPayload = WAMessageDto.GetReplyWAMessage(conversation.WhatsAppMessageId, contact, textMessage);
                    }
                }
                else if (!string.IsNullOrEmpty(messages.MediaUrl))
                {
                    caption = StringHelper.FormateEscapeSequences(messages.Textmessage ?? string.Empty);
                    var mediaInfo = await GetMediaTypeAsync(messages.MediaUrl);
                    mimeType = mediaInfo.MimeType;
                    mediaType = mediaInfo.MediaType;

                    if (!string.IsNullOrEmpty(mediaInfo.MediaType))
                    {
                        if (messages.ReplyId == null || messages.ReplyId == Guid.Empty || conversation is null)
                        {
                            waPayload = WAMessageDto.GetWAMediaPayload(mediaInfo.MediaType, contact, messages.MediaUrl, caption ?? string.Empty);
                        }
                        else
                        {
                            waPayload = WAMessageDto.GetReplyWAMediaPayload(mediaInfo.Item2, conversation.WhatsAppMessageId, contact, messages.MediaUrl, caption ?? string.Empty);
                        }
                    }
                }
                if (waPayload != null)
                {
                    var content = new StringContent(waPayload, Encoding.UTF8, "application/json");
                    var response = await client.PostAsync(apiUrl, content);

                    if (response.IsSuccessStatusCode)
                    {
                        //Enum.TryParse(mediaType.ToUpperInvariant(), out TemplateMediatype type);
                        var responseContentString = await response.Content.ReadAsStringAsync();
                        var responseData = JsonConvert.DeserializeObject<WhatsAppResponse>(responseContentString);
                        var waMessageId = responseData?.Messages?[0].Id;
                        var conv = await _conversationService.SaveTextMediaConversationAsync(messages.Textmessage, companyId?.ToString(), contact, mediaType, caption, mimeType, messages.MediaUrl, waMessageId, conversation.WhatsAppMessageId);
                        results.Add(conv);
                    }
                    else
                    {
                        var contentError = await response.Content.ReadAsStringAsync();
                        var responseError = JsonConvert.DeserializeObject<WhatsAppErrorResponse>(contentError);
                        return (null, responseError);
                    }
                }
            }
            var resultDto = results.Adapt<List<ConversationDto>>();
            await _whatsAppBusinessNotification.RenderMessagesBySignalR(companyId?.ToString() ?? string.Empty, resultDto);
            //await _scheduledOperation.RunTaskAsync(resultDto.FirstOrDefault()?.Id ?? Guid.Empty, 7, CancellationToken.None);
            return (results, null);
        }

        private async Task<(string MimeType, string MediaType)> GetMediaTypeAsync(string url)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                    throw new InvalidOperationException("Failed to retrieve content from the provided URL.");

                var mimeType = response.Content.Headers.ContentType?.MediaType;
                if (string.IsNullOrEmpty(mimeType))
                    throw new InvalidOperationException("Content type not found.");

                var mediaType = "";
                if (mimeType.StartsWith("application/"))
                {
                    var documentTypes = new HashSet<string>
                {
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "text/plain"
                };

                    mediaType = documentTypes.Contains(mimeType) ? "document" : "Others";
                }

                var mediaFileType = mimeType.Split('/')[0]; // Example: "image/jpeg" -> "image"
                mediaType = mediaFileType switch
                {
                    "image" => "image",
                    "video" => "video",
                    "audio" => "audio",
                    _ => "Others"
                };
                return (mimeType, mediaType);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("The provided URL is not valid or accessible.", ex);
            }
        }
    }
}