{
  "ConnectionStrings": {
    "Connection": "Data Source=engageto.database.windows.net;Initial Catalog=qa-engageto;User Id=dbmasteruser;Password=************`H9hTJK5ojM)C=$C69,2;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;Min Pool Size=5;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "MetaConfig": {
    "baseUrl": /*"https://localhost:7153/api/"*/ "https://connect.engagetoq.in/api/",
    "statusUpdate": "WAWebhookMessage/sent-message",
    "receiveMessage": "WAWebhookMessage/receive-message",
    "templateUpdate": "WAWebhookMessage/update-template",
    "common": "WAWebhookMessage/receive-WAmessage"
  },
  "WebhookConfig": {
    "VerifyToken": "12345"
  }
}
