﻿using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Entities;
using EngagetoEntities.UserEntities.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;


namespace Engageto.Controllers.UserControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IAuthentication _authentication;
        private readonly ITokenService _tokenService;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _dbContext;
        private readonly IPermissionsService _permissionsService;
        private readonly IGenericRepository _genericRepository;


        public AuthenticationController(IAuthentication authentication, ITokenService tokenService, IConfiguration configuration, ApplicationDbContext dbContext, IGenericRepository genericRepository, IPermissionsService permissionsService = null)
        {
            _authentication = authentication;
            _tokenService = tokenService;
            _configuration = configuration;
            _dbContext = dbContext;
            _permissionsService = permissionsService;
            _genericRepository = genericRepository;
        }


        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestModel model)
        {
            try
            {

                var user = await _authentication.GetUserAsync(model.Email, model.Password);

                if (user == null)
                {
                    return Unauthorized(new { Message = "Invalid credentials. Please check your email and password." });
                }


                var token = _tokenService.GenerateToken(user);

                var manageAccount = await _dbContext.Users.FirstOrDefaultAsync(m => m.EmailAddress == model.Email);
                var created = manageAccount.CreationDate;
                if (manageAccount != null)
                {
                    var roleId = manageAccount.RoleId;


                    if (roleId != null)
                    {
                        var roleIdString = roleId.ToString();
                        var role = await _dbContext.Roles.FirstOrDefaultAsync(r => r.Id.ToString() == roleIdString);
                        var businessDetails_Meta = await _dbContext.BusinessDetailsMetas.FirstOrDefaultAsync(i => i.BusinessId == manageAccount.CompanyId);
                        bool isPlatform = manageAccount.CompanyId == RoleConstants.CompanyId;
                        var company = await _dbContext.BusinessDetails.FirstOrDefaultAsync(c => c.Id.ToString() == manageAccount.CompanyId);
                        var creationdate = company.CreatedAt;
                        var isDefaultUser = false;
                        if (creationdate == created)
                        {
                            isDefaultUser = true;
                        }
                        //var permissions = _permissionsService.GetMenuHierarchy(roleIdString, manageAccount.CompanyId);
                        var userImage = manageAccount.Image;

                        return Ok(new
                        {
                            Token = token,
                            Email = model.Email,
                            UserId = manageAccount.Id,
                            userImage = userImage,
                            CompanyId = manageAccount.CompanyId,
                            isPlatform = isPlatform,
                            isDefaultUser = isDefaultUser,
                            RoleId = manageAccount.RoleId,
                            RoleName = role.Name,
                            WAAccountId = businessDetails_Meta?.WhatsAppBusinessAccountID,
                            //Permissions = permissions,
                            Message = "Login successful."
                        });
                    }
                    else
                    {
                        return Ok(new
                        {
                            Token = token,
                            Email = model.Email,
                            UserId = manageAccount.Id,
                            CompanyId = manageAccount.CompanyId,
                            Message = "Login successful. Role information not found."
                        });
                    }
                }


                return Ok(new { Token = token, Email = model.Email, AccountId = 0, userId = user.Id, Message = "Login successful. Manage account is not registered." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal Server Error: {ex.Message}" });
            }
        }

        [HttpPost("loginWithTenant")]
        public async Task<IActionResult>LoginWithTenant(Guid businessId)
        {
            try
            {
                string roleId = "55aa1ba5-1507-47e2-888b-89d80cd41906";
                var businessDetail = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object> { { "Id", businessId } }))?.FirstOrDefault();
                if (businessDetail != null)
                {
                    var userDetail = (await _genericRepository.GetByObjectAsync<Ahex_CRM_Users>(new Dictionary<string, object> { { "CompanyId", businessDetail.Id.ToString() }, { "RoleId", roleId } }))?.FirstOrDefault();
                    if (userDetail != null)
                    {
                        var token = _tokenService.GenerateToken(userDetail);
                        var userImage = userDetail.Image;

                        return Ok(new
                        {
                            Token = token,
                            Email = userDetail.EmailAddress,
                            UserId = userDetail.Id,
                            userImage = userImage,
                            CompanyId = userDetail.CompanyId,
                            isPlatform = false,
                            isDefaultUser = true,
                            RoleId = userDetail.RoleId,
                            RoleName = userDetail.Name,
                            Message = "Login successful."
                        });
                    }
                }
                throw new Exception("Invalid tenant");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = ex.Message });
            }
        }
    }
}

