namespace EngagetoEntities.Enums
{
    /// <summary>
    /// Types of reminder workflow nodes
    /// </summary>
    public enum ReminderNodeType
    {
        /// <summary>
        /// Entry point for reminder workflow
        /// </summary>
        ReminderStart = 1,

        /// <summary>
        /// Schedule reminder template node
        /// </summary>
        ScheduleReminder = 2,

        /// <summary>
        /// Final node to complete reminder workflow
        /// </summary>
        ReminderComplete = 3
    }

    /// <summary>
    /// Predefined reminder intervals
    /// </summary>
    public enum ReminderInterval
    {
        /// <summary>
        /// 15 minutes before scheduled time
        /// </summary>
        FifteenMinutes = 15,

        /// <summary>
        /// 30 minutes before scheduled time
        /// </summary>
        ThirtyMinutes = 30,

        /// <summary>
        /// 1 hour before scheduled time
        /// </summary>
        OneHour = 60,

        /// <summary>
        /// 2 hours before scheduled time
        /// </summary>
        TwoHours = 120,

        /// <summary>
        /// 4 hours before scheduled time
        /// </summary>
        FourHours = 240,

        /// <summary>
        /// 1 day before scheduled time (24 hours)
        /// </summary>
        OneDay = 1440,

        /// <summary>
        /// 2 days before scheduled time (48 hours)
        /// </summary>
        TwoDays = 2880,

        /// <summary>
        /// 1 week before scheduled time (7 days)
        /// </summary>
        OneWeek = 10080
    }

    /// <summary>
    /// Status of scheduled reminder jobs
    /// </summary>
    public enum ReminderJobStatus
    {
        /// <summary>
        /// Job is scheduled and waiting to execute
        /// </summary>
        Scheduled = 1,

        /// <summary>
        /// Job is currently executing
        /// </summary>
        Processing = 2,

        /// <summary>
        /// Job completed successfully
        /// </summary>
        Completed = 3,

        /// <summary>
        /// Job failed to execute
        /// </summary>
        Failed = 4,

        /// <summary>
        /// Job was cancelled
        /// </summary>
        Cancelled = 5,

        /// <summary>
        /// Job was skipped (e.g., past due)
        /// </summary>
        Skipped = 6
    }
}
