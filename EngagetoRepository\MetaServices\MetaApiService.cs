﻿using Amazon.Runtime.Internal.Util;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;

namespace EngagetoRepository.MetaServices
{
    public class MetaApiService : IMetaApiService
    {
        private readonly IMetaPayloadService _metaPayloadService;
        private readonly ApplicationDbContext _contextDb;
        private readonly IUserIdentityService _userIdentityService;

        public MetaApiService(IMetaPayloadService metaPayloadService, ApplicationDbContext contextDb, IUserIdentityService userIdentityService)
        {
            _contextDb = contextDb;
            _metaPayloadService = metaPayloadService;
            _userIdentityService = userIdentityService;

        }

        public async Task<(bool Success, JObject Result)> CreateAuthTemplateRequestAsync(string businessId, JObject authTemplate)
        {
            try
            {
                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
                    ?? throw new UnauthorizedAccessException("Meta account is not found.");

                var url = MetaApi.GetCreateAuthTemplateUrl(account.WhatsAppBusinessAccountID);
                var json = JsonConvert.SerializeObject(authTemplate);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.Token);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, content);
                var contentString = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(contentString));
                }
                return (true, JObject.Parse(contentString));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<(bool Success, JObject Result)> SendCarouselTemplateAsync(string businessId, JObject template, string? waPhoneNumberId = null, string? token = null)
        {
            try
            {
                var url = "";
                var validToken = "";
                if (waPhoneNumberId == null)
                {
                    var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
                        ?? throw new UnauthorizedAccessException("Meta account is not found.");
                    url = MetaApi.GetSendTemplateUrl(account.PhoneNumberID);
                    token = account.Token;
                }
                else
                {
                    url = waPhoneNumberId != null ? MetaApi.GetSendTemplateUrl(waPhoneNumberId) : throw new UnauthorizedAccessException("Meta account is not found.");
                    validToken = token;
                }

                var json = JsonConvert.SerializeObject(template);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", validToken);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, content);
                var contentString = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(contentString));
                }
                return (true, JObject.Parse(contentString));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        public async Task<(bool Success, JObject Result)> SendAuthTemplateRequestAsync(string businessId, JObject authTemplate)
        {
            try
            {
                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
                    ?? throw new UnauthorizedAccessException("Meta account is not found.");

                var url = MetaApi.GetSendTemplateUrl(account.PhoneNumberID);
                var json = JsonConvert.SerializeObject(authTemplate);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.Token);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, content);
                var contentString = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(contentString));
                }
                return (true, JObject.Parse(contentString));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<(bool Success, JObject Result)> CreateTemplateRequestAsync(string businessId, JObject authTemplate)
        {
            try
            {
                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
                    ?? throw new UnauthorizedAccessException("Meta account is not found.");

                var url = MetaApi.GetCreateTemplateUrl(account.WhatsAppBusinessAccountID);
                var json = JsonConvert.SerializeObject(authTemplate);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.Token);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, content);
                var contentString = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(contentString));
                }
                return (true, JObject.Parse(contentString));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<ViewAuthTempaltePreviewDto?> GetAuthTemplatePreviewAsync(AuthTemplatePreviewRequestDto requestDto)
        {
            try
            {
                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == requestDto.CompanyId)
                    ?? throw new UnauthorizedAccessException("Meta account is not found.");

                var url = MetaApi.GetMessageTemplatePreview(account.WhatsAppBusinessAccountID);
                url += $"?category={requestDto.Category.ToString()}&languages={string.Join(",", requestDto.Languages)}&add_security_recommendation={requestDto.AddSecurityRecommendation}&button_types={requestDto.ButtonTypes}";
                if (requestDto.CodeExpirationMinutes > 0)
                {
                    url += $"&code_expiration_minutes={requestDto.CodeExpirationMinutes}";
                }

                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.Token);
                //if (cancellationToken.IsCancellationRequested)
                //{
                //    cancellationToken.ThrowIfCancellationRequested();
                //}
                var response = await client.GetAsync(url);
                if (!response.IsSuccessStatusCode)
                {
                    var contentString = await response.Content.ReadAsStringAsync();
                    throw new Exception(contentString);
                }
                var k = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ViewAuthTempaltePreviewDto>(await response.Content.ReadAsStringAsync());
                return result;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<HealthStatusDto?> GetHealthStatusAsync(string businessId)
        {
            try
            {
                if (!Guid.TryParse(businessId, out Guid businessGuidId))
                    return CreateDefaultHealthStatusDto(businessGuidId);

                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId);
                if (account == null)
                    return CreateDefaultHealthStatusDto(businessGuidId);

                var api = MetaApi.GetHealthStatusUrl(account.WhatsAppBusinessAccountID);

                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.Token);

                var response = await client.GetAsync(api);
                if (!response.IsSuccessStatusCode)
                    return CreateDefaultHealthStatusDto(businessGuidId);

                var content = await response.Content.ReadAsStringAsync();
                var jsonData = JsonConvert.DeserializeObject<JObject>(content);
                if (jsonData == null || jsonData["health_status"] is not JObject healthStatus)
                    return CreateDefaultHealthStatusDto(businessGuidId);

                return ParseHealthStatus(healthStatus, businessGuidId);
            }
            catch (HttpRequestException httpEx)
            {
                Console.WriteLine($"HTTP Request Error: {httpEx.Message}");
                return null;
            }
            catch (JsonException jsonEx)
            {
                Console.WriteLine($"JSON Parsing Error: {jsonEx.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                return null;
            }
        }

        public async Task<(bool IsSuccess, JObject Result)> SendTemplateAsync(string businessId, string language, string phoneNumber, string templateName, List<string>? bodyValues, MediaType mediaType, string? headerValue, string? token = null, string? waPhoneNumberId = null, string? waAccountId = null)
        {
            try
            {
                if (string.IsNullOrEmpty(waPhoneNumberId) || string.IsNullOrEmpty(waAccountId))
                {
                    var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId);
                    waPhoneNumberId = account?.PhoneNumberID;
                    waAccountId = account?.WhatsAppBusinessAccountID;
                    token = account?.Token;
                }
                var url = MetaApi.GetSendTemplateUrl(waPhoneNumberId);
                var basePayload = _metaPayloadService.InitializeBasePayload(phoneNumber, templateName, language);
                var components = _metaPayloadService.GetComponents();
                bodyValues = bodyValues?.Where(x => !string.IsNullOrEmpty(x)).ToList();
                // Added body component
                _metaPayloadService.AddBodyComponent(ref components, bodyValues);
                // Added header component
                _metaPayloadService.AddHeaderComponent(ref components, mediaType, headerValue);
                if (basePayload["template"] is JObject templateObject)
                {
                    templateObject["components"] = components;
                }

                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                var content = new StringContent(JsonConvert.SerializeObject(basePayload), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                    return (true, JObject.Parse(await response.Content.ReadAsStringAsync()));

                return (false, JObject.Parse(await response.Content.ReadAsStringAsync()));
            }
            catch (HttpRequestException httpEx)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<(bool IsSuccess, JObject Result)> SendTextWithMediaMessageAsync(string businessId, string phoneNumber, string text, string mediatype, string? mediaUrl, string? waMessageId, string? token = null, string? waPhoneNumberId = null, string? waAccountId = null)
        {
            try
            {
                text = StringHelper.FormateEscapeSequences(text ?? string.Empty);
                if (string.IsNullOrEmpty(waPhoneNumberId) || string.IsNullOrEmpty(waAccountId))
                {
                    var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId);
                    waPhoneNumberId = account?.PhoneNumberID;
                    waAccountId = account?.WhatsAppBusinessAccountID;
                    token = account?.Token;
                }
                string url = MetaApi.GetSendTemplateUrl(waPhoneNumberId);
                string waPayload = string.Empty;
                if (string.IsNullOrEmpty(mediaUrl) || mediaUrl == "default" || mediatype.ToLowerInvariant() == "text")
                {
                    mediaUrl = null;
                    if (string.IsNullOrEmpty(waMessageId))
                    {
                        waPayload = WAMessageDto.GetWAMessage(phoneNumber, text);
                    }
                    else
                    {
                        waPayload = WAMessageDto.GetReplyWAMessage(waMessageId, phoneNumber, text);
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(waMessageId))
                    {
                        waPayload = WAMessageDto.GetWAMediaPayload(mediatype.ToLowerInvariant(), phoneNumber, mediaUrl, text ?? string.Empty);
                    }
                    else
                    {
                        waPayload = WAMessageDto.GetReplyWAMediaPayload(mediatype.ToLowerInvariant(), waMessageId, phoneNumber, mediaUrl, text ?? string.Empty);
                    }
                }
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                var content = new StringContent(waPayload, Encoding.UTF8, "application/json");
                var response = await client.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                    return (true, JObject.Parse(await response.Content.ReadAsStringAsync()));

                return (false, JObject.Parse(await response.Content.ReadAsStringAsync()));

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private HealthStatusDto CreateDefaultHealthStatusDto(Guid businessGuidId)
        {
            return new HealthStatusDto()
            {
                BusinessId = businessGuidId,
                OverAllStatus = MetaAccountStatus.NOT_COMPLETED.ToString(),
                Entities = new List<HealthEntityDto>()
                {
                    new HealthEntityDto(MetaEntityType.WABA.ToString(), MetaAccountStatus.NOT_COMPLETED.ToString(), string.Empty,string.Empty),
                    new HealthEntityDto(MetaEntityType.BUSINESS.ToString(), MetaAccountStatus.NOT_COMPLETED.ToString(), string.Empty,string.Empty)
                }
            };
        }

        private HealthStatusDto ParseHealthStatus(JObject healthStatus, Guid businessGuidId)
        {
            var healthStatusDto = new HealthStatusDto()
            {
                BusinessId = businessGuidId,
                Entities = new List<HealthEntityDto>()
            };

            var entities = healthStatus["entities"] as JArray ?? new JArray();
            foreach (var entity in entities)
            {
                var entityType = entity["entity_type"]?.ToString() ?? string.Empty;
                var canSendMessage = entity["can_send_message"]?.ToString() ?? string.Empty;
                var errors = entity["errors"] as JArray ?? new JArray();

                if (entityType == MetaEntityType.APP.ToString())
                    break;

                if (entityType == MetaEntityType.WABA.ToString())
                    healthStatusDto.OverAllStatus = canSendMessage;

                ProcessErrors(entityType, canSendMessage, errors, healthStatusDto);
            }

            return healthStatusDto;
        }

        private void ProcessErrors(string entityType, string canSendMessage, JArray errors, HealthStatusDto healthStatusDto)
        {
            if (errors.Count == 0)
            {
                healthStatusDto.Entities.Add(new HealthEntityDto(
                    entityType,
                    canSendMessage,
                    string.Empty,
                    string.Empty));
                return;
            }

            string errorCode = errors.Last()?["error_code"]?.ToString() ?? string.Empty;
            string errorDescription = errors.Last()?["error_description"]?.ToString() ?? string.Empty;
            string possibleSolution = errors.Last()?["possible_solution"]?.ToString() ?? string.Empty;
            if (entityType == MetaEntityType.WABA.ToString())
            {
                if (errorCode == "141006")
                {
                    healthStatusDto.Entities.Add(new HealthEntityDto(
                        entityType,
                        MetaAccountStatus.PAYMENT_METHOD_NOT_COMPLETED.ToString(),
                        errorDescription,
                        possibleSolution));
                }
                else if (errorCode == "141014")
                {
                    healthStatusDto.Entities.Add(new HealthEntityDto(
                        entityType,
                        canSendMessage,
                        errorDescription,
                        possibleSolution));
                }
            }
            else if (entityType == MetaEntityType.BUSINESS.ToString() && errorCode == "141010")
            {
                healthStatusDto.Entities.Add(new HealthEntityDto(
                    entityType,
                    MetaAccountStatus.NOT_COMPLETED.ToString(),
                    errorDescription,
                    possibleSolution));
            }
        }
        public async Task<JObject> GetTemplateByNameAsync(string businessId, string templateName)
        {
            var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
              ?? throw new UnauthorizedAccessException("Meta account is not found.");
            var url = MetaApi.GetCreateTemplateUrl(account.WhatsAppBusinessAccountID);
            var requestUrl = $"{url}?name={templateName}";

            var request = new HttpRequestMessage(HttpMethod.Get, requestUrl)
            {
                Headers =
                {
                    Authorization = new AuthenticationHeaderValue("Bearer", account.Token)
                }
            };
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Add("Authorization", "Bearer " + account.Token);
            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"API call failed with status code {response.StatusCode}: {await response.Content.ReadAsStringAsync()}");
            }
            var contentString = await response.Content.ReadAsStringAsync();
            var jsonObject = JObject.Parse(contentString);

            try
            {
                var matchingTemplate = jsonObject["data"]?.FirstOrDefault(t => t["name"]?.ToString() == templateName);
                return matchingTemplate?.ToObject<JObject>();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<GetTemplateData>> GetAllTemplatesAsync(BusinessDetailsMeta business)
        {
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", business.Token);

            var url = MetaApi.GetMetaTemplateUrl(business.WhatsAppBusinessAccountID);
            var response = await client.GetAsync(url);

            if (response.IsSuccessStatusCode)
            {
                var templateResponse = await response.Content.ReadFromJsonAsync<GetTemplateResponse>();
                return templateResponse?.Data ?? new();
            }
            else
            {
                throw new Exception(await response.Content.ReadAsStringAsync());
            }
        }
        public async Task<(bool Success, JObject Result)> GetBusinessDetailsAsync(string businessId)
        {
            try
            {
                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
                    ?? throw new UnauthorizedAccessException("Meta account is not found.");

                var requestUrl = MetaApi.GetAccountDetailsUrl(account.WhatsAppBusinessAccountID);
                var request = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + account.Token);
                var response = await client.SendAsync(request);
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"API call failed with status code {response.StatusCode}: {await response.Content.ReadAsStringAsync()}");
                }
                var contentString = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(contentString));
                }
                return (true, JObject.Parse(contentString));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<(bool Success, JObject Result)> GetConversationAnalyticsAsync(string businessId)
        {
            try
            {
                var account = await _contextDb.BusinessDetailsMetas.FirstOrDefaultAsync(x => x.BusinessId == businessId)
                   ?? throw new UnauthorizedAccessException("Meta account is not found.");

                string baseUrl = MetaApi.GetBaseUrl();
                var nowDateTime = DateTime.UtcNow;
                var startDate = new DateTime(nowDateTime.Year, nowDateTime.Month, nowDateTime.Day, 1, 0, 0, DateTimeKind.Utc);
                var endDate = new DateTime(nowDateTime.Year, nowDateTime.Month, nowDateTime.Day, 1, 0, 0, DateTimeKind.Utc).AddDays(1);
                var startDateUnixtime = new DateTimeOffset(startDate, TimeSpan.Zero).ToUnixTimeSeconds();
                var endDateUnixtime = new DateTimeOffset(endDate, TimeSpan.Zero).ToUnixTimeSeconds();
                string url = $"{baseUrl}/{account.WhatsAppBusinessAccountID}?fields=conversation_analytics.start({startDateUnixtime}).end({endDateUnixtime}).granularity(HALF_HOUR).conversation_categories([\"MARKETING\",\"SERVICE\",\"UTILITY\",\"AUTHENTICATION\"]).dimensions([\"CONVERSATION_CATEGORY\",\"CONVERSATION_TYPE\",\"PHONE\",\"COUNTRY\"])";
                var request = new HttpRequestMessage(HttpMethod.Get, url);
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Add("Authorization", "Bearer " + account.Token);
                var response = await client.SendAsync(request);
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"API call failed with status code {response.StatusCode}: {await response.Content.ReadAsStringAsync()}");
                }
                var contentString = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(contentString));
                }
                return (true, JObject.Parse(contentString));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<(bool IsSuccess, JObject Result)> SendInteractiveMessageAsync(string businessId, string phoneNumber, string messageBody, string? headerText, InteractiveType type, List<ButtonModel>? buttons = null, ListModel? listModel = null)
        {
            try
            {
                // Get the business account details
                var account = await _contextDb.BusinessDetailsMetas
                    .FirstOrDefaultAsync(x => x.BusinessId == businessId)
                    ?? throw new UnauthorizedAccessException("Meta account is not found.");

                if (type == InteractiveType.None)
                {
                    var textPayload = new
                    {
                        messaging_product = "whatsapp",
                        recipient_type = "individual",
                        to = phoneNumber,
                        type = "text",
                        text = new
                        {
                            body = messageBody
                        }
                    };

                    // Get the API URL
                    var url1 = MetaApi.GetSendTemplateUrl(account.PhoneNumberID);

                    // Create HTTP request
                    using var client1 = new HttpClient();
                    client1.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", account.Token);

                    // Send the request
                    var content1 = new StringContent(
                        JsonConvert.SerializeObject(textPayload),
                        Encoding.UTF8,
                        "application/json"
                    );

                    var response1 = await client1.PostAsync(url1, content1);
                    var responseContent1 = await response1.Content.ReadAsStringAsync();

                    if (!response1.IsSuccessStatusCode)
                    {
                        return (false, JObject.Parse(responseContent1));
                    }

                    return (true, JObject.Parse(responseContent1));
                }

                // Construct the interactive message payload based on type
                object action;
                if (type == InteractiveType.Button)
                {
                    var buttonList = buttons?
                        .Select(b => new
                        {
                            type = "reply",
                            reply = new
                            {
                                id = b.Id,
                                title = b.Name
                            }
                        })
                        .ToList() ?? null;

                    action = new
                    {
                        buttons = buttonList
                    };
                }
                else if (type == InteractiveType.List)
                {
                    // Properly format list sections according to WhatsApp API requirements
                    var sections = new List<object>();
                    if (listModel?.Sections != null && listModel.Sections.Any())
                    {
                        // Use the sections from the ListModel
                        sections = listModel.Sections.Select(section => new
                        {
                            title = section.Title,
                            rows = section.Rows.Select(row => new
                            {
                                id = row.Id,
                                title = row.Title,
                                description = row.Description
                            }).ToList()
                        }).ToList<object>();
                    }
                    else
                    {
                        // If no sections are provided, create a default section
                        sections.Add(new
                        {
                            title = "Options",
                            rows = new List<object>() // Empty rows as fallback
                        });
                    }

                    action = new
                    {
                        button = "Select an option", // Always use a fixed, descriptive text instead of listModel?.ButtonText
                        sections = sections
                    };
                }
                else
                {
                    throw new ArgumentException($"Unsupported interactive message type: {type}");
                }

                // Construct the complete payload
                var interactivePayload = new
                {
                    messaging_product = "whatsapp",
                    recipient_type = "individual",
                    to = phoneNumber,
                    type = "interactive",
                    interactive = new
                    {
                        type = type.ToString().ToLower(),
                        header = !string.IsNullOrEmpty(headerText) ? new
                        {
                            type = "text",
                            text = headerText
                        } : null,
                        body = new
                        {
                            text = messageBody
                        },
                        action = action
                    }
                };

                // Get the API URL
                var url = MetaApi.GetSendTemplateUrl(account.PhoneNumberID);

                // Create HTTP request
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", account.Token);

                // Send the request
                var content = new StringContent(
                    JsonConvert.SerializeObject(interactivePayload),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(url, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    return (false, JObject.Parse(responseContent));
                }

                return (true, JObject.Parse(responseContent));
            }
            catch (Exception ex)
            {
                return (false, new JObject { { "error", ex.Message } });
            }
        }

    }
}
