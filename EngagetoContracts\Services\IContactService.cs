﻿using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;

namespace EngagetoContracts.Services
{
    public interface IContactService : ITransientService
    {

        Task<int> ContactImportAsync(UploadFileDto data, Guid businessId, Guid userId);
        Task<ContactImportTracker> GetContactTrackerByIdAsync(int uploadedId);
        Task<PaginatedImportTrackerDto> GetContactImportTrackersAsync(int pageNumber, int pageSize);


    }
}
