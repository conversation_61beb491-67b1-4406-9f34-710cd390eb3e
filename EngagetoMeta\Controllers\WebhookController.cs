﻿using EngagetoMeta.Dtos;
using EngagetoMeta.Repositories;
using EngagetoMeta.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Text;

namespace EngagetoMeta.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class WebhookController : ControllerBase
    {
        private readonly WebhookConfig _webhookConfig;
        private readonly MetaConfig _metaConfig;
        private readonly HttpClient _httpClient;
        private readonly ILogHistoryRepository _logger;

        public WebhookController(WebhookConfig webhookConfig, MetaConfig metaConfig, IHttpClientFactory httpClientFactory, ILogHistoryRepository logger)
        {
            _webhookConfig = webhookConfig;
            _metaConfig = metaConfig;
            _httpClient = httpClientFactory.CreateClient();
            _logger = logger;
        }


        [HttpGet("receive-WAmessage")]
        public ActionResult<string> VerifyWAWebhookAsync([FromQuery(Name = "hub.mode")] string hubMode,
                                                                   [FromQuery(Name = "hub.challenge")] int hubChallenge,
                                                                   [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
        {
            var verifyToken = _webhookConfig.VerifyToken;
            if (!hubVerifyToken.Equals(verifyToken))
            {
                return Forbid("VerifyToken doesn't match");
            }
            return Ok(hubChallenge);
        }

        [HttpPost("receive-WAmessage")]
        public async Task<IActionResult> ReceivedWAWebhookMessageAsync([FromBody] dynamic receivedMessage, CancellationToken token)
        {
            var field = "";
            string whatsaapBusinessAccId = "";
            try
            {
                // Log the received message for debugging
                Console.WriteLine(receivedMessage.ToString());

                // Deserialize the incoming message to the DTO
                var webhookDto = JsonConvert.DeserializeObject<WebhookDto>(receivedMessage.ToString());

                // Extract relevant data
                whatsaapBusinessAccId = webhookDto.Entry?[0].Id ?? string.Empty;
                var entry = webhookDto.Entry?[0];
                var change = entry?.Changes?[0];
                field = change?.Field;
                var contactDetails = change?.Value?.Contacts?[0];
                var statuses = change?.Value?.Statuses;


                var content = new StringContent(JsonConvert.SerializeObject(webhookDto), Encoding.UTF8, "application/json");
                Console.WriteLine(field);
                // Process based on field type
                switch (field)
                {
                    case "messages":
                        if (contactDetails != null)
                            FireAndForgetPostAsync($"{_metaConfig.BaseUrl}{_metaConfig.ReceiveMessage}", content);
                        else if (statuses != null)
                            FireAndForgetPostAsync($"{_metaConfig.BaseUrl}{_metaConfig.StatusUpdate}", content);
                        await _logger.SaveSuccessLogHistoryAsyn("ProcessWAWebhookMessageAsync:messages", receivedMessage,
                                null, "process webhook", whatsaapBusinessAccId, field);
                        break;
                    case "message_template_status_update":
                    case "message_template_quality_update":
                        FireAndForgetPostAsync($"{_metaConfig.BaseUrl}{_metaConfig.TemplateUpdate}", content);
                        await _logger.SaveSuccessLogHistoryAsyn("ProcessWAWebhookMessageAsync:message_template_quality_update", receivedMessage,
                                  null, "process webhook", whatsaapBusinessAccId, field);

                        break;

                    case "business_capability_update":
                    case "phone_number_quality_update":
                        FireAndForgetPostAsync($"{_metaConfig.BaseUrl}{_metaConfig.Common}", content);
                        await _logger.SaveSuccessLogHistoryAsyn("ProcessWAWebhookMessageAsync:phone_number_quality_update", receivedMessage,
                                 null, "process webhook", whatsaapBusinessAccId, field);
                        break;
                    default:
                        FireAndForgetPostAsync($"{_metaConfig.BaseUrl}{_metaConfig.Common}", content);
                        await _logger.SaveSuccessLogHistoryAsyn("ProcessWAWebhookMessageAsync", receivedMessage,
                                 null, "process webhook", whatsaapBusinessAccId, field);
                        break;
                }

                return Ok($"Message received and processing started with url {_metaConfig.BaseUrl}{_metaConfig.ReceiveMessage}");
            }
            catch (Exception ex)
            {
                await _logger.SaveErrorLogHistoryAsyn(
                $@"ProcessWAWebhookMessageAsync:{field}", receivedMessage, "Error processing webhook", ex.Message, ex.StackTrace,
                whatsaapBusinessAccId, field);
                Console.WriteLine($"Error: {ex.Message}");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing the request.");
            }
        }

        // 🔹 Fire-and-Forget API Call Helper
        private void FireAndForgetPostAsync(string url, HttpContent content)
        {
            Task.Run(async () =>
            {
                try
                {
                    var l = await _httpClient.PostAsync(url, content);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API Call Failure: {url} | Error: {ex}");
                }
            }).ContinueWith(t =>
            {
                if (t.Exception != null)
                {
                    Console.WriteLine($"Unhandled Exception: {t.Exception}");
                }
            }, TaskContinuationOptions.OnlyOnFaulted);
        }
    }
}
