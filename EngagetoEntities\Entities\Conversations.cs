﻿using EngagetoEntities.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("Conversations")]
    public class Conversations : ICloneable
    {
        [Key]

        public Guid Id { get; set; }
        public string WhatsAppMessageId { set; get; }
        public string From { set; get; }
        public string To { set; get; }
        public string? ReplyId { set; get; }
        public ConvStatus Status { set; get; }
        public DateTime CreatedAt { set; get; }
        public string? TextMessage { get; set; }
        public string? MediaFileName { get; set; }
        public string? MediaMimeType { get; set; }
        public string? MediaUrl { get; set; }
        public string? MediaCaption { set; get; }
        public MediaType? TemplateMediaType { set; get; }
        public string? TemplateMediaUrl { get; set; }
        public string? TemplateHeader { get; set; }
        public string? TemplateBody { get; set; }
        public string? TemplateFooter { get; set; }
        public string? CallButtonName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? UrlButtonNames { get; set; }
        public string? RedirectUrls { get; set; }
        public string? QuickReplies { get; set; }
        public string? Action { get; set; }
        public MessageType? MessageType { get; set; }
        public string? ReferenceId { get; set; }
        public string? UserId { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorDetails { get; set; }
        public object Clone()
        {
            return this.MemberwiseClone();
        }
        public string? CarouselCards { get; set; }
    }
}
