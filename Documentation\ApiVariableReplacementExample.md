# API Variable Replacement - Implementation Example

## Overview
This feature follows the **exact same pattern** as your existing `ProcessTemplateWithLeadratVariable` method, ensuring consistency with your current architecture.

## Pattern Comparison

### Your Existing Template Pattern:
```csharp
private async Task<(List<TemplateParameterDto> bodyParameters, List<TemplateParameterDto> headerParameters)>
    ProcessTemplateWithLeadratVariable(ViewWATemplatesDto template, CreateWAMessageDto waMessage, 
    ViewCampaignDto campaignDto, IEnumerable<string> bodyVariables, IEnumerable<string> headerVariables, ViewCustomerDto customer)
{
    // 1. Extract variables from template
    var bodyVariables = RegexHelper.ReplaceAndExtractVariables(template.Body).Variables;
    
    // 2. Try API call first
    var response = await httpClient.GetAsync(LeadratApiUrls.GetLeadApiUrl(customer.PhoneNumber, customer.CountryCode));
    if (response.IsSuccessStatusCode)
    {
        // Use API data
        var jsonResponse = JsonConvert.DeserializeObject<JObject>(jsonString);
        var (bodyValues, message) = ProcessTemplateVariables(bodyVariables, ObjectHelper.ConvertJObjectToDictionary(jsonResponse), template.Body);
    }
    else
    {
        // Fallback to contact properties + fallback values
        var customerProperties = typeof(ViewCustomerDto).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(customer)?.ToString() ?? string.Empty);
        var bodyValuesDict = campaignDto.MessageTemplateInfo?.BodyValues?.ToDictionary(
            x => x.Variable,
            x => customerProperties.ContainsKey(x.Value) && !string.IsNullOrEmpty(customerProperties[x.Value])
                ? customerProperties[x.Value] : x.FallbackValue
        );
    }
}
```

### New Interactive Message Pattern:
```csharp
private async Task<string> ProcessInteractiveMessageWithApiVariablesAsync(string messageBody, 
    List<ApiVariableModel> apiVariables, ApiVariableSourceModel apiSource, Contacts contact)
{
    // 1. Extract placeholders from message body (similar to bodyVariables extraction)
    var placeholders = ExtractPlaceholdersFromMessage(messageBody);
    
    // 2. Try API call first (primary data source) - similar to your Leadrat API call
    var response = await httpClient.GetAsync(apiSource.ApiUrl);
    if (response.IsSuccessStatusCode)
    {
        // API call successful - use API data (similar to your successful response handling)
        var jsonResponse = JsonConvert.DeserializeObject<JObject>(jsonString);
        return ProcessMessageWithApiData(messageBody, apiVariables, jsonResponse);
    }
    else
    {
        // API call failed - fallback to local data (similar to your else block)
        return ProcessMessageWithFallbackData(messageBody, apiVariables, contact);
    }
}
```

## Complete Usage Example

### 1. Interactive Message Configuration
```json
{
  "Type": "Button",
  "Body": "Hi #Name#, your account balance is #Balance# and your next appointment is #NextAppointment#.",
  "Header": "Account Update",
  "Footer": "Thank you for choosing us!",
  "Buttons": [
    { "Id": "1", "Name": "View Details" },
    { "Id": "2", "Name": "Contact Support" }
  ],
  
  // NEW API VARIABLE FEATURE
  "EnableApiVariables": true,
  "ApiSource": {
    "ApiUrl": "https://your-api.com/customer-data/{phoneNumber}",
    "Method": "GET",
    "Headers": {
      "Authorization": "Bearer your-token",
      "tenant": "your-tenant-id"
    },
    "TimeoutSeconds": 30
  },
  "ApiVariables": [
    {
      "Placeholder": "#Name#",
      "ApiKey": "customer.name",
      "FallbackValue": "Valued Customer",
      "DataType": "string"
    },
    {
      "Placeholder": "#Balance#",
      "ApiKey": "account.balance",
      "FallbackValue": "0.00",
      "DataType": "currency"
    },
    {
      "Placeholder": "#NextAppointment#",
      "ApiKey": "appointments.next.date",
      "FallbackValue": "Not scheduled",
      "DataType": "date",
      "Format": "dd-MM-yyyy"
    }
  ]
}
```

### 2. API Response Example
```json
{
  "customer": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "account": {
    "balance": 1250.75,
    "currency": "USD"
  },
  "appointments": {
    "next": {
      "date": "2024-01-15T10:00:00Z",
      "type": "consultation"
    }
  }
}
```

### 3. Processing Flow

#### Step 1: API Call Successful
```
Input:  "Hi #Name#, your account balance is #Balance# and your next appointment is #NextAppointment#."
Output: "Hi John Doe, your account balance is $1,250.75 and your next appointment is 15-01-2024."
```

#### Step 2: API Call Failed (Fallback)
```csharp
// Falls back to contact properties + fallback values (just like your template processing)
var contactProperties = typeof(Contacts).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(contact)?.ToString() ?? string.Empty);

// If contact.Name exists, use it; otherwise use fallback
var name = contactProperties.ContainsKey("Name") && !string.IsNullOrEmpty(contactProperties["Name"]) 
    ? contactProperties["Name"] 
    : "Valued Customer";

Output: "Hi Akash, your account balance is 0.00 and your next appointment is Not scheduled."
```

### 4. Integration in Workflow
```csharp
// In ProcessInteractiveMessageNodeAsync method
string bodyMessage;
if (interactiveMessage.EnableApiVariables && interactiveMessage.ApiVariables != null && interactiveMessage.ApiSource != null)
{
    // Use new API variable replacement (similar to ProcessTemplateWithLeadratVariable)
    bodyMessage = await ReplaceVariablesWithApiSupportAsync(
        interactiveMessage.Body, 
        interactiveMessage.Variables,      // Existing variables (unchanged)
        interactiveMessage.ApiVariables,   // New API variables
        interactiveMessage.ApiSource,      // API configuration
        contact);
}
else
{
    // Use existing method (unchanged behavior)
    bodyMessage = ReplaceVariablesInBody(interactiveMessage.Body, interactiveMessage.Variables, contact);
}
```

## Key Benefits

1. **Consistent Pattern**: Follows your existing template processing architecture
2. **Non-Intrusive**: Existing workflows continue to work unchanged
3. **Fallback Logic**: Same robust fallback pattern as your templates
4. **Error Handling**: Comprehensive error handling with logging
5. **Performance**: Optional caching support for API responses

## Migration Path

1. **Phase 1**: Deploy with `EnableApiVariables: false` (default)
2. **Phase 2**: Test with specific workflows
3. **Phase 3**: Gradually enable for production workflows

This implementation ensures your interactive messages have the same powerful dynamic content capabilities as your templates, while maintaining full backward compatibility!
