﻿using Amazon.Runtime.Internal.Transform;
using EngagetoContracts.UserContracts;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace EngagetoRepository.UserRepository
{
    public class TokenService : ITokenService
    {
        private readonly IConfiguration _configuration;
        private readonly IGenericRepository _genericRepository;

        public TokenService(IConfiguration configuration, IGenericRepository genericRepository)
        {
            _configuration = configuration;
            _genericRepository = genericRepository;
        }

        public string GenerateToken(Ahex_CRM_Users user)
        {
            var claims = new[]
            {
                new Claim(ClaimTypes.Name, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim("UserName",user.Name),
                new Claim("BusinessId",user.CompanyId),
                new Claim("Email",user.EmailAddress ?? string.Empty),
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                /* issuer: _configuration["Jwt:Issuer"],
                 audience: _configuration["Jwt:Audience"],*/
                issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                audience: "https://loginAPI/api",
                claims: claims,
                expires: DateTime.UtcNow.AddHours(2),
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        public async Task<string> GenerateTokenForTenant(string tenantId)
        {
            try
            {
                string roleId = "55aa1ba5-1507-47e2-888b-89d80cd41906";
                var businessDetail = (await _genericRepository.GetByObjectAsync<Ahex_CRM_BusinessDetails>(new Dictionary<string, object> { { "TenantId", tenantId } }))?.FirstOrDefault();
                if (businessDetail != null)
                {
                    var userDetail = (await _genericRepository.GetByObjectAsync<Ahex_CRM_Users>(new Dictionary<string, object> { { "CompanyId", businessDetail.Id.ToString() }, { "RoleId", roleId } }))?.FirstOrDefault();
                    if (userDetail != null)
                    {
                        var claims = new[]
                        {
                            new Claim(ClaimTypes.Name, userDetail.Id.ToString()),
                            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                            new Claim("UserName",userDetail.Name),
                            new Claim("BusinessId",userDetail.CompanyId),
                            new Claim("Email",userDetail.EmailAddress ?? string.Empty),
                        };

                        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
                        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

                        var token = new JwtSecurityToken(
                            /* issuer: _configuration["Jwt:Issuer"],
                                audience: _configuration["Jwt:Audience"],*/
                            issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                            audience: "https://loginAPI/api",
                            claims: claims,
                            expires: DateTime.UtcNow.AddHours(2),
                            signingCredentials: creds
                        );
                        return new JwtSecurityTokenHandler().WriteToken(token);
                    }
                }
                throw new Exception("Not valid tenant");
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /* public string GenerateToken1(AccountDetails user)
         {
             var claims = new[]
             {
             new Claim(ClaimTypes.Name, user.Id.ToString()),
             new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),

         };

             var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
             var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

             var token = new JwtSecurityToken(
                *//* issuer: _configuration["Jwt:Issuer"],
                 audience: _configuration["Jwt:Audience"],*//*
                issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                 audience: "https://loginAPI/api",
                 claims: claims,
                 expires: DateTime.UtcNow.AddHours(1),
                 signingCredentials: creds
             );

             return new JwtSecurityTokenHandler().WriteToken(token);
         }
         public string GenerateToken2(ClientsAccountDetails user)
         {
             var claims = new[]
             {
             new Claim(ClaimTypes.Name, user.Id.ToString()),
             new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),

         };

             var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"));
             var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

             var token = new JwtSecurityToken(
                *//* issuer: _configuration["Jwt:Issuer"],
                 audience: _configuration["Jwt:Audience"],*//*
                issuer: "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                 audience: "https://loginAPI/api",
                 claims: claims,
                 expires: DateTime.UtcNow.AddHours(1),
                 signingCredentials: creds
             );

             return new JwtSecurityTokenHandler().WriteToken(token);
         }*/
    }
}
