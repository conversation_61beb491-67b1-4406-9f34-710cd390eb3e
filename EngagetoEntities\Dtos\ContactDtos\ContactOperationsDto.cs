﻿namespace EngagetoEntities.Dtos.ContactDtos
{

    public class ContactOperationsDto
    {
        public ContactSearch? Searching { get; set; }
        public ContactSort? Sorting { get; set; }
        public ContactFilterGroup? Filtering { get; set; }
    }
    public class ContactSearch
    {
        public string? Value { get; set; }
    }

    public class ContactSort
    {
        public string? Column { get; set; }
        public string? Order { get; set; }
    }

    public class ContactFilterGroup
    {
        public string FilterType { get; set; } = default!;
        public List<ContactFilterCondition> Conditions { get; set; } = default!;
    }

    public class ContactFilterCondition
    {
        public string Column { get; set; } = default!;
        public string Operator { get; set; } = default!;
        public string[] Value { get; set; } = default!;
    }
}
