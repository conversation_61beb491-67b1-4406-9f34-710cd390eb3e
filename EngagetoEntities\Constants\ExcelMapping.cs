﻿using OfficeOpenXml;

public static class ExcelMapping
{
    // Example Column Mapping
    public static Dictionary<string, List<string>> ContactColumnMapping = new Dictionary<string, List<string>>()
    {
        {"Name", new List<string> { "Name", "name" } },
        {"CountryCode", new List<string> { "CountryCode", "Country Code", "country code", "countryCode", "countrycode" } },
        {"Contact", new List<string> { "contact", "Contact", "PhoneNumber", "phoneNumber", "phone number" } },
        {"Email", new List<string> { "email", "Email" } },
        {"CountryName", new List<string> { "CountryName", "Country Name" } },
        {"Tags", new List<string> { "tags","tag" } }
    };


    public static bool ValidateHeaders(ExcelWorksheet worksheet)
    {
        var headers = new List<string>();
        int totalCols = worksheet.Dimension.End.Column;

        for (int col = 1; col <= totalCols; col++)
        {
            headers.Add(worksheet.Cells[1, col].Text.Trim());
        }

        foreach (var kvp in ContactColumnMapping)
        {
            bool matchFound = headers.Any(header =>
                kvp.Value.Any(variant => string.Equals(header, variant, StringComparison.OrdinalIgnoreCase)));

            if (!matchFound)
            {
                return false;
            }
        }

        return true;
    }


}
