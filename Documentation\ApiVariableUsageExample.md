# API Variable Replacement - Complete Usage Guide

## Overview
This feature follows your **exact existing pattern** using `StringHelper.ReplaceAndExtractVariables` and `VariableModelDto` structure.

## How to Use

### 1. Interactive Message Configuration
```json
{
  "Type": "Button",
  "Body": "Hi {{Name}}, your account balance is {{Balance}} and your next appointment is {{NextAppointment}}.",
  "Header": "Account Update", 
  "Footer": "Thank you for choosing us!",
  "Buttons": [
    { "Id": "1", "Name": "View Details" },
    { "Id": "2", "Name": "Contact Support" }
  ],
  
  // Existing variables (unchanged)
  "Variables": [
    {
      "Variable": "{{CompanyName}}",
      "Value": "Name", // Contact property
      "FallbackValue": "Our Company"
    }
  ],
  
  // NEW API VARIABLE FEATURE
  "EnableApiVariables": true,
  "ApiVariables": [
    {
      "Variable": "{{Name}}",
      "Value": "customer.name",        // API response key
      "FallbackValue": "Valued Customer"
    },
    {
      "Variable": "{{Balance}}",
      "Value": "account.balance",      // API response key
      "FallbackValue": "0.00"
    },
    {
      "Variable": "{{NextAppointment}}",
      "Value": "appointments.next.date", // API response key
      "FallbackValue": "Not scheduled"
    }
  ]
}
```

### 2. Processing Flow (Same as Your Template Pattern)

#### Step 1: Extract Variables
```csharp
// Uses your existing StringHelper method
var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);
// extractedVariables = ["{{Name}}", "{{Balance}}", "{{NextAppointment}}"]
```

#### Step 2: Try API Call First
```csharp
// Similar to your Leadrat API call
var response = await httpClient.GetAsync(BuildApiUrl(contact));
if (response.IsSuccessStatusCode)
{
    // Use API data (success path)
    var jsonResponse = JsonConvert.DeserializeObject<JObject>(jsonString);
    return ProcessMessageWithApiData(messageBody, variables, jsonResponse, extractedVariables);
}
else
{
    // Fallback to contact properties + fallback values (failure path)
    return ProcessMessageWithFallbackData(messageBody, variables, contact);
}
```

#### Step 3: API Success - Use API Data
```csharp
// Similar to your successful API response handling
var apiDataDict = ObjectHelper.ConvertJObjectToDictionary(jsonResponse);
var variableValuesDict = new Dictionary<string, string>();

foreach (var variable in variables)
{
    if (extractedVariables.Contains(variable.Variable))
    {
        // Extract value from API response using the Value as key
        var value = GetValueFromApiData(apiDataDict, variable.Value) ?? variable.FallbackValue;
        variableValuesDict[variable.Variable] = value;
    }
}

// Replace placeholders using your existing RegexHelper
return RegexHelper.ReplacePlaceholders(messageBody, variableValuesDict);
```

#### Step 4: API Failure - Use Fallback Data
```csharp
// Similar to your else block with contact properties
var contactProperties = typeof(Contacts).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(contact)?.ToString() ?? string.Empty);

var variableValuesDict = variables?.ToDictionary(
    x => x.Variable,
    x => contactProperties.ContainsKey(x.Value) && !string.IsNullOrEmpty(contactProperties[x.Value])
        ? contactProperties[x.Value]
        : x.FallbackValue
) ?? new Dictionary<string, string>();

return RegexHelper.ReplacePlaceholders(messageBody, variableValuesDict);
```

### 3. Expected API Response
```json
{
  "customer": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "account": {
    "balance": 1250.75
  },
  "appointments": {
    "next": {
      "date": "2024-01-15T10:00:00Z"
    }
  }
}
```

### 4. Processing Results

#### API Success:
```
Input:  "Hi {{Name}}, your account balance is {{Balance}} and your next appointment is {{NextAppointment}}."
Output: "Hi John Doe, your account balance is 1250.75 and your next appointment is 2024-01-15T10:00:00Z."
```

#### API Failure (Fallback):
```
Input:  "Hi {{Name}}, your account balance is {{Balance}} and your next appointment is {{NextAppointment}}."
Output: "Hi Valued Customer, your account balance is 0.00 and your next appointment is Not scheduled."
```

### 5. Customization Points

#### API URL Builder
```csharp
private string BuildApiUrl(Contacts contact)
{
    // Customize this method based on your API structure
    var phoneNumber = $"{contact.CountryCode}{contact.Contact}";
    return $"https://your-api.com/customer-data/{phoneNumber}";
    
    // Or use your existing pattern:
    // return LeadratApiUrls.GetLeadApiUrl(contact.Contact, contact.CountryCode);
}
```

#### Add Tenant Header (if needed)
```csharp
// In ProcessInteractiveMessageWithApiVariablesAsync method
httpClient.DefaultRequestHeaders.Add("tenant", _tenantInfo.Id);
```

### 6. Integration Example

```csharp
// In your workflow node configuration
var interactiveMessage = new InteractiveMessageDto
{
    Type = InteractiveType.Button,
    Body = "Hi {{Name}}, your balance is {{Balance}}.",
    Buttons = new List<ButtonDto>
    {
        new() { Id = "1", Name = "View Details" },
        new() { Id = "2", Name = "Contact Support" }
    },
    
    // Enable API variables
    EnableApiVariables = true,
    ApiVariables = new List<VariableModelDto>
    {
        new() { Variable = "{{Name}}", Value = "customer.name", FallbackValue = "Customer" },
        new() { Variable = "{{Balance}}", Value = "account.balance", FallbackValue = "0.00" }
    }
};
```

### 7. Benefits

1. **✅ Consistent Pattern**: Uses your existing `StringHelper.ReplaceAndExtractVariables`
2. **✅ Same Structure**: Uses existing `VariableModelDto` (no new models needed)
3. **✅ Familiar Flow**: Same API-first, fallback-second pattern as templates
4. **✅ Non-Intrusive**: Existing workflows continue unchanged
5. **✅ Easy Migration**: Just add `EnableApiVariables: true` and `ApiVariables` array

This implementation ensures your interactive messages have the same powerful dynamic content capabilities as your templates, using the exact same patterns and helper methods!
