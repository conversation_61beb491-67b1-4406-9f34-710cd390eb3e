﻿using EngagetoDapper.Data.Connections;
using EngagetoDapper.Data.Interfaces.AutomationInterfaces;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Utilities;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using Mapster;
using Newtonsoft.Json;

namespace EngagetoDapper.Data.Dapper.Services.AutomationServices
{
    public class AutomationWorkflowService : IAutomationWorkflowService
    {
        private readonly IGenericRepository _genericRespostry;
        private readonly IDapperConnectionFactory _connectionFactory;
        private readonly IAutoReplyAutomationRepostry _autoReplyAutomationRepostry;
        private readonly ApplicationDbContext _automationDbContext;
        
        public AutomationWorkflowService(IGenericRepository genericRespostry,
            IAutoReplyAutomationRepostry autoReplyAutomationRepostry,
            ApplicationDbContext automationDbContext,
            IDapperConnectionFactory connectionFactory)
        {
            _autoReplyAutomationRepostry = autoReplyAutomationRepostry;
            _genericRespostry = genericRespostry;
            _connectionFactory = connectionFactory;
            _automationDbContext = automationDbContext;
        }

        public async Task<Guid> SaveAutomationWorkFlowAsync(AutomationWorkflowDto automationWorkflowDto, bool isNew)
        {
            try
            {
                _connectionFactory.UnitOfWork.Begin();
                if (string.IsNullOrEmpty(automationWorkflowDto.WorkflowName))
                    automationWorkflowDto.WorkflowName = $"Untitled Workflow {AutoGenerateNumber(100000000, 999999999)}";


                var workflow = await _genericRespostry.GetByNameAsync<WorkflowEntity>(new Dictionary<string, string>()
                        {
                            {
                               automationWorkflowDto.WorkflowName, "WorkflowName"
                            }
                        });
                if (isNew && workflow.Count > 0)
                    throw new Exception("A workflow name already exists with this name.");

                var workflowEntity = CreateWorkflowEntity(automationWorkflowDto);
                workflowEntity.Step = 1;

                if (workflow.Any())
                    workflowEntity.Step = (workflow.OrderByDescending(x => x.Step)?.FirstOrDefault()?.Step ?? 0) + 1;

                int insertedWorkflow = await _genericRespostry.SaveAsync(workflowEntity);

                if (insertedWorkflow > 0 && automationWorkflowDto?.ResponseCustomerMessage?.VeriableNameEntityId != null)
                {
                    var workflowCustomerResponse = CreateWorkflowCustomerResponse(workflowEntity);
                    workflowCustomerResponse.VeriableNameEntityId = automationWorkflowDto?.ResponseCustomerMessage?.VeriableNameEntityId;
                    int insertedWorkflowCustomerResponse = await _genericRespostry.SaveAsync(workflowCustomerResponse);
                }
                if (automationWorkflowDto.Veriables?.Any() ?? false)
                {
                    var veriables = CreateVeriableEntities(automationWorkflowDto, workflowEntity);
                    if (veriables.Any())
                    {
                        var result = await _autoReplyAutomationRepostry.SaveVeriablesAsync(veriables);
                    }
                }
                _connectionFactory.UnitOfWork.Commit();
                return workflowEntity.Id;
            }
            catch (Exception)
            {
                _connectionFactory.UnitOfWork.Rollback();
                throw;
            }
        }

        public async Task<Dictionary<string, List<AutomationWorkflowResponseDto>>> GetWorkflowAsync(Guid companyId, Guid? userId = null, Guid? workflowId = null, string? workflowName = null)
        {
            //List<AutomationWorkflowResponseDto> automationWorkflowResponses = new List<AutomationWorkflowResponseDto>();
            Dictionary<string, List<AutomationWorkflowResponseDto>> automationWorkflowResponses = new Dictionary<string, List<AutomationWorkflowResponseDto>>();
            try
            {
                var workflowResult = await _autoReplyAutomationRepostry.GetWorkflowAsync<WorkflowResultDto>(companyId, userId, workflowId, workflowName);
                if (workflowResult != null && workflowResult.Any())
                {
                    var workflowResultsGroups = workflowResult
                        .OrderBy(x => x.Step)
                        .GroupBy(x => x.WorkflowName)
                        .Select(workflowGroup => new
                        {
                            WorkflowName = workflowGroup.Key,
                            WorkflowResults = workflowGroup.GroupBy(x => x.Id)
                        });

                    foreach (var workflowGroup in workflowResultsGroups)
                    {
                        var automationWorkflowResult = new List<AutomationWorkflowResponseDto>();
                        foreach (var workflow in workflowGroup.WorkflowResults)
                        {
                            var firstWorkflow = workflow.FirstOrDefault();
                            if (firstWorkflow == null) continue;

                            var workflowDto = firstWorkflow.Adapt<AutomationWorkflowResponseDto>();
                            var List = _automationDbContext.InputListEntities
                                .Where(m => m.Id == firstWorkflow.WorkflowListId && m.CompanyId == firstWorkflow.CompanyId);
                            if ((firstWorkflow.FlowResponseType == WorkflowResponseType.List) && List.Any())
                            {
                                workflowDto.AutoReplyWorkflowList =
                                 firstWorkflow.Adapt<WorkflowListDto>();
                            }


                            workflowDto.ResponseCustomerMessage = firstWorkflow.Adapt<AutoReplyWorkflowCustomerResponseDto>();

                            workflowDto.Veriables = firstWorkflow.VeriableId != null
                                ? workflow.Select(veriable => new VeriableDto
                                {
                                    Id = veriable.VeriableId,
                                    Veriable = veriable.Veriable,
                                    ReferenceId = veriable.Id,
                                    FallbackValue = veriable.FallbackValue,
                                    Value = veriable.Value,
                                    Type = veriable.Type,
                                    Index = veriable.Index
                                })
                                    .OrderBy(m => m.Type)    // First order by Type
                                    .ThenBy(m => m.Index)    // Then order by Index
                                    .ToList()
                                    : null;
                            automationWorkflowResult.Add(workflowDto);

                            //automationWorkflowResult.Add(new AutomationWorkflowResponseDto
                            //{
                            //    CompanyId = firstWorkflow.CompanyId,
                            //    UserId = firstWorkflow.UserId,
                            //    Title = firstWorkflow.Title,
                            //    WorkflowName = workflowGroup.WorkflowName,
                            //    FlowResponseType = firstWorkflow.FlowResponseType,
                            //    AutoReplyWorkflowButtons = firstWorkflow.FlowResponseType == WorkflowResponseType.Button
                            //        ? JsonConvert.DeserializeObject<List<string>>(firstWorkflow.WorkflowButtons)
                            //        : null,
                            //    AutoReplyWorkflowList = firstWorkflow.FlowResponseType == WorkflowResponseType.List
                            //        ? new WorkflowListDto
                            //        {
                            //            Id = firstWorkflow.WorkflowListId,
                            //            ButtonName = firstWorkflow.WorkflowButtons,
                            //            CompanyId = firstWorkflow.CompanyId,
                            //            UserId = firstWorkflow.UserId,
                            //            ListName = firstWorkflow.ListName,
                            //            Inputs = JsonConvert.DeserializeObject<List<Inputs>>(firstWorkflow.Inputs)
                            //        }
                            //        : null,
                            //    Veriables = firstWorkflow.VeriableId != null
                            //        ? workflow.Select(veriable => new VeriableDto
                            //        {
                            //            Id = veriable.VeriableId,
                            //            ReferenceId = veriable.Id,
                            //            FallbackValue = veriable.FallbackValue,
                            //            Value = veriable.Value
                            //        }).ToList()
                            //        : null,
                            //    ResponseCustomerMessage = new AutoReplyWorkflowCustomerResponseDto
                            //    {
                            //        ResponseMessage = firstWorkflow.ResponseMessage,
                            //        VeriableName = new VeriableNameDto
                            //        {
                            //            CompanyId = firstWorkflow.CompanyId,
                            //            UserId = firstWorkflow.UserId,
                            //            VeriableName = firstWorkflow.VeriableName
                            //        }
                            //    }
                            //});
                        }
                        automationWorkflowResponses.Add(workflowGroup.WorkflowName, automationWorkflowResult);
                    }

                }
                return automationWorkflowResponses;

            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<bool> UpdateWorkflowAsync(AutomationWorkflowDto automationWorkflowResponse)
        {
            try
            {
                _connectionFactory.UnitOfWork.Begin();
                if (automationWorkflowResponse.Id == null)
                    throw new Exception("workflow id can not null");

                Dictionary<Guid, string> filters = new Dictionary<Guid, string>();
                filters.Add(automationWorkflowResponse.CompanyId, "CompanyId");
                filters.Add(automationWorkflowResponse.Id ?? Guid.Empty, "ReferenceId");
                var veriablesResult = await _genericRespostry.GetByGuidIdAsync<VeriableEntity>(filters);
                foreach (var variable in veriablesResult.Where(x => !x.IsDeleted))
                {
                    variable.IsDeleted = true;
                    var result = await _genericRespostry.UpdateRecordAsync<VeriableEntity>(variable, new Dictionary<string, object>() { { "Id", variable.Id } });
                }
                var workflowCustomerResponseFilter = filters;

                var workflowCustomerResult = await _genericRespostry.GetByGuidIdAsync<AutomationCustomerResponseEntity>(workflowCustomerResponseFilter);
                if (workflowCustomerResult.Any())
                {
                    foreach (var workflowCustomer in workflowCustomerResult)
                    {
                        workflowCustomer.VeriableNameEntityId = automationWorkflowResponse.ResponseCustomerMessage.VeriableNameEntityId;
                        workflowCustomer.UpdatedBy = automationWorkflowResponse.UserId;
                        var result = await _genericRespostry.UpdateRecordAsync(workflowCustomer, new Dictionary<string, object>() { { "Id", workflowCustomer.Id } });
                    }
                }


                var workflowEntities = await _genericRespostry
                    .GetByGuidIdAsync<WorkflowEntity>(new Dictionary<Guid, string>() { { automationWorkflowResponse.Id ?? Guid.Empty, "Id" } });
                
                if (!workflowEntities.Any())
                    throw new Exception("Workflow not found with Id, somthing issue is there. please connect with team");
                
                var workflowEntity = workflowEntities.First();
                workflowEntity.FlowResponseType = automationWorkflowResponse.FlowResponseType;
                workflowEntity.UpdatedBy = automationWorkflowResponse.UserId ?? Guid.Empty;
                workflowEntity.UpdatedAt = DateTime.UtcNow;
                workflowEntity.WorkflowName = automationWorkflowResponse.WorkflowName;
                workflowEntity.Title = automationWorkflowResponse.Title;
                workflowEntity.WorkflowButtons = JsonConvert.SerializeObject(automationWorkflowResponse.AutoReplyWorkflowButtons);
                workflowEntity.WorkflowListId = automationWorkflowResponse.WorkflowListId;
                workflowEntity.DefaultErrorResponse = automationWorkflowResponse.DefaultErrorResponse;
                workflowEntity.WebhookTriggerUrl = automationWorkflowResponse.WebhookTriggerUrl;
                workflowEntity.WebhookTriggerBody = automationWorkflowResponse.WebhookTriggerBody;
                workflowEntity.WebhookTriggerHttpMethod = automationWorkflowResponse.WebhookTriggerHttpMethod;
                workflowEntity.WebhookTriggerHeader = JsonConvert.SerializeObject(automationWorkflowResponse.WebhookTriggerHeader);
                workflowEntity.StepType = automationWorkflowResponse.StepType;

                var workflowresult = await _genericRespostry.UpdateRecordAsync(workflowEntity, new Dictionary<string, object>() { { "Id", workflowEntity.Id } });
                var veriables = CreateVeriableEntities(automationWorkflowResponse, workflowEntity);
                if (veriables != null && veriables.Any())
                {
                    await _autoReplyAutomationRepostry.SaveVeriablesAsync(veriables);
                }
                if (workflowCustomerResult.Count == 0)
                {
                    if (automationWorkflowResponse?.ResponseCustomerMessage?.VeriableNameEntityId != null)
                    {
                        var workflowCustomerResponse = CreateWorkflowCustomerResponse(workflowEntity);
                        workflowCustomerResponse.VeriableNameEntityId = automationWorkflowResponse?.ResponseCustomerMessage?.VeriableNameEntityId;
                        int insertedWorkflowCustomerResponse = await _genericRespostry.SaveAsync(workflowCustomerResponse);

                    }

                }
                _connectionFactory.UnitOfWork.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _connectionFactory.UnitOfWork.Rollback();
                throw;
            }
        }
        public async Task<List<WorkflowNamesDto>> GetWorkflowNamesAsync(Guid companyId)
        {
            var result = await _autoReplyAutomationRepostry
                .GetWorkflowNamesAsync<WorkflowNamesDto>(companyId);
            return result?.ToList() ?? new List<WorkflowNamesDto>();
        }
        public async Task<bool> DeleteWorkflowAsync(Guid companyId, Guid userId, Guid? workflowId, string? workflowName)
        {
            try
            {
                var result = await _autoReplyAutomationRepostry
                    .SoftDeleteAutoReplyWorkflowAsync(companyId, userId, workflowId, workflowName);
                return result > 0;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> UpdateWorkflowNameAsync(WorkflowUpdatePayloadDto workflowUpdatePayload, Guid userId)
        {
            try
            {
                var existingWorkflows = await _genericRespostry.GetRecordByRequestFilter<WorkflowEntity>(
                    new List<Dtos.RequestFilterDto>
                    {
                        new Dtos.RequestFilterDto("CompanyId", workflowUpdatePayload.CompanyId, "="),
                        new Dtos.RequestFilterDto("WorkflowName", new List<string>
                            {
                                workflowUpdatePayload.OldWorkflowName,
                                workflowUpdatePayload.NewWorkflowName
                            },
                            "in")
                    });

                if (!existingWorkflows.Any())
                {
                    return await _autoReplyAutomationRepostry.UpdateWorkflowNameAsync(workflowUpdatePayload, userId);
                }
                else
                {
                    if (!existingWorkflows.Any(x => x.WorkflowName == workflowUpdatePayload.OldWorkflowName))
                        throw new Exception("No workflow found with the old name.");

                    if (existingWorkflows.Any(x => x.WorkflowName == workflowUpdatePayload.NewWorkflowName))
                        throw new Exception("The new workflow name is already in use.");
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }


        #region Helper
        private WorkflowEntity CreateWorkflowEntity(AutomationWorkflowDto automationWorkflowDto)
        {
            var workflowEntity = automationWorkflowDto.Adapt<WorkflowEntity>();
            workflowEntity.Id = Guid.NewGuid();

            workflowEntity.CreatedBy = automationWorkflowDto.UserId;
            workflowEntity.UpdatedBy = automationWorkflowDto.UserId;
            workflowEntity.CreatedAt = DateTime.Now;

            switch (workflowEntity.FlowResponseType)
            {
                case WorkflowResponseType.None:
                    workflowEntity.WorkflowListId = null;
                    workflowEntity.WorkflowButtons = null;
                    break;
                case WorkflowResponseType.Button:
                    if (string.IsNullOrEmpty(workflowEntity.WorkflowButtons))
                        throw new Exception("Workflow buttons can't be null");
                    workflowEntity.WorkflowListId = null;
                    break;
                case WorkflowResponseType.List:
                    if (workflowEntity.WorkflowListId == null)
                        throw new Exception("WorkflowListId can't be null");
                    workflowEntity.WorkflowButtons = null;
                    break;
            }
            return workflowEntity;
        }

        private List<VeriableEntity> CreateVeriableEntities(AutomationWorkflowDto automationWorkflowDto, WorkflowEntity workflowEntity)
        {
            var veriables = automationWorkflowDto.Veriables.Adapt<List<VeriableEntity>>();
            if (veriables != null && veriables.Any())
            {


                foreach (var veriable in veriables)
                {
                    veriable.Id = Guid.NewGuid();
                    veriable.ReferenceId = workflowEntity.Id;
                    veriable.CompanyId = workflowEntity.CompanyId;
                    veriable.UserId = workflowEntity.UserId ?? Guid.Empty;
                    veriable.CreatedAt = DateTime.Now;
                    veriable.CreatedBy = workflowEntity.UserId;
                    veriable.UpdatedAt = DateTime.Now;
                    veriable.UpdatedBy = workflowEntity.UserId;
                    veriable.IsDeleted = false;
                    veriable.ReferenceTableType = ReferenceTableType.Workflow;
                }
            }
            return veriables;
        }

        private AutomationCustomerResponseEntity CreateWorkflowCustomerResponse(WorkflowEntity workflowEntity)
        {
            return new AutomationCustomerResponseEntity()
            {
                Id = Guid.NewGuid(),
                CompanyId = workflowEntity.CompanyId,
                UserId = workflowEntity.UserId ?? Guid.Empty,
                CreatedAt = DateTime.Now,
                CreatedBy = workflowEntity.UserId,
                UpdatedAt = DateTime.Now,
                UpdatedBy = workflowEntity.UserId,
                IsDeleted = false,
                ReferenceId = workflowEntity.Id,
                ReferenceTableType = ReferenceTableType.Workflow,
            };
        }
        private int AutoGenerateNumber(int minValue, int maxValue)
        {
            Random random = new Random();
            return random.Next(minValue, maxValue);
        }


        #endregion

    }
}
